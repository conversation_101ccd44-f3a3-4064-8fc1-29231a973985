import { NextResponse } from 'next/server'
import { getPrecio<PERSON> } from '@/app/actions/precios'
import { TipoProducto } from '@/app/types/productos'

export async function GET(request: Request) {
  const url = new URL(request.url)
  const terminal = url.searchParams.get('terminal') || ''
  const start = url.searchParams.get('start') || ''
  const end = url.searchParams.get('end') || ''
  // Por simplicidad usamos siempre Diésel
  const producto = TipoProducto.DIESEL

  if (!terminal || !start || !end) {
    return NextResponse.json({ success: false, error: 'Faltan parámetros' }, { status: 400 })
  }

  try {
    // Agregar valores predeterminados para page y pageSize
    const result = await getPrecios({ producto, terminal, start, end, page: 1, pageSize: 100 })
    return NextResponse.json(result)
  } catch (err: any) {
    return NextResponse.json({ success: false, error: err.message }, { status: 500 })
  }
}
