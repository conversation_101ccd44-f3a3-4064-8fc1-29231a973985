import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import { z } from "zod";

// Esquema de validación para el lead
const leadSchema = z.object({
  email: z.string().email("Correo electrónico inválido"),
  source: z.string().optional(),
});

// Límites para prevenir spam
const MAX_ATTEMPTS = 5; // Máximo número de intentos por correo
const ATTEMPT_WINDOW = 24 * 60 * 60 * 1000; // 24 horas en milisegundos
const RATE_LIMIT_WINDOW = 10 * 60 * 1000; // 10 minutos en milisegundos

// Rate limit en memoria (se reinicia cuando se reinicia el servidor)
// Estructura: { [ip]: { count: number, lastReset: Date } }
const ipRateLimits = new Map<string, { count: number, lastReset: Date }>();

// Función para verificar el rate limit por IP
function checkIpRateLimit(ip: string, maxRequests = 10, windowMs = 60000) {
  const now = new Date();
  const record = ipRateLimits.get(ip) || { count: 0, lastReset: now };
  
  // Si ha pasado el tiempo de la ventana, reiniciar el contador
  if (now.getTime() - record.lastReset.getTime() > windowMs) {
    record.count = 1;
    record.lastReset = now;
  } else {
    record.count += 1;
  }
  
  ipRateLimits.set(ip, record);
  
  // Calcular tiempo restante para reset en segundos
  const resetTime = new Date(record.lastReset.getTime() + windowMs);
  const resetInSeconds = Math.ceil((resetTime.getTime() - now.getTime()) / 1000);
  
  return {
    success: record.count <= maxRequests,
    limit: maxRequests,
    remaining: Math.max(0, maxRequests - record.count),
    reset: resetInSeconds
  };
}

// Limpiar entradas antiguas cada hora para evitar fugas de memoria
setInterval(() => {
  const now = new Date().getTime();
  for (const [ip, data] of ipRateLimits.entries()) {
    if (now - data.lastReset.getTime() > 3600000) { // 1 hora
      ipRateLimits.delete(ip);
    }
  }
}, 3600000); // Cada hora

export async function POST(request: NextRequest) {
  try {
    console.log("API de leads-db: Iniciando procesamiento de solicitud");
    
    // Obtener la IP del cliente
    const ipAddress = request.headers.get("x-forwarded-for") || 
                      request.headers.get("x-real-ip") || 
                      "unknown";
    
    // Aplicar rate limit por IP: 10 solicitudes por minuto
    const { success, limit, reset, remaining } = checkIpRateLimit(ipAddress, 10, 60000);
    
    // Si se excede el límite, devolver error 429
    if (!success) {
      console.log(`API de leads-db: Rate limit excedido para IP ${ipAddress}`);
      return NextResponse.json(
        { 
          message: "Demasiadas solicitudes. Por favor, inténtalo más tarde.",
          limit,
          remaining,
          resetInSeconds: reset
        }, 
        { 
          status: 429,
          headers: {
            "X-RateLimit-Limit": limit.toString(),
            "X-RateLimit-Remaining": remaining.toString(),
            "X-RateLimit-Reset": reset.toString()
          }
        }
      );
    }
    
    // Obtener el User-Agent
    const userAgent = request.headers.get("user-agent") || "unknown";
    
    // Obtener el cuerpo de la solicitud
    let body;
    try {
      body = await request.json();
      console.log("API de leads-db: Cuerpo de la solicitud:", body);
    } catch (parseError) {
      console.error("API de leads-db: Error al parsear el cuerpo de la solicitud:", parseError);
      return NextResponse.json({ message: "Error al parsear el cuerpo de la solicitud" }, { status: 400 });
    }
    
    // Validar los datos de entrada
    const result = leadSchema.safeParse(body);
    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      return NextResponse.json({ message: `Error de validación: ${errors.join(', ')}` }, { status: 400 });
    }
    
    const { email, source = "unknown" } = body;

    try {
      // Verificar si el correo ya existe como usuario
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        // Si ya es un usuario, no necesitamos guardarlo como lead
        return NextResponse.json(
          { message: "El correo ya está registrado como usuario", isUser: true },
          { status: 200 }
        );
      }

      // Verificar si el correo ya existe como lead
      const existingLead = await prisma.lead.findUnique({
        where: { email },
      });

      if (existingLead) {
        // Verificar límites de intentos para prevenir spam
        const now = new Date();
        const lastAttempt = existingLead.lastAttempt ? new Date(existingLead.lastAttempt) : null;
        
        // Si ha habido demasiados intentos en el período de tiempo definido
        if (existingLead.attemptCount >= MAX_ATTEMPTS && 
            lastAttempt && 
            (now.getTime() - lastAttempt.getTime()) < ATTEMPT_WINDOW) {
          return NextResponse.json(
            { message: "Demasiados intentos. Por favor, inténtalo más tarde." },
            { status: 429 }
          );
        }
        
        // Si el último intento fue muy reciente (para prevenir múltiples solicitudes rápidas)
        if (lastAttempt && (now.getTime() - lastAttempt.getTime()) < RATE_LIMIT_WINDOW) {
          return NextResponse.json(
            { message: "Por favor, espera unos minutos antes de intentarlo de nuevo." },
            { status: 429 }
          );
        }
        
        // Actualizar el lead existente
        const updatedLead = await prisma.lead.update({
          where: { id: existingLead.id },
          data: {
            lastAttempt: now,
            attemptCount: existingLead.attemptCount + 1,
            ipAddress,
            userAgent,
            source: existingLead.source || source, // Mantener la fuente original si existe
          },
        });
        
        console.log("API de leads-db: Lead actualizado:", updatedLead.id);
        
        return NextResponse.json(
          { message: "Lead actualizado correctamente" },
          { status: 200 }
        );
      }

      // Crear un nuevo lead
      const newLead = await prisma.lead.create({
        data: {
          email,
          ipAddress,
          userAgent,
          source,
          lastAttempt: new Date(),
          attemptCount: 1,
        },
      });
      
      console.log("API de leads-db: Nuevo lead creado:", newLead.id);
      
      return NextResponse.json(
        { message: "Lead registrado correctamente", lead: { id: newLead.id, email: newLead.email } },
        { status: 201 }
      );
    } catch (dbError: any) {
      console.error("API de leads-db: Error de base de datos:", dbError);
      
      // Verificar si es un error de clave duplicada
      if (dbError.code === 'P2002') {
        return NextResponse.json(
          { message: "Este correo electrónico ya está registrado" },
          { status: 409 }
        );
      }
      
      throw dbError; // Re-lanzar para que lo maneje el catch exterior
    }
  } catch (error: any) {
    console.error("API de leads-db: Error general:", error);
    // Proporcionar un mensaje de error más detallado si está disponible
    const errorMessage = error.message || "Error al registrar lead";
    return NextResponse.json(
      { 
        message: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error.toString() : undefined 
      },
      { status: 500 }
    );
  }
}
