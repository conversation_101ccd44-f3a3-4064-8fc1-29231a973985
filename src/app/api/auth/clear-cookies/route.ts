import { NextResponse } from 'next/server';

// Manejador para solicitudes GET a /api/auth/clear-cookies
export async function GET() {
  try {
    // Generamos la respuesta JSON
    const response = NextResponse.json(
      { success: true, message: 'Cookies eliminadas correctamente' },
      { status: 200 }
    );

    // Nombres de las cookies a eliminar
    const cookieNames = [
      'next-auth.session-token',
      'next-auth.csrf-token',
      'next-auth.callback-url',
      '__Secure-next-auth.session-token',
      '__Secure-next-auth.csrf-token',
      '__Host-next-auth.csrf-token',
    ];

    // Eliminamos cada cookie de la respuesta
    for (const name of cookieNames) {
      response.cookies.delete(name);
    }

    return response;
  } catch (error) {
    console.error('Error al eliminar cookies:', error);
    return NextResponse.json(
      { success: false, message: 'Error al eliminar cookies' },
      { status: 500 }
    );
  }
}
