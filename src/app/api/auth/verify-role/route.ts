import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/authOptions';
import { isValidRoleSync } from '@/lib/roleUtils';
import { ROLE_HIERARCHY } from '@/lib/roleConstants';

export async function GET(request: NextRequest) {
  try {
    // Obtener los parámetros de la URL
    const searchParams = request.nextUrl.searchParams;
    const requiredRole = searchParams.get('requiredRole');
    const redirectUrl = searchParams.get('redirectUrl');

    // Verificar que los parámetros necesarios estén presentes
    if (!requiredRole || !redirectUrl) {
      return NextResponse.redirect(new URL('/', request.url));
    }

    // Obtener la sesión del usuario
    const session = await getServerSession(authOptions);

    // Si no hay sesión, redirigir a login
    if (!session?.user) {
      const url = new URL('/auth/signin', request.url);
      url.searchParams.set('callbackUrl', redirectUrl);
      return NextResponse.redirect(url);
    }

    // Obtener el rol del usuario
    const userRole = session.user.role;

    // Verificar que el rol es válido
    if (!userRole || !isValidRoleSync(userRole)) {
      return NextResponse.redirect(new URL('/', request.url));
    }

    // Verificar que el rol tiene suficientes privilegios
    const userRoleIndex = ROLE_HIERARCHY.indexOf(userRole as any);
    const requiredRoleIndex = ROLE_HIERARCHY.indexOf(requiredRole as any);

    if (userRoleIndex < requiredRoleIndex) {
      return NextResponse.redirect(new URL('/', request.url));
    }

    // Si todo está bien, redirigir a la URL original
    return NextResponse.redirect(new URL(redirectUrl));
  } catch (error) {
    console.error('Error al verificar el rol:', error);
    return NextResponse.redirect(new URL('/', request.url));
  }
}
