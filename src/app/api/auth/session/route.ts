import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/authOptions';

// Manejador para solicitudes GET a /api/auth/session
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    return NextResponse.json({
      user: session?.user || null,
      expires: session?.expires || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    });
  } catch (error) {
    console.error('Error al obtener la sesión:', error);
    
    // En caso de error, devolver una respuesta exitosa con un objeto de sesión vacío
    return NextResponse.json({
      user: null,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    });
  }
}

// Manejador para solicitudes POST a /api/auth/session
export async function POST() {
  try {
    const session = await getServerSession(authOptions);
    
    return NextResponse.json({
      user: session?.user || null,
      expires: session?.expires || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    });
  } catch (error) {
    console.error('Error al obtener la sesión:', error);
    
    // En caso de error, devolver una respuesta exitosa con un objeto de sesión vacío
    return NextResponse.json({
      user: null,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    });
  }
}
