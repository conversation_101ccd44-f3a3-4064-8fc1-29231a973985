import { NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import bcrypt from "bcryptjs";
import { z } from "zod";

// Esquema de validación para el registro
const registerSchema = z.object({
  name: z.string()
    .min(1, "El nombre es requerido")
    .max(50, "El nombre no puede exceder los 50 caracteres"),
  email: z.string().email("Correo electrónico inválido"),
  phone: z.string().min(10, "El teléfono debe tener al menos 10 dígitos").optional(),
  password: z.string().min(6, "La contraseña debe tener al menos 6 caracteres"),
});

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Validar los datos de entrada
    const result = registerSchema.safeParse(body);
    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      return NextResponse.json({ message: `Error de validación: ${errors.join(', ')}` }, { status: 400 });
    }

    const { name, email, phone, password } = body;

    // Verificar si el usuario ya existe
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { message: "El correo electrónico ya está registrado" },
        { status: 409 }
      );
    }

    // Encriptar la contraseña
    const hashedPassword = await bcrypt.hash(password, 10);

    // Crear el usuario
    const user = await prisma.user.create({
      data: {
        name,
        email,
        ...(phone ? { phone } : {}),
        password: hashedPassword,
      },
    });

    // Actualizar el lead si existe para marcarlo como convertido a usuario
    try {
      await prisma.lead.updateMany({
        where: { email },
        data: { convertedToUser: true }
      });
    } catch (leadError) {
      // No hacemos nada si hay un error al actualizar el lead
      // ya que lo importante es que el usuario se haya creado correctamente
      console.warn("Error al actualizar el estado del lead:", leadError);
    }

    // Eliminar la contraseña del objeto de respuesta
    const { password: _, ...userWithoutPassword } = user;

    return NextResponse.json(
      { message: "Usuario registrado correctamente", user: userWithoutPassword },
      { status: 201 }
    );
  } catch (error: any) {
    console.error("Error al registrar usuario:", error);
    return NextResponse.json(
      { message: "Error al registrar usuario" },
      { status: 500 }
    );
  }
}
