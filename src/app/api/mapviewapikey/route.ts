import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/authOptions';
import { hasRoleOrHigher } from '@/lib/roles';
import { errorLogger } from '@/lib/loggers/logger';

// Rate limit en memoria (se reinicia cuando se reinicia el servidor)
// Estructura: { [ip]: { count: number, lastReset: Date } }
const ipRateLimits = new Map<string, { count: number, lastReset: Date }>();

// Función para verificar el rate limit por IP
function checkIpRateLimit(ip: string, maxRequests = 20, windowMs = 60000) {
  const now = new Date();
  const record = ipRateLimits.get(ip) || { count: 0, lastReset: now };
  
  // Si ha pasado el tiempo de la ventana, reiniciar el contador
  if (now.getTime() - record.lastReset.getTime() > windowMs) {
    record.count = 1;
    record.lastReset = now;
  } else {
    record.count += 1;
  }
  
  ipRateLimits.set(ip, record);
  
  // Calcular tiempo restante para reset en segundos
  const resetTime = new Date(record.lastReset.getTime() + windowMs);
  const resetInSeconds = Math.ceil((resetTime.getTime() - now.getTime()) / 1000);
  
  return {
    success: record.count <= maxRequests,
    limit: maxRequests,
    remaining: Math.max(0, maxRequests - record.count),
    reset: resetInSeconds
  };
}

// Limpiar entradas antiguas cada hora para evitar fugas de memoria
setInterval(() => {
  const now = new Date().getTime();
  for (const [ip, data] of ipRateLimits.entries()) {
    if (now - data.lastReset.getTime() > 3600000) { // 1 hora
      ipRateLimits.delete(ip);
    }
  }
}, 3600000); // Cada hora

export async function GET(request: Request) {
  try {
    // Obtener la IP del cliente
    const ip = request.headers.get('x-forwarded-for') || 'unknown-ip';
    
    // Aplicar rate limit: 20 solicitudes por minuto
    const rateLimit = checkIpRateLimit(ip, 20, 60000);
    
    if (!rateLimit.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Demasiadas solicitudes. Por favor, inténtalo más tarde." 
        },
        { 
          status: 429,
          headers: {
            "X-RateLimit-Limit": rateLimit.limit.toString(),
            "X-RateLimit-Remaining": rateLimit.remaining.toString(),
            "X-RateLimit-Reset": rateLimit.reset.toString()
          }
        }
      );
    }
    
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    
    // Si no hay sesión, denegar acceso
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'No autenticado' },
        { status: 401 }
      );
    }
    
    // Verificar que el usuario tiene al menos el rol USER
    const hasRequiredRole = await hasRoleOrHigher('USER');
    if (!hasRequiredRole) {
      return NextResponse.json(
        { success: false, message: 'Acceso denegado. Rol insuficiente.' },
        { status: 403 }
      );
    }
    
    // Verificar que la clave API existe (pero no la devolvemos)
    const mapTilerApiKey = process.env.MAPTILER_API_KEY;
    
    if (!mapTilerApiKey) {
      console.error('Error: MAPTILER_API_KEY no está definida en las variables de entorno');
      return NextResponse.json(
        { success: false, message: 'Configuración de MapTiler no disponible' },
        { status: 500 }
      );
    }
    
    // Devolver la clave API de forma segura solo al servidor
    return NextResponse.json({ 
      success: true,
      mapTilerApiKey: mapTilerApiKey,
      message: 'Servicio de MapTiler disponible'
    });
  } catch (error: any) {
    // Registrar el error completo internamente
    errorLogger.error('Error al verificar el servicio de MapTiler', { 
      error: error.stack || error.toString(),
      endpoint: '/api/mapviewapikey'
    });
    console.error('Error al verificar el servicio de MapTiler:', error);
    
    // Devolver un mensaje genérico al cliente
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
