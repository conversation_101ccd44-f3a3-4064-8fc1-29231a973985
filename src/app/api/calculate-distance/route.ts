import { NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/authOptions';
import { isValidRoleSync } from '@/lib/roleUtils';
import { ROLE_HIERARCHY } from '@/lib/roleConstants';

// Esquema de validación para las coordenadas
const pointSchema = z.object({
  lat: z.number().min(-90).max(90),
  lon: z.number().min(-180).max(180),
});

const requestBodySchema = z.object({
  origin: pointSchema,
  destination: pointSchema,
});

export async function POST(request: Request) {
  try {
    // Verificar autenticación y rol del usuario
    const session = await getServerSession(authOptions);
    
    // Si no hay sesión, denegar acceso
    if (!session?.user) {
      return NextResponse.json(
        { error: 'No autenticado' },
        { status: 401 }
      );
    }
    
    // Verificar que el rol es válido
    const userRole = session.user.role;
    if (!userRole || !isValidRoleSync(userRole)) {
      return NextResponse.json(
        { error: 'Rol de usuario inválido' },
        { status: 403 }
      );
    }
    
    // Verificar que el rol tiene suficientes privilegios (WORKER o superior)
    const userRoleIndex = ROLE_HIERARCHY.indexOf(userRole as any);
    const requiredRoleIndex = ROLE_HIERARCHY.indexOf('WORKER');
    
    if (userRoleIndex < requiredRoleIndex) {
      return NextResponse.json(
        { error: 'Acceso denegado. Se requiere rol WORKER o superior' },
        { status: 403 }
      );
    }

    // Continuar con el procesamiento normal si el usuario tiene permisos
    // 1. Validar el cuerpo de la solicitud
    const rawBody = await request.json();
    const validationResult = requestBodySchema.safeParse(rawBody);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Datos de entrada inválidos', details: validationResult.error.errors },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store',
            'Connection': 'keep-alive'
          }
        }
      );
    }

    const { origin, destination } = validationResult.data;

    // 2. Formatear coordenadas para Mapbox: lon,lat;lon,lat
    const coordinates = `${origin.lon},${origin.lat};${destination.lon},${destination.lat}`;

    // 3. Construir la URL de Mapbox Directions
    const token = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;
    if (!token) {
      throw new Error('Token de Mapbox no configurado en NEXT_PUBLIC_MAPBOX_TOKEN');
    }
    const url = `https://api.mapbox.com/directions/v5/mapbox/driving/${coordinates}` +
                `?access_token=${token}` +
                `&overview=false`; // overview=false evita devolver la geometría completa

    // 4. Llamar a la API de Mapbox
    const response = await fetch(url);

    if (!response.ok) {
      return NextResponse.json(
        { error: `Error en la API de Mapbox: ${response.status} ${response.statusText}` },
        {
          status: 502,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store',
            'Connection': 'keep-alive'
          }
        }
      );
    }

    const data = await response.json();

    // 5. Validar la respuesta de Mapbox
    if (data.code !== 'Ok' || !data.routes || data.routes.length === 0) {
      return NextResponse.json(
        { error: 'No se pudo calcular la ruta', mapboxResponse: data },
        {
          status: 422,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store',
            'Connection': 'keep-alive'
          }
        }
      );
    }

    // 6. Extraer la distancia en metros y convertir a kilómetros
    const distanceInMeters = data.routes[0].distance;
    const distanceInKm = distanceInMeters / 1000;

    // 7. Devolver la distancia en kilómetros
    return NextResponse.json(
      { distance: distanceInKm },
      {
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'max-age=3600',
          'Access-Control-Allow-Origin': '*',
          'Connection': 'keep-alive'
        }
      }
    );

  } catch (error) {
    console.error('Error al calcular distancia con Mapbox:', error);

    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: 'Formato JSON inválido en la solicitud' },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store',
            'Connection': 'keep-alive'
          }
        }
      );
    }

    return NextResponse.json(
      { error: 'Error al procesar la solicitud' },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store',
          'Connection': 'keep-alive'
        }
      }
    );
  }
}
