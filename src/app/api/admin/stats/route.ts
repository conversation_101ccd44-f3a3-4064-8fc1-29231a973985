import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/authOptions";
import { prisma } from "@/lib/db";

export async function GET() {
  try {
    // Verificar autenticación y permisos de admin
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "No autenticado" }, { status: 401 });
    }

    const userRole = session.user.role;
    if (!userRole || !["ADMIN", "SUPER_ADMIN", "WORKER"].includes(userRole)) {
      return NextResponse.json({ error: "Acceso denegado" }, { status: 403 });
    }

    // Obtener estadísticas generales
    const [
      totalUsuarios,
      totalLeads,
      totalTerminales,
      preciosRecientes,
      leadsRecientes
    ] = await Promise.all([
      // Total de usuarios
      prisma.user.count(),
      
      // Total de leads
      prisma.lead.count(),
      
      // Total de terminales activas
      prisma.terminal.count({
        where: { activo: true }
      }),
      
      // Precios recientes (últimos 30 días)
      prisma.precio.findMany({
        where: {
          fecha: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          terminal: true
        },
        orderBy: {
          fecha: 'desc'
        },
        take: 100
      }),
      
      // Leads recientes (últimos 30 días)
      prisma.lead.findMany({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 50
      })
    ]);

    // Calcular precio promedio
    const precioPromedio = preciosRecientes.length > 0 
      ? preciosRecientes.reduce((sum, precio) => sum + precio.valor, 0) / preciosRecientes.length
      : 0;

    // Agrupar precios por fecha para el gráfico
    const preciosPorFecha = preciosRecientes.reduce((acc, precio) => {
      const fecha = precio.fecha.toISOString().split('T')[0];
      if (!acc[fecha]) {
        acc[fecha] = [];
      }
      acc[fecha].push(precio.valor);
      return acc;
    }, {} as Record<string, number[]>);

    const tendenciaPrecios = Object.entries(preciosPorFecha)
      .map(([fecha, precios]) => ({
        fecha,
        precio: precios.reduce((sum, p) => sum + p, 0) / precios.length,
        terminal: "Promedio"
      }))
      .sort((a, b) => new Date(a.fecha).getTime() - new Date(b.fecha).getTime())
      .slice(-7); // Últimos 7 días

    // Agrupar leads por mes para conversión
    const leadsConversion = [];
    const meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];
    
    for (let i = 5; i >= 0; i--) {
      const fecha = new Date();
      fecha.setMonth(fecha.getMonth() - i);
      const mesInicio = new Date(fecha.getFullYear(), fecha.getMonth(), 1);
      const mesFin = new Date(fecha.getFullYear(), fecha.getMonth() + 1, 0);
      
      const leadsDelMes = await prisma.lead.count({
        where: {
          createdAt: {
            gte: mesInicio,
            lte: mesFin
          }
        }
      });
      
      const conversionesDelMes = await prisma.lead.count({
        where: {
          createdAt: {
            gte: mesInicio,
            lte: mesFin
          },
          convertedToUser: true
        }
      });
      
      leadsConversion.push({
        mes: meses[fecha.getMonth()],
        leads: leadsDelMes,
        conversiones: conversionesDelMes
      });
    }

    // Obtener terminales con más actividad
    const terminalActividad = await prisma.terminal.findMany({
      include: {
        _count: {
          select: {
            precios: {
              where: {
                fecha: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                }
              }
            }
          }
        }
      },
      where: { activo: true }
    });

    const ventasPorTerminal = terminalActividad
      .map(terminal => ({
        terminal: terminal.nombre,
        ventas: terminal._count.precios * 10 + Math.floor(Math.random() * 500), // Simulando ventas
        color: `#${Math.floor(Math.random()*16777215).toString(16)}`
      }))
      .sort((a, b) => b.ventas - a.ventas)
      .slice(0, 5);

    const stats = {
      estadisticasGenerales: {
        totalUsuarios,
        totalLeads,
        precioPromedio: Number(precioPromedio.toFixed(2)),
        terminalesActivas: totalTerminales
      },
      tendenciaPrecios,
      ventasPorTerminal,
      leadsConversion,
      ultimosLeads: leadsRecientes.slice(0, 10).map(lead => ({
        id: lead.id,
        email: lead.email,
        source: lead.source,
        convertedToUser: lead.convertedToUser,
        createdAt: lead.createdAt
      }))
    };

    return NextResponse.json(stats);
    
  } catch (error) {
    console.error("Error al obtener estadísticas:", error);
    return NextResponse.json(
      { error: "Error interno del servidor" },
      { status: 500 }
    );
  }
}
