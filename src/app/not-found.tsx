// app/not-found.tsx
import Link from 'next/link'
import Image from 'next/image'
import dynamic from 'next/dynamic'

const Navbar = dynamic(() => import('@/components/layout/Navbar'))
import notFoundImage from '@/assets/images/hero/isometric-oldweb.webp'

export default function NotFound() {
  return (
    <>
      <Navbar />
      <main className="flex min-h-screen flex-col items-center py-20 justify-center bg-gradient-to-b from-zinc-50 via-white to-white dark:from-zinc-900 dark:via-zinc-950 dark:to-black px-4 py-16 relative">
        {/* Degradado de fondo */}
        <section
          className="absolute inset-0 bg-gradient-to-t from-orange-100/70"
          aria-hidden="true"
        />
        <div className="text-center relative z-10">
          {/* Código de error */}
          <h1 className="mb-3 text-7xl font-extrabold tracking-tight ">
            404
          </h1>

          {/* Título */}
          <h2 className="mb-8 text-2xl font-semibold text-gray-800 dark:text-gray-200 sm:text-3xl">
            Página no encontrada
          </h2>

          {/* Ilustración */}
          <div className="relative mx-auto mb-10 w-80 max-w-full">
            <div className="absolute inset-0 -z-10 rounded-3xl blur-5xl" />
            <Image
              src={notFoundImage}
              alt="Ilustración de pozo petrolero y auto-tanques"
              priority
              className="animate-float "
            />
          </div>

          {/* Descripción */}
          <p className="mx-auto mb-6 max-w-md text-lg text-gray-600 dark:text-gray-400">
            <span className="font-medium text-black">Lo sentimos</span>, la página que buscas <span className="font-medium text-black">no existe o ha cambiado de lugar.</span>
          </p>

          {/* CTA */}
          <Link
            href="/"
            className="inline-flex items-center gap-2 rounded-md bg-primary px-8 py-3 text-white shadow-lg hover:shadow-primary/30 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/40"
          >
            Volver al inicio
          </Link>
        </div>
      </main>
    </>
  )
}
