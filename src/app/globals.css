/*
Version: 1.0
Author: coderthemes
Email: <EMAIL>
*/
/* @import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700&display=swap'); */


@tailwind base;
@tailwind components;
@tailwind utilities;

/* @import '../../node_modules/aos/dist/aos.css'; */

.filter-options li a {
  @apply text-gray-700 px-4 py-1.5 rounded border border-gray-300 flex items-center justify-center text-sm transition-all duration-500;
}

.filter-options li:hover a,
.filter-options li.active a,
.filter-options li:active a,
.filter-options li:focus a {
  @apply bg-primary text-white border-transparent shadow-md shadow-primary/25;
}

.lg-react-element {
  @apply flex flex-wrap justify-center items-start !important;
}

.hero-with-shapes {
  @keyframes rotating {
    0% {
      transform: rotate(0deg);
      top: 10%;
      left: 5%;
    }

    100% {
      transform: rotate(360deg);
      top: 60%;
      left: 15%;
    }
  }

  @keyframes rotating2 {
    0% {
      transform: rotate(0deg);
      bottom: 10%;
      right: 10%;
    }

    100% {
      transform: rotate(360deg);
      bottom: 80%;
      right: 30%;
    }
  }

  @keyframes rotating3 {
    0% {
      transform: rotate(0deg);
      bottom: 0%;
      right: 65%;
    }

    100% {
      transform: rotate(360deg);
      bottom: 50%;
      right: 35%;
    }
  }

  .shape1 {
    position: absolute;
    top: 5%;
    left: 5%;
    width: 7rem;
    height: 7rem;
    background: url('../assets/images/shapes/rounded-square2.svg');
    animation: rotating alternate ease-in-out infinite 6s;
  }

  .shape2 {
    position: absolute;
    bottom: 20%;
    right: 10%;
    width: 7rem;
    height: 7rem;
    background: url('../assets/images/shapes/rounded-square2.svg');
    animation: rotating2 alternate ease-in-out infinite 6s;
  }

  .shape3 {
    position: absolute;
    bottom: 0%;
    right: 65%;
    width: 7rem;
    height: 7rem;
    background: url('../assets/images/shapes/rounded-square2.svg');
    animation: rotating3 alternate ease-in-out infinite 6s;
  }
}


button[data-headlessui-state] {
  outline: transparent;
}


.logo-light {
  @apply hidden;
}

@screen lg {
  .logo-light {
    @apply hidden dark:block;
  }

  .logo-dark {
    @apply block dark:hidden;
  }
}

.navbar-nav {
  .nav-item {
    .nav-link {
      @apply font-medium relative tracking-wide flex items-center py-1.5 px-3.5 text-gray-800 rounded-md text-sm cursor-pointer transition-all duration-300 bg-transparent;

      &:is(.active, :active, :focus, :hover) {
        @apply text-primary;
      }
    }

    .nav-item {
      .nav-item {

        .nav-link {
          @apply text-gray-800;
        }
      }
    }
  }
}

Navbar header {


  &.nav-sticky {
    @apply bg-white shadow;
  }

  &.dark {
    .navbar-nav {
      .nav-item {
        .nav-link {
          @apply text-gray-100;

          &:is(.active, :active, :focus, :hover) {
            @apply text-primary;
          }
        }

        .fc-dropdown {
          .nav-link {
            @apply text-gray-800;

            &.active {
              @apply text-primary;
            }
          }
        }
      }
    }

    &.nav-sticky {

      @screen lg {

        .logo-light {
          @apply hidden;
        }

        .logo-dark {
          @apply block;
        }
      }

      .navbar-nav {
        .nav-link {
          @apply text-gray-800 hover:text-primary;

          &:is(.active, :active, :focus, :hover) {
            @apply text-primary;
          }
        }
      }
    }
  }
}



html {
  @apply relative scroll-smooth;
}


body {
  font-size: 15px;
  @apply font-body overflow-x-hidden text-gray-500;
}


@supports (-webkit-overflow-scrolling: touch) {
  body {
    cursor: pointer;
  }
}


input,
textarea,
button,
select,
a {
  -webkit-tap-highlight-color: transparent;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  @apply font-semibold;
}

.vertical-rl {
  writing-mode: vertical-rl;
  text-orientation: mixed;
}


:root {
  &:is([data-mode="dark"]) {
    color-scheme: dark;
  }
}


@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}


@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Estilos personalizados para scrollbar */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background: #d1d5db;
  }

  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background: #f3f4f6;
  }

  /* Animación de aparición suave */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }
}

/* Prevenir desbordamiento horizontal */
html, body {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

/* Asegurar que todos los contenedores respeten el viewport */
* {
  max-width: 100vw;
  box-sizing: border-box;
}
