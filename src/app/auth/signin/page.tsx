
"use client";
import { useState, useEffect } from "react";
import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { FaChevronLeft } from "react-icons/fa";
import { GrLogin } from "react-icons/gr";
import { FiUserPlus } from 'react-icons/fi';
import logoDark from "@/assets/images/logo-combustibles-cassiopeia.png";
import calidad from "@/assets/images/hero/combustibles-de-calidad.png";
import { securityLogger } from "@/lib/loggers/clientLogger";

// Función para validar y sanitizar URLs de redirección
const validateRedirectUrl = (url: string | null): string => {
  // Si no hay URL, redirigir a la página principal
  if (!url) return "/";
  
  try {
    // Verificar si la URL es relativa (comienza con /)
    if (url.startsWith("/")) {
      // Asegurarse de que no hay caracteres de escape o manipulación
      const sanitizedPath = url.replace(/[^\w\s\-_\/\?\&\=\.]/gi, "");
      
      // Verificar que no intenta escapar del dominio con //
      if (sanitizedPath.includes("//")) return "/";
      
      return sanitizedPath;
    }
    
    // Si la URL es absoluta, verificar que pertenece a nuestro dominio
    const currentHost = window.location.host;
    const urlObj = new URL(url);
    
    if (urlObj.host === currentHost) {
      return urlObj.pathname + urlObj.search;
    }
    
    // Si la URL es de otro dominio, redirigir a la página principal
    securityLogger.warn("Intento de redirección a dominio externo bloqueado", {
      attemptedUrl: url,
      attemptedHost: urlObj.host,
      allowedHost: currentHost,
      userAgent: navigator.userAgent
    });
    return "/";
  } catch (error) {
    // Si hay un error al analizar la URL, redirigir a la página principal
    securityLogger.error("Error al validar URL de redirección en el cliente", {
      url,
      error: error instanceof Error ? error.message : String(error)
    });
    return "/";
  }
};

export default function SignIn() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const rawCallbackUrl = searchParams.get("callbackUrl");
  const callbackUrl = validateRedirectUrl(rawCallbackUrl);
  
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [registered, setRegistered] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [honeypotField, setHoneypotField] = useState("");
  const [loginAttempts, setLoginAttempts] = useState(0);

  useEffect(() => {
    // Recuperar intentos de login del almacenamiento local
    const storedAttempts = localStorage.getItem('loginAttempts');
    if (storedAttempts) {
      setLoginAttempts(parseInt(storedAttempts, 10));
    }
  }, []);

  // Obtener el correo electrónico y el estado de registro de los parámetros de búsqueda
  useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(emailParam);
    }

    const isRegistered = searchParams.get("registered") === "true";
    if (isRegistered) {
      setRegistered(true);
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Verificación de honeypot - si está lleno, es probablemente un bot
    if (honeypotField) {
      securityLogger.warn("Posible ataque de bot detectado (honeypot)", {
        email,
        userAgent: navigator.userAgent
      });
      
      // Simular éxito para no alertar al bot
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      setLoading(false);
      setError("Error de autenticación. Inténtalo de nuevo más tarde.");
      return;
    }
    
    setLoading(true);
    setError("");

    try {
      // Registrar intento de inicio de sesión (sin la contraseña)
      securityLogger.info("Intento de inicio de sesión", {
        email: email,
        userAgent: navigator.userAgent,
        origin: window.location.origin,
        callbackUrl: callbackUrl
      });

      // Validar nuevamente la URL de redirección antes de enviarla
      const safeCallbackUrl = validateRedirectUrl(callbackUrl);
      
      // Eliminar logs de información sensible
      const result = await signIn("credentials", {
        redirect: false,
        email,
        password,
        callbackUrl: safeCallbackUrl
      });

      if (result?.error) {
        // Incrementar contador de intentos fallidos
        const newAttempts = loginAttempts + 1;
        setLoginAttempts(newAttempts);
        localStorage.setItem('loginAttempts', newAttempts.toString());
        
        // Mensaje genérico para evitar enumeración de usuarios
        setError("Credenciales inválidas. Por favor, intenta de nuevo.");
        
        // Registrar el intento fallido de forma segura
        securityLogger.warn("Intento de inicio de sesión fallido", {
          email: email,
          reason: "Credenciales inválidas",
          userAgent: navigator.userAgent,
          origin: window.location.origin
        });
      } else {
        // Registrar inicio de sesión exitoso
        securityLogger.info("Inicio de sesión exitoso", {
          email: email,
          userAgent: navigator.userAgent,
          origin: window.location.origin
        });
        
        // Usar router.push en lugar de window.location para redirecciones seguras
        setTimeout(() => {
          router.push(safeCallbackUrl);
        }, 500);
        return;
      }
    } catch (error) {
      // No exponer detalles del error
      setError("Ocurrió un error al iniciar sesión. Por favor, intenta de nuevo.");
      
      // Registrar el error de forma segura
      securityLogger.error("Error durante el proceso de inicio de sesión", {
        email: email,
        errorType: error instanceof Error ? error.name : "UnknownError",
        userAgent: navigator.userAgent,
        origin: window.location.origin
      });
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-white to-blue-100/20 relative">
      {/* Reemplazar el botón con posicionamiento absoluto */}
      <div className="w-full max-w-6xl mb-4">
        <Link 
          href="/" 
          className="inline-flex items-center border text-gray-600 hover:text-gray-900 bg-white rounded-lg px-3 py-2 shadow-sm hover:shadow transition-all"
        >
          <FaChevronLeft className="mr-2 h-3.5 w-3.5" />
          Volver al inicio
        </Link>
      </div>
      
      <div className="grid md:grid-cols-12 bg-white border shadow rounded-xl mb-6 w-full max-w-6xl overflow-hidden">
        <div className="bg-white shadow-[1px_0px_15px_0px_rgba(0,0,0,0.1)] p-4 sm:p-6 md:p-8 lg:p-12 rounded-s-xl xl:col-span-5 md:col-span-6">
          <div className="mb-8">
            <Image 
              src={logoDark} 
              alt="Logo Cassiopeia Petrolíferos | Combustibles Cassiopeia" 
              width={224} 
              height={48}
              className="h-auto"
            />
          </div>
          
          <h6 className="text-base font-medium text-gray-600">¡Bienvenido de nuevo!</h6>
          <p className="text-gray-500 text-sm mt-1 mb-6">Ingresa tu correo electrónico y contraseña para acceder.</p>
          
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
              {error}
            </div>
          )}

          {registered && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4">
              ¡Registro exitoso! Ahora puedes iniciar sesión con tus credenciales.
            </div>
          )}
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4 relative">
              <label className="block text-md font-medium text-gray-600 mb-2" htmlFor="email">
                Correo Electrónico
              </label>
              <div className="relative">
                <input 
                  type="email" 
                  placeholder="Ingresa tu correo electrónico" 
                  className="block w-full rounded-lg py-2 px-4 border border-gray-200 focus:outline-none" 
                  name="email" 
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-md font-medium text-gray-600 mb-2" htmlFor="password">
                Contraseña
              </label>
              <div className="flex">
                <div className="w-full relative">
                  <input 
                    type={showPassword ? "text" : "password"} 
                    placeholder="Ingresa tu contraseña" 
                    className="block w-full rounded-l-lg py-2 px-4 border border-gray-200 focus:ring-transparent focus:border-gray-200" 
                    name="password" 
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                  />
                </div>
                <span 
                  className="inline-flex items-center justify-center px-4 border rounded-r-lg -ms-px cursor-pointer"
                  onClick={togglePasswordVisibility}
                >
                  <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="20" width="20" xmlns="http://www.w3.org/2000/svg">
                    {showPassword ? (
                      <>
                        <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"></path>
                        <path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"></path>
                        <path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"></path>
                        <line x1="2" x2="22" y1="2" y2="22"></line>
                      </>
                    ) : (
                      <>
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </>
                    )}
                  </svg>
                </span>
              </div>
            </div>
            
            <div className="flex justify-center mb-6 mt-6">
              <button 
                type="submit" 
                disabled={loading}
                className="relative inline-flex items-center justify-center px-6 py-2 hover:bg-primary-600 rounded-lg text-base bg-primary text-white capitalize transition-all w-full disabled:opacity-70"
              >
                <GrLogin className="w-4 h-4 mr-2"/>
                {loading ? "Iniciando sesión..." : "Iniciar sesión"}
              </button>
            </div>
          </form>
          
          <div className="w-full">
            <Link href="/auth/signup" className="block border text-gray-600 font-medium leading-6 text-center align-middle select-none py-2 px-4 rounded-lg transition-all hover:shadow-md">
              <span className="flex items-center justify-center">
                <FiUserPlus className="w-4 h-4 mr-2"/>
                Registrarse
              </span>
            </Link>
          </div>
        </div>
        
        <div className="hidden md:block xl:col-span-7 md:col-span-6">
          <div className="max-w-[100%] mx-auto">
            <div className="pt-12">
              <div className="flex items-center justify-center h-full">
                <div className="swiper swiper-initialized swiper-horizontal swiper-backface-hidden">
                  <div className="swiper-wrapper">
                    <div className="swiper-slide swiper-slide-active">
                      <div className="swiper-slide-content">
                        <div className="text-center w-4/5 mx-auto">
                          <Image 
                            src={calidad} 
                            alt="Calidad que Impulsa el Cambio Combustibles Cassiopeia" 
                            width={800} 
                            height={547}
                            className="w-full rounded-xl shadow-lg"
                            priority
                          />
                        </div>
                        <div className="text-center my-6 pb-8">
                          <h5 className="font-medium text-lg text-gray-600 my-2.5">Líderes en Abastecimiento de Hidrocarburos de Alta Calidad en México.</h5>
                          <p className="text-md text-gray-500">Accede a tu cuenta para acceder a precios exclusivos y más.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="swiper-pagination !bottom-0"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
