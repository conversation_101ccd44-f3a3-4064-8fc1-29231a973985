"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";

export default function AuthError() {
  const searchParams = useSearchParams();
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    const error = searchParams.get("error");
    
    if (error === "CredentialsSignin") {
      setErrorMessage("Credenciales inválidas. Por favor, verifica tu correo y contraseña.");
    } else if (error === "AccessDenied") {
      setErrorMessage("Acceso denegado. No tienes permiso para acceder a esta página.");
    } else if (error === "OAuthSignin" || error === "OAuthCallback" || error === "OAuthCreateAccount") {
      setErrorMessage("Hubo un problema con la autenticación externa. Por favor, intenta de nuevo.");
    } else if (error === "EmailCreateAccount" || error === "Callback" || error === "EmailSignin") {
      setErrorMessage("Hubo un problema con la autenticación por correo. Por favor, intenta de nuevo.");
    } else if (error === "SessionRequired") {
      setErrorMessage("Debes iniciar sesión para acceder a esta página.");
    } else {
      setErrorMessage("Ocurrió un error durante la autenticación. Por favor, intenta de nuevo.");
    }
  }, [searchParams]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="w-full max-w-md space-y-8 bg-white p-8 rounded-xl shadow-md">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
            Error de autenticación
          </h2>
        </div>

        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {errorMessage}
        </div>

        <div className="flex flex-col space-y-4">
          <Link
            href="/auth/signin"
            className="w-full text-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Volver a iniciar sesión
          </Link>
          <Link
            href="/"
            className="w-full text-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Volver al inicio
          </Link>
        </div>
      </div>
    </div>
  );
}
