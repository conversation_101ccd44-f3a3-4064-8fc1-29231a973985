
"use client";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";

import logoDark from "@/assets/images/logo-combustibles-cassiopeia.png";
import calidad from "@/assets/images/hero/combustibles-de-calidad.png";
import impulsando from "@/assets/images/hero/impulsando-un-futuro.png";
import recogerack from "@/assets/images/hero/combustible-en-terminal.png";
import { FaChevronLeft } from "react-icons/fa";
import { FiUserPlus } from "react-icons/fi";
// Eliminamos la importación de securityLogger

// Función para registrar eventos de seguridad desde el cliente
const logSecurityEvent = async (level: 'info' | 'warn' | 'error', message: string, metadata: any) => {
  try {
    // Enviamos el evento a un endpoint de API que usará el logger en el servidor
    await fetch('/api/log/security', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        level,
        message,
        metadata: {
          ...metadata,
          userAgent: navigator.userAgent,
          origin: window.location.origin,
          path: window.location.pathname
        }
      }),
      // No esperamos la respuesta para no bloquear la UI
      keepalive: true
    });
  } catch (error) {
    // Si falla el registro, al menos lo mostramos en consola
    console.error('Error al registrar evento de seguridad:', error);
  }
};

export default function SignUp() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [honeypotField, setHoneypotField] = useState(""); // Campo honeypot para detectar bots
  const [signupAttempts, setSignupAttempts] = useState(0);

  // Cargar intentos de registro previos al montar el componente
  useEffect(() => {
    const storedAttempts = localStorage.getItem('signupAttempts');
    if (storedAttempts) {
      setSignupAttempts(parseInt(storedAttempts, 10));
    }
  }, []);

  // Función para validar y sanitizar URLs de redirección
  const validateRedirectUrl = (url: string | null): string => {
    // Si no hay URL, redirigir a la página principal
    if (!url) return "/";
    
    try {
      // Verificar si la URL es relativa (comienza con /)
      if (url.startsWith("/")) {
        // Asegurarse de que no hay caracteres de escape o manipulación
        const sanitizedPath = url.replace(/[^\w\s\-_\/\?\&\=\.]/gi, "");
        
        // Verificar que no intenta escapar del dominio con //
        if (sanitizedPath.includes("//")) return "/";
        
        return sanitizedPath;
      }
      
      // Si la URL es absoluta, verificar que pertenece a nuestro dominio
      const currentHost = window.location.host;
      const urlObj = new URL(url);
      
      if (urlObj.host === currentHost) {
        return urlObj.pathname + urlObj.search;
      }
      
      // Si la URL es de otro dominio, redirigir a la página principal
      logSecurityEvent('warn', "Intento de redirección a dominio externo bloqueado", {
        attemptedUrl: url,
        attemptedHost: urlObj.host,
        allowedHost: currentHost
      });
      return "/";
    } catch (error) {
      // Si hay un error al analizar la URL, redirigir a la página principal
      logSecurityEvent('error', "Error al validar URL de redirección en el cliente", {
        url,
        error: error instanceof Error ? error.message : String(error)
      });
      return "/";
    }
  };

  // Obtener el correo electrónico de los parámetros de búsqueda
  useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(emailParam);
    }
    
    // Validar callbackUrl si existe
    const callbackUrl = searchParams.get("callbackUrl");
    if (callbackUrl) {
      // Almacenar la URL validada para usarla después del registro
      const safeCallbackUrl = validateRedirectUrl(callbackUrl);
      if (safeCallbackUrl !== callbackUrl) {
        // Si la URL no es segura, actualizar la URL sin recargar la página
        const newUrl = new URL(window.location.href);
        if (safeCallbackUrl === "/") {
          newUrl.searchParams.delete("callbackUrl");
        } else {
          newUrl.searchParams.set("callbackUrl", safeCallbackUrl);
        }
        window.history.replaceState({}, "", newUrl.toString());
        
        // Registrar el intento de manipulación de URL
        logSecurityEvent('warn', "URL de redirección manipulada detectada y sanitizada", {
          originalUrl: callbackUrl,
          sanitizedUrl: safeCallbackUrl
        });
      }
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Verificar si hay demasiados intentos de registro
    if (signupAttempts >= 5) {
      const lastAttemptTime = localStorage.getItem('lastSignupAttempt');
      const now = Date.now();
      
      if (lastAttemptTime && (now - parseInt(lastAttemptTime, 10)) < 30 * 60 * 1000) { // 30 minutos
        logSecurityEvent('warn', "Demasiados intentos de registro", {
          email,
          attempts: signupAttempts
        });
        
        setLoading(false);
        setError("Demasiados intentos. Por favor, inténtalo más tarde.");
        return;
      } else {
        // Reiniciar contador después de 30 minutos
        setSignupAttempts(0);
        localStorage.removeItem('signupAttempts');
      }
    }

    // Verificación de honeypot - si está lleno, es probablemente un bot
    if (honeypotField) {
      logSecurityEvent('warn', "Posible ataque de bot detectado (honeypot)", {
        email
      });
      
      // Simular éxito para no alertar al bot
      await new Promise(resolve => setTimeout(resolve, 1500));
      setLoading(false);
      setError("Error al registrar usuario. Inténtalo de nuevo más tarde.");
      return;
    }

    // Validación básica
    if (password !== confirmPassword) {
      setError("Las contraseñas no coinciden");
      setLoading(false);
      return;
    }

    if (password.length < 6) {
      setError("La contraseña debe tener al menos 6 caracteres");
      setLoading(false);
      return;
    }

    try {
      // Registrar intento de registro (sin la contraseña)
      logSecurityEvent('info', "Intento de registro de usuario", {
        email,
        name
      });

      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          email,
          phone,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Incrementar contador de intentos fallidos
        const newAttempts = signupAttempts + 1;
        setSignupAttempts(newAttempts);
        localStorage.setItem('signupAttempts', newAttempts.toString());
        localStorage.setItem('lastSignupAttempt', Date.now().toString());
        
        // Registrar el error de registro
        logSecurityEvent('warn', "Registro de usuario fallido", {
          email,
          reason: data.message || "Error desconocido",
          statusCode: response.status
        });
        
        // Mensaje de error genérico para evitar enumeración de usuarios
        if (response.status === 409) {
          // 409 Conflict - El correo ya existe
          setError("No se pudo completar el registro. Por favor, verifica tus datos o intenta con otro correo.");
        } else {
          setError("Error al procesar la solicitud. Por favor, inténtalo de nuevo más tarde.");
        }
        setLoading(false);
        return;
      }

      // Registro exitoso - reiniciar contador de intentos
      setSignupAttempts(0);
      localStorage.removeItem('signupAttempts');
      localStorage.removeItem('lastSignupAttempt');

      // Registrar registro exitoso
      logSecurityEvent('info', "Registro de usuario exitoso", {
        email,
        userId: data.user?.id
      });

      // Obtener callbackUrl de los parámetros de búsqueda y validarla
      const callbackUrl = searchParams.get("callbackUrl");
      const safeCallbackUrl = validateRedirectUrl(callbackUrl);
      
      // Redirigir al inicio de sesión con parámetro registered=true
      // y opcionalmente con callbackUrl validada
      const signinUrl = new URL("/auth/signin", window.location.origin);
      signinUrl.searchParams.set("registered", "true");
      
      if (safeCallbackUrl && safeCallbackUrl !== "/") {
        signinUrl.searchParams.set("callbackUrl", safeCallbackUrl);
      }
      
      router.push(signinUrl.toString());
    } catch (error: any) {
      // Incrementar contador de intentos fallidos
      const newAttempts = signupAttempts + 1;
      setSignupAttempts(newAttempts);
      localStorage.setItem('signupAttempts', newAttempts.toString());
      localStorage.setItem('lastSignupAttempt', Date.now().toString());
      
      // Mensaje de error genérico
      setError("Error al procesar la solicitud. Por favor, inténtalo de nuevo más tarde.");
      setLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-white to-orange-100/20 relative">
      
      <div className="w-full max-w-6xl mb-4">
        <Link 
          href="/" 
          className="inline-flex items-center border text-gray-600 hover:text-gray-900 bg-white rounded-lg px-3 py-2 shadow-sm hover:shadow transition-all"
        >
          <FaChevronLeft className="mr-2 h-3.5 w-3.5" />
          Volver al inicio
        </Link>
      </div>
      
      <div className="grid md:grid-cols-12 bg-white border shadow rounded-xl mb-6 w-full max-w-6xl overflow-hidden">
        <div className="bg-white shadow-[1px_0px_15px_0px_rgba(0,0,0,0.1)] p-6 sm:p-6 md:p-8 lg:p-12 rounded-s-xl xl:col-span-5 md:col-span-6">
          <div className="mb-8">
            <Image 
              src={logoDark} 
              alt="Logo Cassiopeia Petrolíferos | Combustibles Cassiopeia" 
              width={224} 
              height={48}
              className="h-auto"
            />
          </div>
          
          <h6 className="text-base font-medium text-gray-600">Crea tu cuenta</h6>
          <p className="text-gray-500 text-sm mt-1 mb-4">¿No tienes una cuenta? Crea tu cuenta, toma menos de un minuto.</p>
          
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
              {error}
            </div>
          )}
          
          <form onSubmit={handleSubmit}>
            {/* Campo honeypot invisible para detectar bots */}
            <div className="opacity-0 absolute top-0 left-0 h-0 w-0 -z-10 overflow-hidden">
              <label className="hidden">
                No llenar este campo
                <input 
                  type="text" 
                  name="website" 
                  tabIndex={-1}
                  autoComplete="off"
                  value={honeypotField}
                  onChange={(e) => setHoneypotField(e.target.value)}
                />
              </label>
            </div>
            
            <div className="mb-2 relative">
              <label className="block text-md font-medium text-gray-600 mb-1" htmlFor="name">
                Nombre
              </label>
              <div className="relative">
                <input 
                  type="text" 
                  placeholder="Tu nombre" 
                  className="py-2 px-4 block w-full border border-gray-300 rounded-lg text-sm focus:border-gray-300 focus:ring-0" 
                  name="name" 
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  maxLength={30}
                />
              </div>
            </div>
            
            <div className="mb-2 relative">
              <label className="block text-md font-medium text-gray-600 mb-1" htmlFor="email">
                Correo electrónico
              </label>
              <div className="relative">
                <input 
                  type="email" 
                  placeholder="<EMAIL>" 
                  className="py-2 px-4 block w-full border border-gray-300 rounded-lg text-sm focus:border-gray-300 focus:ring-0" 
                  name="email" 
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div className="mb-3 relative">
              <label className="block text-sm font-medium text-gray-600 mb-1" htmlFor="phone">
                Teléfono
              </label>
              <div className="relative">
                <input 
                  type="tel" 
                  placeholder="Tu número de teléfono" 
                  className="py-2 px-4 block w-full border border-gray-300 rounded-lg text-sm focus:border-gray-300 focus:ring-0" 
                  name="phone" 
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  minLength={10}
                  maxLength={12}
                  pattern="[0-9]{10,12}"
                  title="El número debe tener entre 10 y 12 dígitos"
                />
              </div>
            </div>
            
            <div className="mb-2">
              <label className="block text-md font-medium text-gray-600 mb-1" htmlFor="password">
                Contraseña
              </label>
              <div className="flex">
                <div className="w-full relative">
                  <input 
                    type={showPassword ? "text" : "password"} 
                    placeholder="Contraseña (mínimo 6 caracteres)" 
                    className="py-2 px-4 block w-full border border-gray-300 rounded-l-lg text-sm focus:border-gray-300 focus:ring-0" 
                    name="password" 
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                  />
                </div>
                <span 
                  className="inline-flex items-center justify-center px-4 border rounded-r-lg -ms-px cursor-pointer"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="20" width="20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"></path>
                      <path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"></path>
                      <path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"></path>
                      <line x1="2" x2="22" y1="2" y2="22"></line>
                    </svg>
                  ) : (
                    <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" height="20" width="20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                  )}
                </span>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-md font-medium text-gray-600 mb-1" htmlFor="confirmPassword">
                Confirmar contraseña
              </label>
              <div className="relative">
                <input 
                  type={showPassword ? "text" : "password"} 
                  placeholder="Confirma tu contraseña" 
                  className="py-2 px-4 block w-full border border-gray-300 rounded-lg text-sm focus:border-gray-300 focus:ring-0" 
                  name="confirmPassword" 
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div className="mb-0 text-center">
              <button 
                type="submit" 
                disabled={loading}
                className="flex text-center items-center justify-center w-full bg-primary text-white leading-6 text-center align-middle select-none py-2 px-4 text-base rounded-lg transition-all hover:shadow-lg hover:shadow-primary/30 disabled:opacity-70"
              >
                <FiUserPlus className="w-4 h-4 mr-2" />
                {loading ? "Registrando..." : "Registrarse"}
              </button>
            </div>
          </form>
        </div>
        
        <div className="hidden md:block xl:col-span-7 md:col-span-6">
          <div className="max-w-[100%] mx-auto">
            <div className="my-6 py-6">
              <div className="flex items-center justify-center h-full">
                <Swiper
                  modules={[Pagination, Autoplay]}
                  pagination={{ clickable: true }}
                  autoplay={{ delay: 5000, disableOnInteraction: false }}
                  loop={true}
                  className="w-full"
                >
                  <SwiperSlide>
                    <div className="swiper-slide-content">
                      <div className="text-center w-4/5 mx-auto">
                        <Image 
                          src={calidad} 
                          alt="Calidad que Impulsa el Cambio Combustibles Cassiopeia" 
                          width={800} 
                          height={547}
                          className="w-full rounded-xl shadow-lg"
                          priority
                        />
                      </div>
                      <div className="text-center my-6 pb-8">
                        <h5 className="font-medium text-lg text-gray-600 my-2.5">Líderes en Abastecimiento de Hidrocarburos de Alta Calidad en México.</h5>
                        <p className="text-md text-gray-500">Crea tu cuenta para acceder a precios exclusivos y más.</p>
                      </div>
                    </div>
                  </SwiperSlide>
                  <SwiperSlide>
                    <div className="swiper-slide-content">
                      <div className="text-center w-4/5 mx-auto">
                        <Image 
                          src={impulsando} 
                          alt="Impulsando un futuro sostenible con eficiencia energética" 
                          width={800} 
                          height={547}
                          className="w-full rounded-xl shadow-lg"
                        />
                      </div>
                      <div className="text-center my-6 pb-8">
                        <h5 className="font-medium text-lg text-gray-600 my-2.5">Impulsando un futuro sostenible</h5>
                        <p className="text-md text-gray-500">Comprometidos a suministrar con integridad, seguridad y innovación.</p>
                      </div>
                    </div>
                  </SwiperSlide>
                  <SwiperSlide>
                    <div className="swiper-slide-content">
                      <div className="text-center w-4/5 mx-auto">
                        <Image 
                          src={recogerack} 
                          alt="Recoge tu combustible directo en terminal (RACK)" 
                          width={800} 
                          height={547}
                          className="w-full rounded-xl shadow-lg"
                        />
                      </div>
                      <div className="text-center my-6 pb-8">
                        <h5 className="font-medium text-lg text-gray-600 my-2.5">Recoge tu Combustible Directo en Terminal</h5>
                        <p className="text-md text-gray-500">Carga directamente en Terminal (RACK) y aprovecha precios preferenciales.</p>
                      </div>
                    </div>
                  </SwiperSlide>
                </Swiper>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="w-full text-center">
        <p className="text-gray-500 leading-6 text-base">
          ¿Ya tienes una cuenta?
          <Link href="/auth/signin" className="text-primary font-semibold ms-1">
            Inicia sesión
          </Link>
        </p>
      </div>
    </div>
  );
}
