"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/authOptions";
import { prisma } from "@/lib/db";
import { z } from "zod";
import { revalidatePath } from "next/cache";

// Esquema de validación para proveedor
const proveedorSchema = z.object({
  nombre: z.string().min(1, "El nombre del proveedor es requerido").max(100, "El nombre no puede exceder 100 caracteres"),
  descripcion: z.string().optional(),
  activo: z.boolean().default(true)
});

const updateProveedorSchema = z.object({
  nombre: z.string().min(1, "El nombre del proveedor es requerido").max(100, "El nombre no puede exceder 100 caracteres").optional(),
  descripcion: z.string().optional(),
  activo: z.boolean().optional()
});

export interface ProveedorData {
  id?: string;
  nombre: string;
  descripcion?: string;
  activo: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Función para verificar permisos
async function checkPermissions() {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.email) {
    throw new Error("No autorizado");
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    select: { id: true, role: true }
  });

  if (!user) {
    throw new Error("Usuario no encontrado");
  }

  // Solo ADMIN, SUPER_ADMIN y WORKER pueden gestionar proveedores
  if (!["ADMIN", "SUPER_ADMIN", "WORKER"].includes(user.role)) {
    throw new Error("No tienes permisos para gestionar proveedores");
  }

  return user;
}

// Obtener todos los proveedores
export async function getProveedores(filters?: {
  activo?: boolean;
  search?: string;
}) {
  try {
    await checkPermissions();

    let whereClause: any = {};
    
    if (filters?.activo !== undefined) {
      whereClause.activo = filters.activo;
    }
    
    if (filters?.search) {
      whereClause.nombre = {
        contains: filters.search,
        mode: 'insensitive'
      };
    }

    const proveedores = await prisma.proveedor.findMany({
      where: whereClause,
      orderBy: [
        { activo: 'desc' }, // Activos primero
        { nombre: 'asc' }   // Luego alfabéticamente
      ]
    });

    return { success: true, data: proveedores };
  } catch (error) {
    console.error("Error al obtener proveedores:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Crear nuevo proveedor
export async function createProveedor(data: Omit<ProveedorData, 'id' | 'createdAt' | 'updatedAt'>) {
  try {
    const user = await checkPermissions();

    // Validar datos
    const validatedData = proveedorSchema.parse(data);

    // Verificar si ya existe un proveedor con ese nombre
    const existingProveedor = await prisma.proveedor.findUnique({
      where: { nombre: validatedData.nombre }
    });

    let proveedor;

    if (existingProveedor) {
      if (existingProveedor.activo) {
        // Si existe y está activo, no permitir duplicado
        return { success: false, error: "Ya existe un proveedor activo con ese nombre" };
      } else {
        // Si existe pero está inactivo, reactivarlo
        proveedor = await prisma.proveedor.update({
          where: { id: existingProveedor.id },
          data: {
            activo: true,
            descripcion: validatedData.descripcion || existingProveedor.descripcion
          }
        });
      }
    } else {
      // Si no existe, crear uno nuevo
      proveedor = await prisma.proveedor.create({
        data: {
          nombre: validatedData.nombre,
          descripcion: validatedData.descripcion,
          activo: validatedData.activo
        }
      });
    }

    revalidatePath('/admin');
    return { success: true, data: proveedor };
  } catch (error) {
    console.error("Error al crear proveedor:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Actualizar proveedor existente
export async function updateProveedor(id: string, data: Partial<Omit<ProveedorData, 'id' | 'createdAt' | 'updatedAt'>>) {
  try {
    const user = await checkPermissions();

    // Validar datos
    const validatedData = updateProveedorSchema.parse(data);

    // Verificar que el proveedor existe
    const existingProveedor = await prisma.proveedor.findUnique({
      where: { id }
    });

    if (!existingProveedor) {
      return { success: false, error: "Proveedor no encontrado" };
    }

    // Si se está actualizando el nombre, verificar que no exista otro proveedor con ese nombre
    if (validatedData.nombre && validatedData.nombre !== existingProveedor.nombre) {
      const duplicateProveedor = await prisma.proveedor.findUnique({
        where: { nombre: validatedData.nombre }
      });

      if (duplicateProveedor) {
        return { success: false, error: "Ya existe un proveedor con ese nombre" };
      }
    }

    // Actualizar el proveedor
    const proveedor = await prisma.proveedor.update({
      where: { id },
      data: validatedData
    });

    revalidatePath('/admin');
    return { success: true, data: proveedor };
  } catch (error) {
    console.error("Error al actualizar proveedor:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Eliminar proveedor
export async function deleteProveedor(id: string) {
  try {
    const user = await checkPermissions();

    // Verificar que el proveedor existe
    const existingProveedor = await prisma.proveedor.findUnique({
      where: { id }
    });

    if (!existingProveedor) {
      return { success: false, error: "Proveedor no encontrado" };
    }

    // En lugar de eliminar físicamente, marcar como inactivo
    const proveedor = await prisma.proveedor.update({
      where: { id },
      data: { activo: false }
    });

    revalidatePath('/admin');
    return { success: true, data: proveedor };
  } catch (error) {
    console.error("Error al eliminar proveedor:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Obtener proveedor específico
export async function getProveedor(id: string) {
  try {
    await checkPermissions();

    const proveedor = await prisma.proveedor.findUnique({
      where: { id }
    });

    if (!proveedor) {
      return { success: false, error: "Proveedor no encontrado" };
    }

    return { success: true, data: proveedor };
  } catch (error) {
    console.error("Error al obtener proveedor:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Activar/desactivar proveedor
export async function toggleProveedorStatus(id: string) {
  try {
    const user = await checkPermissions();

    const existingProveedor = await prisma.proveedor.findUnique({
      where: { id }
    });

    if (!existingProveedor) {
      return { success: false, error: "Proveedor no encontrado" };
    }

    const proveedor = await prisma.proveedor.update({
      where: { id },
      data: { activo: !existingProveedor.activo }
    });

    revalidatePath('/admin');
    return { success: true, data: proveedor };
  } catch (error) {
    console.error("Error al cambiar estado del proveedor:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error interno del servidor"
    };
  }
}

// Eliminar múltiples proveedores por nombre
export async function deleteMultipleProveedores(nombres: string[]) {
  try {
    const user = await checkPermissions();

    if (!nombres || nombres.length === 0) {
      return { success: false, error: "No se proporcionaron proveedores para eliminar" };
    }

    // Verificar que todos los proveedores existen
    const existingProveedores = await prisma.proveedor.findMany({
      where: {
        nombre: { in: nombres },
        activo: true // Solo proveedores activos pueden ser "eliminados"
      }
    });

    if (existingProveedores.length === 0) {
      return { success: false, error: "No se encontraron proveedores activos para eliminar" };
    }

    const foundNames = existingProveedores.map(p => p.nombre);
    const notFoundNames = nombres.filter(nombre => !foundNames.includes(nombre));

    // Marcar como inactivos (eliminación lógica)
    const result = await prisma.proveedor.updateMany({
      where: {
        nombre: { in: foundNames }
      },
      data: { activo: false }
    });

    revalidatePath('/admin');

    return {
      success: true,
      data: {
        eliminados: result.count,
        proveedoresEliminados: foundNames,
        proveedoresNoEncontrados: notFoundNames
      }
    };
  } catch (error) {
    console.error("Error al eliminar múltiples proveedores:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error interno del servidor"
    };
  }
}
