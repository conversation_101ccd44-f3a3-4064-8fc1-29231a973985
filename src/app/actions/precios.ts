"use server";

import { z } from 'zod';
import { prisma, isPrismaAvailable } from '@/lib/prisma';
import { TipoProducto } from '@/app/types/productos';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/authOptions';

// Schema Zod para validación de consultas de precios
const GetPreciosSchema = z
  .object({
    producto: z.enum(
      [
        TipoProducto.GASOLINA_REGULAR,
        TipoProducto.GASOLINA_PREMIUM,
        TipoProducto.DIESEL,
      ],
      { errorMap: () => ({ message: 'Producto inválido.' }) }
    ),
    terminal: z.string().min(1).max(100),
    start: z.string().refine((date) => !isNaN(Date.parse(date)), {
      message: 'Fecha de inicio inválida',
    }),
    end: z.string().refine((date) => !isNaN(Date.parse(date)), {
      message: '<PERSON>cha de fin inválida',
    }),
    page: z.number().int().min(1).default(1),
    pageSize: z.number().int().min(1).max(100).default(20),
  })
  .strict();

export type GetPreciosInput = z.infer<typeof GetPreciosSchema>;

/**
 * Obtiene la lista de productos disponibles
 */
export async function getProductos() {
  const { getProductosArray } = await import('@/app/types/productos');
  return getProductosArray();
}

/**
 * Obtiene registros de precios filtrados por producto, terminal y rango de fechas
 */
export async function getPrecios(params: GetPreciosInput) {
  // Validar parámetros
  const parse = GetPreciosSchema.safeParse(params);
  if (!parse.success) {
    const errores = parse.error.errors.map((e) => `${e.path.join('.')}: ${e.message}`);
    throw new Error(`Error de validación: ${errores.join(', ')}`);
  }
  const { producto, terminal: terminalNombre, start, end } = parse.data;

  // Verificar sesión
  const session = await getServerSession(authOptions);
  if (!session) {
    return { success: false, error: 'Debes iniciar sesión para consultar precios', requiresAuth: true, data: [] };
  }

  // Verificar disponibilidad de la base de datos
  if (!isPrismaAvailable() || !prisma) {
    throw new Error('Base de datos no disponible');
  }

  // Procesar fechas
  const inicio = new Date(start);
  inicio.setHours(0, 0, 0, 0);
  const fin = new Date(end);
  fin.setHours(23, 59, 59, 999);

  // Buscar terminal
  const terminalObj = await prisma.terminal.findUnique({ where: { nombre: terminalNombre.toLowerCase() } });
  if (!terminalObj) {
    throw new Error(`Terminal "${terminalNombre}" no encontrada`);
  }

  // Consultar precios en la base de datos
  const precios = await prisma.precio.findMany({
    where: { producto, terminalId: terminalObj.id, fecha: { gte: inicio, lte: fin } },
    orderBy: { fecha: 'asc' },
    include: { terminal: true },
  });

  // Formatear resultados
  const resultados = precios.map((p) => ({
    id: p.id,
    producto: p.producto,
    terminal: p.terminal.nombre,
    fecha: p.fecha,
    valor: p.valor,
  }));

  return { success: true, data: resultados };
}
