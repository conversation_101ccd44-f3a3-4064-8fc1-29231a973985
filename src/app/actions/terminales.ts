'use server';

import { prisma, isPrismaAvailable } from '@/lib/prisma';

// Terminales predeterminadas para usar cuando no se puede acceder a la base de datos
// Nombres en minúsculas para mantener consistencia con la base de datos
const TERMINALES_PREDETERMINADAS = [
  { id: '1', nombre: 'axfaltec' },
  { id: '2', nombre: 'cadereyta' },
  { id: '3', nombre: 'escamela' },
  { id: '4', nombre: 'madero' },
  { id: '5', nombre: 'pajaritos' },
  { id: '6', nombre: 'progreso' },
  { id: '7', nombre: 'valero' },
  { id: '8', nombre: 'tizayuca' },
  { id: '9', nombre: 'tuxpan' },
  { id: '10', nombre: 'tula' },
  { id: '11', nombre: 'vopak' },
  { id: '12', nombre: 'villahermosa' }
];

/**
 * Obtiene todas las terminales activas
 */
export async function getTerminales() {
  console.log('[getTerminales] Iniciando consulta de terminales');

  // Verificar si Prisma está disponible
  if (!isPrismaAvailable() || !prisma) {
    console.warn('[getTerminales] Prisma no está disponible, devolviendo terminales predeterminadas');
    return TERMINALES_PREDETERMINADAS;
  }

  try {
    // Consultar todas las terminales sin filtros para depuración
    const todasTerminales = await prisma.terminal.findMany();
    console.log(`[getTerminales] Total de terminales en la base de datos: ${todasTerminales.length}`);

    if (todasTerminales.length === 0) {
      console.warn('[getTerminales] No se encontraron terminales en la base de datos, devolviendo predeterminadas');
      return TERMINALES_PREDETERMINADAS;
    }

    // Filtrar terminales activas
    const terminalesActivas = todasTerminales.filter((t) => t.activo);
    console.log(`[getTerminales] Terminales activas: ${terminalesActivas.length}`);

    if (terminalesActivas.length === 0) {
      console.warn('[getTerminales] No se encontraron terminales activas, devolviendo todas las terminales');
      // Si no hay terminales activas, devolver todas las terminales
      return todasTerminales.map((t) => ({
        id: t.id,
        nombre: t.nombre
      }));
    }

    // Formatear la respuesta
    const resultado = terminalesActivas.map((t) => ({
      id: t.id,
      nombre: t.nombre
    }));

    // Ordenar por nombre
    resultado.sort((a, b) => a.nombre.localeCompare(b.nombre));

    return resultado;
  } catch (error: any) {
    console.error('[getTerminales] Error detallado:', error);
    // En caso de error, devolver terminales predeterminadas
    console.warn('[getTerminales] Devolviendo terminales predeterminadas debido a un error');
    return TERMINALES_PREDETERMINADAS;
  }
}
