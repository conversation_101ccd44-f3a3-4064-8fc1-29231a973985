"use server";

import { prisma } from "@/lib/db";
import { getSession } from "@/lib/auth";
import { isValidArea, type AreaType } from "@/lib/areaConstants";

// Tipo para los datos de área desde la base de datos
export interface AreaData {
  id: string;
  nombre: string;
  displayName: string;
  descripcion: string | null;
  activo: boolean;
  orden: number;
  icono: string | null;
  color: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Tipo para los datos de usuario con área
export interface UserWithArea {
  id: string;
  name: string | null;
  email: string;
  role: string;
  areaId: string | null;
  area: AreaData | null;
}

/**
 * Obtiene todas las áreas activas ordenadas
 */
export async function getAreas(): Promise<{ success: boolean; data?: AreaData[]; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user) {
      return { success: false, error: "No autenticado" };
    }

    const areas = await prisma.area.findMany({
      where: { activo: true },
      orderBy: { orden: 'asc' }
    });

    return { success: true, data: areas };
  } catch (error) {
    console.error("Error al obtener áreas:", error);
    return { success: false, error: "Error interno del servidor" };
  }
}

/**
 * Obtiene un área específica por su nombre
 */
export async function getAreaByName(nombre: string): Promise<{ success: boolean; data?: AreaData; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user) {
      return { success: false, error: "No autenticado" };
    }

    if (!isValidArea(nombre)) {
      return { success: false, error: "Área no válida" };
    }

    const area = await prisma.area.findUnique({
      where: { nombre }
    });

    if (!area) {
      return { success: false, error: "Área no encontrada" };
    }

    return { success: true, data: area };
  } catch (error) {
    console.error("Error al obtener área:", error);
    return { success: false, error: "Error interno del servidor" };
  }
}

/**
 * Asigna un área a un usuario (solo ADMIN y SUPER_ADMIN pueden hacer esto)
 */
export async function assignUserToArea(userId: string, areaId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user) {
      return { success: false, error: "No autenticado" };
    }

    // Verificar permisos - solo ADMIN y SUPER_ADMIN pueden asignar áreas
    if (!["ADMIN", "SUPER_ADMIN"].includes(session.user.role)) {
      return { success: false, error: "No tienes permisos para asignar áreas" };
    }

    // Verificar que el área existe
    const area = await prisma.area.findUnique({
      where: { id: areaId, activo: true }
    });

    if (!area) {
      return { success: false, error: "Área no encontrada o inactiva" };
    }

    // Verificar que el usuario existe
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return { success: false, error: "Usuario no encontrado" };
    }

    // Asignar el área al usuario
    await prisma.user.update({
      where: { id: userId },
      data: { areaId }
    });

    return { success: true };
  } catch (error) {
    console.error("Error al asignar área:", error);
    return { success: false, error: "Error interno del servidor" };
  }
}

/**
 * Remueve el área asignada a un usuario
 */
export async function removeUserFromArea(userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user) {
      return { success: false, error: "No autenticado" };
    }

    // Verificar permisos - solo ADMIN y SUPER_ADMIN pueden remover áreas
    if (!["ADMIN", "SUPER_ADMIN"].includes(session.user.role)) {
      return { success: false, error: "No tienes permisos para remover áreas" };
    }

    // Remover el área del usuario
    await prisma.user.update({
      where: { id: userId },
      data: { areaId: null }
    });

    return { success: true };
  } catch (error) {
    console.error("Error al remover área:", error);
    return { success: false, error: "Error interno del servidor" };
  }
}

/**
 * Obtiene todos los usuarios con sus áreas asignadas
 */
export async function getUsersWithAreas(): Promise<{ success: boolean; data?: UserWithArea[]; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user) {
      return { success: false, error: "No autenticado" };
    }

    // Verificar permisos - solo ADMIN y SUPER_ADMIN pueden ver todos los usuarios
    if (!["ADMIN", "SUPER_ADMIN"].includes(session.user.role)) {
      return { success: false, error: "No tienes permisos para ver usuarios" };
    }

    const users = await prisma.user.findMany({
      include: {
        area: true
      },
      orderBy: [
        { role: 'asc' },
        { name: 'asc' }
      ]
    });

    return { success: true, data: users };
  } catch (error) {
    console.error("Error al obtener usuarios con áreas:", error);
    return { success: false, error: "Error interno del servidor" };
  }
}

/**
 * Obtiene usuarios de un área específica
 */
export async function getUsersByArea(areaId: string): Promise<{ success: boolean; data?: UserWithArea[]; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user) {
      return { success: false, error: "No autenticado" };
    }

    const users = await prisma.user.findMany({
      where: { areaId },
      include: {
        area: true
      },
      orderBy: [
        { role: 'asc' },
        { name: 'asc' }
      ]
    });

    return { success: true, data: users };
  } catch (error) {
    console.error("Error al obtener usuarios por área:", error);
    return { success: false, error: "Error interno del servidor" };
  }
}

/**
 * Obtiene el área del usuario actual
 */
export async function getCurrentUserArea(): Promise<{ success: boolean; data?: AreaData; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return { success: false, error: "No autenticado" };
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { area: true }
    });

    if (!user) {
      return { success: false, error: "Usuario no encontrado" };
    }

    if (!user.area) {
      return { success: false, error: "Usuario sin área asignada" };
    }

    return { success: true, data: user.area };
  } catch (error) {
    console.error("Error al obtener área del usuario:", error);
    return { success: false, error: "Error interno del servidor" };
  }
}
