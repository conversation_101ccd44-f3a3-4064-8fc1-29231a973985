"use server";

import { z } from 'zod';
import { prisma } from '@/lib/prisma';

// Esquema de validación para obtener una tarifa por distancia
const ObtenerTarifaPorDistanciaSchema = z.object({
  distancia: z.number().min(0, 'La distancia debe ser un número positivo'),
});

export type ObtenerTarifaPorDistanciaInput = z.infer<typeof ObtenerTarifaPorDistanciaSchema>;

/**
 * Obtiene todas las tarifas por kilómetro
 */
export async function obtenerTarifasPorKm() {
  try {
    console.log('Obteniendo tarifas por kilómetro desde la base de datos...');

    // Verificar si el modelo TarifaKm existe en el cliente de Prisma
    if (!prisma.tarifaKm) {
      console.error('El modelo TarifaKm no está disponible en el cliente de Prisma');
      return {
        success: false,
        error: 'Modelo no disponible',
        data: {}
      };
    }

    const tarifas = await prisma.tarifaKm.findMany({
      orderBy: { distancia: 'asc' }
    });

    console.log(`Se encontraron ${tarifas.length} tarifas en la base de datos`);

    // Convertir a formato Record<string, number> para mantener compatibilidad
    const tarifasMap: Record<string, number> = {};
    tarifas.forEach(tarifa => {
      tarifasMap[tarifa.distancia.toString()] = tarifa.tarifa;
    });

    return { success: true, data: tarifasMap };
  } catch (error: any) {
    console.error('[obtenerTarifasPorKm] Error:', error);
    return {
      success: false,
      error: `Error al obtener tarifas: ${error.message}`,
      data: {}
    };
  }
}

/**
 * Encuentra la tarifa correspondiente a una distancia específica
 * Devuelve la tarifa del rango más cercano por debajo de la distancia dada
 */
export async function obtenerTarifaPorDistancia(params: ObtenerTarifaPorDistanciaInput) {
  try {
    // Validar los parámetros de entrada
    const parseResult = ObtenerTarifaPorDistanciaSchema.safeParse(params);
    if (!parseResult.success) {
      const errores = parseResult.error.errors.map((e) => `${e.path.join('.')}: ${e.message}`);
      return {
        success: false,
        error: `Error de validación: ${errores.join(', ')}`,
        data: null
      };
    }

    const { distancia } = params;

    // Verificar si el modelo TarifaKm existe en el cliente de Prisma
    if (!prisma.tarifaKm) {
      console.error('El modelo TarifaKm no está disponible en el cliente de Prisma');
      return {
        success: false,
        error: 'Modelo no disponible',
        data: null
      };
    }

    // Obtener todas las tarifas
    const tarifas = await prisma.tarifaKm.findMany({
      where: {
        distancia: {
          lte: distancia
        }
      },
      orderBy: { distancia: 'desc' },
      take: 1
    });

    if (tarifas.length === 0) {
      // Si no hay tarifas para la distancia dada, obtener la tarifa mínima
      const tarifaMinima = await prisma.tarifaKm.findFirst({
        orderBy: { distancia: 'asc' }
      });

      if (!tarifaMinima) {
        return {
          success: false,
          error: 'No hay tarifas disponibles',
          data: null
        };
      }

      return { success: true, data: tarifaMinima };
    }

    return { success: true, data: tarifas[0] };
  } catch (error: any) {
    console.error('[obtenerTarifaPorDistancia] Error:', error);
    return {
      success: false,
      error: `Error al obtener tarifa por distancia: ${error.message}`,
      data: null
    };
  }
}


