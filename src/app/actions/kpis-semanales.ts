"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/authOptions";
import { prisma } from "@/lib/db";
import { z } from "zod";
import { revalidatePath } from "next/cache";

// Esquema de validación para crear/actualizar KPIs semanales
const kpiSemanalSchema = z.object({
  year: z.number().int().min(2020).max(2030),
  weekNumber: z.number().int().min(1).max(53),
  weekStartDate: z.string().datetime(),
  weekEndDate: z.string().datetime(),
  volumenTotalLitros: z.number().min(0),
  crecimientoMensual: z.number(),
  margenBrutoPorLitro: z.number().min(0),
  tasaRetencionClientes: z.number().min(0).max(100),
  cumplimientoObjetivo: z.number().min(0).max(200),
  desviacionVentas: z.number(),
  cicloPromedioCierre: z.number().int().min(0),
  clientesActivosMensuales: z.number().int().min(0)
});

const updateKpiSemanalSchema = z.object({
  volumenTotalLitros: z.number().min(0).optional(),
  crecimientoMensual: z.number().optional(),
  margenBrutoPorLitro: z.number().min(0).optional(),
  tasaRetencionClientes: z.number().min(0).max(100).optional(),
  cumplimientoObjetivo: z.number().min(0).max(200).optional(),
  desviacionVentas: z.number().optional(),
  cicloPromedioCierre: z.number().int().min(0).optional(),
  clientesActivosMensuales: z.number().int().min(0).optional()
});

export interface KpiSemanalData {
  id?: string;
  year: number;
  weekNumber: number;
  weekStartDate: string;
  weekEndDate: string;
  volumenTotalLitros: number;
  crecimientoMensual: number;
  margenBrutoPorLitro: number;
  tasaRetencionClientes: number;
  cumplimientoObjetivo: number;
  desviacionVentas: number;
  cicloPromedioCierre: number;
  clientesActivosMensuales: number;
}

// Verificar permisos
async function checkPermissions(allowedRoles: string[] = ["ADMIN", "SUPER_ADMIN", "WORKER"]) {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    throw new Error("No autenticado");
  }

  const userRole = session.user.role;
  if (!userRole || !allowedRoles.includes(userRole)) {
    throw new Error("Acceso denegado");

  }

  return session.user;
}

// Obtener KPIs semanales
export async function getKpisSemanales(filters?: {
  year?: number;
  weekNumber?: number;
  limit?: number;
}) {
  try {
    await checkPermissions();

    let whereClause: any = {};
    
    if (filters?.year) {
      whereClause.year = filters.year;
    }
    
    if (filters?.weekNumber) {
      whereClause.weekNumber = filters.weekNumber;
    }

    const kpis = await prisma.kpiSemanal.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ],
      take: filters?.limit || undefined
    });

    return { success: true, data: kpis };
  } catch (error) {
    console.error("Error al obtener KPIs semanales:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Crear nuevo KPI semanal
export async function createKpiSemanal(data: KpiSemanalData) {
  try {
    const user = await checkPermissions();

    // Validar datos
    const validatedData = kpiSemanalSchema.parse(data);

    // Verificar si ya existe un KPI para esa semana
    const existingKpi = await prisma.kpiSemanal.findUnique({
      where: {
        year_weekNumber: {
          year: validatedData.year,
          weekNumber: validatedData.weekNumber
        }
      }
    });

    if (existingKpi) {
      return {
        success: false,
        error: "Ya existe un KPI para esta semana"
      };
    }

    // Crear el KPI semanal
    const kpiSemanal = await prisma.kpiSemanal.create({
      data: {
        ...validatedData,
        weekStartDate: new Date(validatedData.weekStartDate),
        weekEndDate: new Date(validatedData.weekEndDate),
        userId: user.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, data: kpiSemanal };
  } catch (error) {
    console.error("Error al crear KPI semanal:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Datos inválidos",
        details: error.errors
      };
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Obtener KPI semanal específico
export async function getKpiSemanal(id: string) {
  try {
    await checkPermissions();

    const kpi = await prisma.kpiSemanal.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!kpi) {
      return { success: false, error: "KPI no encontrado" };
    }

    return { success: true, data: kpi };
  } catch (error) {
    console.error("Error al obtener KPI semanal:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Actualizar KPI semanal
export async function updateKpiSemanal(id: string, data: Partial<KpiSemanalData>) {
  try {
    const user = await checkPermissions();

    // Verificar que el KPI existe
    const existingKpi = await prisma.kpiSemanal.findUnique({
      where: { id }
    });

    if (!existingKpi) {
      return { success: false, error: "KPI no encontrado" };
    }

    // Solo el creador, admin o super_admin pueden actualizar
    if (user.role === "WORKER" && existingKpi.userId !== user.id) {
      return { success: false, error: "No tienes permisos para actualizar este KPI" };
    }

    // Validar datos
    const validatedData = updateKpiSemanalSchema.parse(data);

    // Actualizar el KPI semanal
    const updatedKpi = await prisma.kpiSemanal.update({
      where: { id },
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, data: updatedKpi };
  } catch (error) {
    console.error("Error al actualizar KPI semanal:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Datos inválidos",
        details: error.errors
      };
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Eliminar KPI semanal
export async function deleteKpiSemanal(id: string) {
  try {
    await checkPermissions(["ADMIN", "SUPER_ADMIN"]); // Solo admins pueden eliminar

    // Verificar que el KPI existe
    const existingKpi = await prisma.kpiSemanal.findUnique({
      where: { id }
    });

    if (!existingKpi) {
      return { success: false, error: "KPI no encontrado" };
    }

    // Eliminar el KPI semanal
    await prisma.kpiSemanal.delete({
      where: { id }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, message: "KPI eliminado exitosamente" };
  } catch (error) {
    console.error("Error al eliminar KPI semanal:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}
