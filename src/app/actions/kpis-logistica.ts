"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/authOptions";
import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { z } from "zod";

// Esquema de validación para crear/actualizar KPIs semanales de logística
const kpiLogisticaSchema = z.object({
  year: z.number().int().min(2020).max(2030),
  weekNumber: z.number().int().min(1).max(53),
  weekStartDate: z.string().datetime(),
  weekEndDate: z.string().datetime(),
  unidadesConfirmadas: z.number().int().min(0),
  unidadesSolicitadas: z.number().int().min(0),
  porcentajeEntregasTiempo: z.number().min(0).max(100),
  porcentajeRetardos: z.number().min(0).max(500),
  porcentajeReprogramaciones: z.number().min(0).max(500),
  promedioKmOperacion: z.number().min(0),
  promedioCostoFleteLitro: z.number().min(0),
  promedioCostoFleteOperacion: z.number().min(0),
  pagoSemanalFlete: z.number().min(0),
  pagoSemanalPenalizaciones: z.number().min(0),
  porcentajeRutasCotizadas: z.number().min(0).max(100),
  porcentajeTransportistas: z.number().min(0).max(100)
});

const updateKpiLogisticaSchema = z.object({
  unidadesConfirmadas: z.number().int().min(0).optional(),
  unidadesSolicitadas: z.number().int().min(0).optional(),
  porcentajeEntregasTiempo: z.number().min(0).max(100).optional(),
  porcentajeRetardos: z.number().min(0).max(500).optional(),
  porcentajeReprogramaciones: z.number().min(0).max(500).optional(),
  promedioKmOperacion: z.number().min(0).optional(),
  promedioCostoFleteLitro: z.number().min(0).optional(),
  promedioCostoFleteOperacion: z.number().min(0).optional(),
  pagoSemanalFlete: z.number().min(0).optional(),
  pagoSemanalPenalizaciones: z.number().min(0).optional(),
  porcentajeRutasCotizadas: z.number().min(0).max(100).optional(),
  porcentajeTransportistas: z.number().min(0).max(100).optional()
});

export interface KpiLogisticaData {
  id?: string;
  year: number;
  weekNumber: number;
  weekStartDate: string;
  weekEndDate: string;
  unidadesConfirmadas: number;
  unidadesSolicitadas: number;
  porcentajeEntregasTiempo: number;
  porcentajeRetardos: number;
  porcentajeReprogramaciones: number;
  promedioKmOperacion: number;
  promedioCostoFleteLitro: number;
  promedioCostoFleteOperacion: number;
  pagoSemanalFlete: number;
  pagoSemanalPenalizaciones: number;
  porcentajeRutasCotizadas: number;
  porcentajeTransportistas: number;
  createdAt?: string;
  updatedAt?: string;
  user?: {
    id: string;
    name?: string | null;
    email?: string | null;
  };
}

// Verificar permisos
async function checkPermissions(allowedRoles: string[] = ["ADMIN", "SUPER_ADMIN", "WORKER"]) {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    throw new Error("No autenticado");
  }

  const userRole = session.user.role;
  if (!userRole || !allowedRoles.includes(userRole)) {
    throw new Error("Acceso denegado");
  }

  return session.user;
}

// Obtener KPIs semanales de logística
export async function getKpisLogistica(filters?: {
  year?: number;
  weekNumber?: number;
  limit?: number;
}) {
  try {
    await checkPermissions();

    let whereClause: any = {};
    
    if (filters?.year) {
      whereClause.year = filters.year;
    }
    
    if (filters?.weekNumber) {
      whereClause.weekNumber = filters.weekNumber;
    }

    const kpis = await prisma.kpiLogistica.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ],
      take: filters?.limit || undefined
    });

    return { success: true, data: kpis };
  } catch (error) {
    console.error("Error al obtener KPIs de logística:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Crear KPI semanal de logística
export async function createKpiLogistica(data: KpiLogisticaData) {
  try {
    const user = await checkPermissions();

    // Validar datos
    const validatedData = kpiLogisticaSchema.parse(data);

    // Verificar que no exista ya un KPI para esa semana
    const existingKpi = await prisma.kpiLogistica.findUnique({
      where: {
        year_weekNumber: {
          year: validatedData.year,
          weekNumber: validatedData.weekNumber
        }
      }
    });

    if (existingKpi) {
      return { success: false, error: "Ya existe un KPI para esa semana" };
    }

    // Crear el KPI semanal de logística
    const kpiLogistica = await prisma.kpiLogistica.create({
      data: {
        year: validatedData.year,
        weekNumber: validatedData.weekNumber,
        weekStartDate: new Date(validatedData.weekStartDate),
        weekEndDate: new Date(validatedData.weekEndDate),
        unidadesConfirmadas: validatedData.unidadesConfirmadas,
        unidadesSolicitadas: validatedData.unidadesSolicitadas,
        porcentajeEntregasTiempo: validatedData.porcentajeEntregasTiempo,
        porcentajeRetardos: validatedData.porcentajeRetardos,
        porcentajeReprogramaciones: validatedData.porcentajeReprogramaciones,
        promedioKmOperacion: validatedData.promedioKmOperacion,
        promedioCostoFleteLitro: validatedData.promedioCostoFleteLitro,
        promedioCostoFleteOperacion: validatedData.promedioCostoFleteOperacion,
        pagoSemanalFlete: validatedData.pagoSemanalFlete,
        pagoSemanalPenalizaciones: validatedData.pagoSemanalPenalizaciones,
        porcentajeRutasCotizadas: validatedData.porcentajeRutasCotizadas,
        porcentajeTransportistas: validatedData.porcentajeTransportistas,
        userId: user.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, data: kpiLogistica };
  } catch (error) {
    console.error("Error al crear KPI de logística:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Datos inválidos",
        details: error.errors
      };
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Actualizar KPI semanal de logística
export async function updateKpiLogistica(id: string, data: Partial<KpiLogisticaData>) {
  try {
    await checkPermissions();

    // Verificar que el KPI existe
    const existingKpi = await prisma.kpiLogistica.findUnique({
      where: { id }
    });

    if (!existingKpi) {
      return { success: false, error: "KPI no encontrado" };
    }

    // Validar datos
    const validatedData = updateKpiLogisticaSchema.parse(data);

    // Actualizar el KPI semanal de logística
    const updatedKpi = await prisma.kpiLogistica.update({
      where: { id },
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, data: updatedKpi };
  } catch (error) {
    console.error("Error al actualizar KPI de logística:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Datos inválidos",
        details: error.errors
      };
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Eliminar KPI semanal de logística
export async function deleteKpiLogistica(id: string) {
  try {
    await checkPermissions(["ADMIN", "SUPER_ADMIN"]); // Solo admins pueden eliminar

    // Verificar que el KPI existe
    const existingKpi = await prisma.kpiLogistica.findUnique({
      where: { id }
    });

    if (!existingKpi) {
      return { success: false, error: "KPI no encontrado" };
    }

    // Eliminar el KPI semanal de logística
    await prisma.kpiLogistica.delete({
      where: { id }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, message: "KPI eliminado exitosamente" };
  } catch (error) {
    console.error("Error al eliminar KPI de logística:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}
