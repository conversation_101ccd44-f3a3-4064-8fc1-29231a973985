import type { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        // Regla para todos los crawlers
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/auth/',
          '/auth/signin',
          '/auth/signup',
          '/auth/error',
          '/auth/iniciar-sesion',
          '/auth/registrarse',
          '/tarifas/',
          '/dashboard/',
          '/types/',
          '/login/',
          '/register/',
          '/logout/',
          '/error/',
          '/iniciar-sesion/',
          '/registrarse/',
          '/operaciones/',
          '/private/',
          '/search',           // bloquea /search?q=
        ],
        // Para Bing/Yandex que lo soporten, podrías descomentar:
        // crawlDelay: 10
      },
      {
        // Reglas específicas (pueden heredar las mismas disallow si lo deseas)
        userAgent: ['Bingbot', 'Yandex'],
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/auth/',
          '/auth/signin',
          '/auth/signup',
          '/auth/error',
          '/auth/iniciar-sesion',
          '/auth/registrarse',
          '/tarifas/',
          '/dashboard/',
          '/types/',
          '/login/',
          '/register/',
          '/error/',
          '/logout/',
          '/iniciar-sesion/',
          '/registrarse/',
          '/operaciones/',
          '/private/',
        ],
      },
    ],
    // Next.js genera este endpoint en /robots.txt
    sitemap: 'https://cassiopeiamx.com/sitemap.xml',
    // host: 'cassiopeiamx.com', // opcional, soportado por Next.js
  }
}
