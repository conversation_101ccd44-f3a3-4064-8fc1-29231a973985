const Features1 = ({ features }) => {
  return (
    <div className="py-10">
      <div className="container">
        <div className="text-center">
          <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
            Compromiso y Calidad
          </span>
          <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
            Comprometidos a suministrar con integridad, seguridad y innovación
          </h1>
          <p className="text-base md:text-base lg:text-xl text-gray-500">
            Empresa con 7 años de experiencia ofreciendo confianza y resultados.
          </p>
        </div>
        <div className="grid lg:grid-cols-3 lg:gap-6 gap-10 mt-16">
          {(features ?? []).map((feature, idx) => {
            return (
              <div data-aos="fade-up" data-aos-duration={300} key={idx}>
                <div
                  className={`h-12 w-12 rounded-md ${feature.variant} flex items-center justify-center`}
                >
                  {feature.icon}
                </div>
                <h1 className="mb-3 mt-4 text-xl md:text-xl lg:text-2xl">{feature.title}</h1>
                <p className="text-gray-500 text-base md:text-base lg:text-lg">{feature.description}</p>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default Features1
