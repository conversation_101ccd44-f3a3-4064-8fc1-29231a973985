import Image from 'next/image'
import { FaArrowRight } from 'react-icons/fa6'

import nosotros from '@/assets/images/nosotros/nosotros.png'

const Features2 = () => {
  return (
    <div className="py-16 lg:py-32 overflow-x-hidden" data-aos="fade-up">
      <div className="container">
        <div className="relative">
          <div className="lg:absolute lg:max-w-md lg:translate-y-1/2 z-10 mb-14">
            <div className="bg-white shadow-lg border rounded-lg p-4 w-full">
              <div className="relative">
                <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                <h4 className="font-medium my-2 text-xl md:text-xl lg:text-2xl">
                  En Cassiopeia Petrolíferos
                  <br />
                </h4>
                <p className="text-gray-500 text-sm md:text-sm lg:text-md">
                  Ofrecemos Combustibles confiables y de alta calidad, garantizando un desempeño superior y la máxima seguridad en tus operaciones.
                </p>
                <div className="mt-5 flex items-center">
                  <a
                    href="https://api.whatsapp.com/send/?phone=5559513012"
                    target="_blank"
                    className="text-primary text-sm md:text-sm lg:text-md inline-flex items-center gap-2"
                  >
                    Solicita una cotización <FaArrowRight />
                  </a>
                </div>
              </div>
            </div>
          </div>
          <div className="relative">
            <Image
              alt="Equipo Profesional de Cassiopeia Petrolíferos | Expertos en Energía y Combustibles."
              width={800}
              height={530}
              src={nosotros}
              className="ms-auto rounded-2xl transition-transform duration-300"
            />
            <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default Features2
