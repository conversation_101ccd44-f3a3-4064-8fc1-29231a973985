import Image from 'next/image'

//images
import whiteWave from '@/assets/images/shapes/white-wave.svg'
import impulsando from '@/assets/images/hero/impulsando-un-futuro.jpg'

const Hero = () => {
  return (
    <section
      className="relative"
      style={{
        background:
          'linear-gradient(rgba(255,165,0,.07) 0, rgba(255,165,0,.05) 100%)',
      }}
    >
      <section className="relative pt-36 pb-24">
        <div className="container">
          <div className="grid lg:grid-cols-7 grid-cols-1 gap-16 items-center">
            <div className="lg:col-span-4" >
              <div className="relative sm:-ml-2 md:-ml-5 lg:-ml-65 2xl:-ml-10 2xl:min-w-[130%] lg:w-[113%] w-full">
                <div style={{ WebkitMaskImage: 'radial-gradient(ellipse 215% 270%,rgba(0, 0, 0, 1) 22%, transparent 36%)'
                }}>
                  <Image
                    width={618}
                    height={495}
                    loading="lazy"
                    placeholder="blur"
                    src={impulsando}
                    alt="Eficiencia Energética con Combustibles Cassiopeia | Soluciones Sustentables para la Industria."
                    className="rounded-3xl"
                  />
                </div>
              </div>
            </div>
            <div className="lg:col-span-3">
              <div className="text-center sm:text-start">
                <h1 className="text-3xl/snug sm:text-4xl/snug xl:text-5xl/snug font-semibold mb-7">
                  Impulsando un futuro sostenible con&nbsp;
                  <span className="relative after:bg-orange-500/50 after:-z-10 after:absolute after:h-6 after:w-full after:bottom-0 after:start-0 after:end-0">
                  eficiencia
                  </span>
                  energética
                </h1>
                <p className="text-gray-500 text-2xl">
                  Tu respuesta segura para tus necesidades de combustibles y energía.
                </p>
                <div className="flex gap-3 mt-10">
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <div className="absolute bottom-0 inset-x-0 hidden sm:block">
        <Image
          src={whiteWave}
          alt="white-wave-svg"
          className="w-full -scale-x-100 -scale-y-100"
        />
      </div>
    </section>
  )
}

export default Hero
