import Image from 'next/image'
import { FaArrowRight } from 'react-icons/fa6'

//images
import whiteWave from '@/assets/images/shapes/white-wave.svg'
import servicios from '@/assets/images/nosotros/servicios-cassiopeia.png'

const Features4 = () => {
  return (
    <section className="bg-gradient-to-r from-gray-100/70 to-gray-100/50 relative py-16 lg:py-32">
      <div className="absolute top-0 inset-x-0 hidden sm:block">
        <Image
          src={whiteWave}
          alt="white-wave-svg"
          className="w-full -scale-x-100"
        />
      </div>
      <div className="container">
        <div
          className="grid lg:grid-cols-2 grid-cols-1 gap-14 items-center"
          data-aos="fade-up"
        >
          {/* Texto - primero en móvil (order-1), segundo en desktop (lg:order-2) */}
          <div className="order-1 lg:order-2">
            <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mb-5">
              Soluciones de combustible y logística confiable para empresas
            </h1>
            <p className="text-base md:text-base lg:text-xl text-gray-500">
              Ofrecemos soluciones integrales en combustible y logística gracias a una amplia red de proveedores y alianzas estratégicas. Con experiencia que garantiza un suministro confiable y cumplimiento regulatorio.
            </p>
            <div>
              <a
                href="https://api.whatsapp.com/send/?phone=5559513012"
                target="_blank"
                className="bg-primary text-white rounded-lg text-sm inline-flex gap-2 items-center shadow-lg shadow-primary/30 focus:shadow-none focus:outline focus:outline-primary/40 px-8 py-3 mt-8"
              >
                Nuestros Servicios <FaArrowRight />
              </a>
            </div>
          </div>
          {/* Imagen - segundo en móvil (order-2), primero en desktop (lg:order-1) */}
          <div className="order-2 lg:order-1">
            <Image
              width={515}
              height={326}
              src={servicios}
              alt="Servicios de Alta Calidad en Combustibles | Cassiopeia Petrolíferos" 
              className="shadow rounded-2xl"
            />
          </div>
        </div>
      </div>
    </section>
  )
}

export default Features4
