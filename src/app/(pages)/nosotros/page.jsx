import Features1 from './Features1'
import Features2 from './Features2'
import Features3 from './Features3'
import Features4 from './Features4'
import Hero from './Hero'
import Footer2 from '@/components/common/Footer/Footer2'

// data
import { features } from './data'

export const metadata = {
  title: 'Nosotros',
  description: 'Impulsando un Futuro Sostenible con Eficiencia energética',
  Images: [
    {
      url: '@/assets/images/hero/impulsando-un-futuro.png',
      alt: 'Impulsando un futuro sostenible con eficiencia energética',
    },
  ],
  openGraph: {
    title: 'Nosotros',
    description: 'Impulsando un Futuro Sostenible con Eficiencia energética',
    Images: [
      {
        url: '@/assets/images/hero/impulsando-un-futuro.png',
        alt: 'Impulsando un futuro sostenible con eficiencia energética',
      },
    ],
  },
  url: "https://cassiopeiamx.com/nosotros",
  locale: 'es_MX',
  type: 'website',
}

export const revalidate = 3600

const Marketing = () => {
  return (
    <div className="text-gray-700">
      <Hero />

      <section className="py-16 lg:py-32">
        <Features1 features={features} />
        <Features2 />
        <Features4/>
      </section>

      <Features3 />

      <Footer2 />
    </div>
  )
}

export default Marketing
