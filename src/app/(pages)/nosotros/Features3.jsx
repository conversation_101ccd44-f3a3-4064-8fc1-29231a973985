import { FaArrowRight } from 'react-icons/fa6'
import { Zap } from 'lucide-react'

const checkIcon = (
  <svg
    className="h-5 w-5 text-green-500"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
    <polyline points="22 4 12 14.01 9 11.01" />
  </svg>
)

const Features3 = () => {
  return (
    <div className="pt-8 pb-28 lg:pb-32">
      <div className="container">
        <div
          className="grid lg:grid-cols-3 grid-cols-1 gap-10 items-center"
          data-aos="fade-up"
        >
          <div className="lg:col-span-2">
            <span className="h-14 w-14 bg-yellow-600/10 rounded-lg flex items-center justify-center">
              <Zap className="h-7 w-7 text-yellow-500" />
            </span>
            <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-5 mb-4 max-w-lg">
              Expertos en suministro de combustible y más
            </h1>
            <p className="text-base md:text-base lg:text-xl text-gray-500">
              Suministro de combustible en Centro, Occidente,
              Golfo y Sureste de México. <span className="text-primary font-medium">Contamos con certificación NOM-016-CRE-2016</span> y trazabilidad completa, además de soluciones del Sector Energético.
            </p>
          </div>
          <div className="lg:col-span-1">
            <div className="bg-white shadow-lg border rounded-lg p-10 lg:w-full md:w-1/2 sm:w-3/4 w-full">
              <div className="flex flex-col gap-5">
                <h6 className="flex items-center gap-3 font-medium text-sm">
                  {checkIcon}
                  Permisos vigentes
                </h6>
                <h6 className="flex items-center gap-3 font-medium text-sm">
                  {checkIcon}
                  Experiencia comprobada
                </h6>
                <h6 className="flex items-center gap-3 font-medium text-sm">
                  {checkIcon}
                  Calidad certificada
                </h6>
                <h6 className="flex items-center gap-3 font-medium text-sm">
                  {checkIcon}
                  Suministro confiable
                </h6>
                <h6 className="flex items-center gap-3 font-medium text-sm">
                  {checkIcon}
                  Trazabilidad completa
                </h6>
                <h6 className="flex items-center gap-4">
                  <a href="https://api.whatsapp.com/send/?phone=525559513012" target="_blank" className='flex items-center gap-2'>
                    <FaArrowRight size={20} color="#22C55E" className="mr-2" />
                    <p className="text-sm text-green-600">Descubre más</p>
                  </a>
                </h6>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Features3
