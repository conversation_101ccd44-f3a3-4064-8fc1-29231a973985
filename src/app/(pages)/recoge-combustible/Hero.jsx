"use client"
import Image from 'next/image'

//images
import whiteWave from '@/assets/images/shapes/white-wave.svg'
import recogerack from '@/assets/images/hero/combustible-en-terminal.png'

const Hero = () => {
  return (
    <section className="bg-gradient-to-t from-orange-500/10 relative">
      <section className="relative lg:pt-44 pt-40 lg:pb-36 pb-16">
        <div className="container">
          <div className="grid lg:grid-cols-2 grid-cols-1 gap-8 items-center">
            {/* Texto - Primero en móvil (order-1) y primero en desktop (lg:order-1) */}
            <div className="order-1 text-center sm:text-start">
              <h1 className="text-3xl/tight sm:text-4xl/tight lg:text-5xl/tight font-semibold mb-7">
                Recoge tu&nbsp;
                <span>
                  Combustible <span className="relative inline-block z-0 after:bg-orange-300 after:-z-10 after:absolute after:h-3 sm:after:h-4 md:after:h-5 lg:after:h-6 after:w-full after:bottom-0 after:start-0">
                    Directo en terminal
                  </span>
                </span>
              </h1>
              <p className="text-gray-500 text-2xl">
                <span className="text-primary font-medium">
                  ¿Tienes tu propio transporte?&nbsp;
                </span>
                Carga directamente en Terminal (RACK) y aprovecha precios preferenciales.
              </p>
              <div className="mt-5">
                <p className="inline-block bg-orange-500/10 text-md rounded-lg py-2 px-5 gap-1">
                  ¿Dejarás pasar el precio directo de Terminal?
                  <br /> 
                  <a
                    className="font-semibold cursor-pointer text-primary sm:text-base text-lg" 
                    target="_blank"
                    onClick={(e) => {
                      e.preventDefault(); // Prevenir la redirección inmediata
                      const adscompleteId = process.env.NEXT_PUBLIC_GOOGLE_ADS_COMPLETE_ID;
                      if (window.gtag) {
                          // Llamar al evento de conversión con un callback
                          gtag("event", "conversion", {
                          "send_to": adscompleteId,
                          "event_callback": () => {
                              window.location.href = "https://api.whatsapp.com/send/?phone=5559513012";
                          }
                          });
                      } else {
                          // Si `gtag` no está disponible, redirigir inmediatamente
                          window.location.href = "https://api.whatsapp.com/send/?phone=5559513012";
                      }
                    }}
                  >
                    Revisa los requisitos aquí
                  </a>
                </p>
              </div>
            </div>

            {/* Imagen - Segundo en móvil (order-2) y segundo en desktop (lg:order-2) */}
            <div className="order-2">
              <div className="relative 2xl:w-[128%]">
                <div className="before:w-28 before:h-28 sm:before:absolute before:-z-10 before:-bottom-8 before:-start-8 before:bg-[url('../assets/images/pattern/dot.svg')] hidden sm:block" />
                <Image
                  width={655}
                  height={447}
                  alt="Recoge tu combustible directo en terminal (RACK) | Abastecimiento eficiente y seguro de combustibles - Cassiopeia Petrolíferos."
                  className="w-full h-full bg-white p-2 rounded-3xl shadow-lg shadow-black/5"
                  src={recogerack}
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* El wave permanece igual */}
      <div className="absolute bottom-0 inset-x-0 hidden sm:block">
        <Image
          width={1905}
          height={150}
          src={whiteWave}
          alt="white-wave-svg"
          className="w-full scale-x-100 -scale-y-100"
        />
      </div>
    </section>
  )
}

export default Hero
