import Footer2 from '@/components/common/Footer/Footer2'
import Hero from './Hero'
import About from './About'
import Features from './Features'

export const metadata = {
  title: 'Recoge Combustible',
  description: 'Recoge tu Combustible Directamente en Terminal (RACK) y Maximiza la eficiencia operativa.',
  Images: [
    {
      url: '@/assets/images/hero/combustible-en-terminal.png',
      alt: 'Recoge tu Combustible Directamente en Terminal (RACK) y Maximiza la eficiencia operativa.',
    },
  ],
  openGraph: {  
    title: 'Recoge Combustible',  
    description: 'Recoge tu Combustible Directamente en Terminal (RACK) y Maximiza la eficiencia operativa.',
    Images: [
      {
        url: '@/assets/images/hero/combustible-en-terminal.png',
        alt: 'Recoge tu Combustible Directamente en Terminal (RACK) y Maximiza la eficiencia operativa.',
      },
    ],
  },
  url: "https://cassiopeiamx.com/recoge-combustible",
  locale: 'es_MX',
  type: 'website',
}

export const revalidate = 3600

const Startup = () => {
  return (
    <div className="text-gray-700">
      <Hero />
      <About />
      <Features />
      <Footer2 />
    </div>
  )
}

export default Startup
