"use client"

import Link from 'next/link'
import Image from 'next/image'
import React, { useState, useEffect, useCallback, useRef } from 'react'
import { FaArrowRight } from 'react-icons/fa6'
import { DateRangePicker } from '@/components/date-range-picker'
import TerminalSelector from '@/components/TerminalSelector'
import ProductoSelector from '@/components/ProductoSelector'
import PreciosDashboard from '@/components/PreciosDashboard'
import { getPrecios } from '@/app/actions/precios'
import { TipoProducto } from '@/app/types/productos'
import { useSession } from 'next-auth/react'
import LoginDialog from '@/components/LoginDialog'
import tanqueracks from '@/assets/images/hero/tanques-de-terminal.png'
import SimpleCollapse from '@/components/frost-ui/SimpleCollapse'

export default function Features() {
  const { data: session, status } = useSession()
  const isAuthenticated = status === "authenticated"
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [loginMessage, setLoginMessage] = useState("")
  const autoSearchAttempted = useRef(false)

  // Estado para los precios y carga
  const [precios, setPrecios] = useState([])
  const [loading, setLoading] = useState(false)
  const [showLoading, setShowLoading] = useState(false)
  const [error, setError] = useState(null)
  const [hasBuscado, setHasBuscado] = useState(false)
  const [primeraVez, setPrimeraVez] = useState(true)

  // Estado para el rango de fechas - inicializado en null
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)

  // Estado para la terminal seleccionada
  const [terminal, setTerminal] = useState('')

  // Estado para el producto seleccionado
  const [producto, setProducto] = useState(TipoProducto.DIESEL)

  // Función para mostrar el diálogo de inicio de sesión
  const mostrarLoginDialog = (mensaje = "Déjanos tu correo para acceder a nuestros precios exclusivos de combustible") => {
    console.log("Mostrando login dialog con mensaje:", mensaje);
    setLoginMessage(mensaje);
    setShowLoginDialog(true);
    console.log("Estado showLoginDialog después de setear:", true);
  };

  // Función para verificar si todos los campos están completos
  const camposCompletos = () => {
    return startDate && endDate && terminal && producto
  }

  // Función para realizar la búsqueda de precios
  const buscarPrecios = async () => {
    if (loading) return

    if (!camposCompletos()) {
      setError('Por favor, completa todos los campos para buscar precios')
      return
    }

    if (!isAuthenticated) {
      mostrarLoginDialog()
      return
    }

    setLoading(true)
    setShowLoading(false)
    setError(null)

    const loadingTimer = setTimeout(() => {
      if (loading) setShowLoading(true)
    }, 300)

    try {
      const params = {
        producto,
        terminal,
        start: startDate.toISOString(),
        end: endDate.toISOString()
      }

      const result = await getPrecios(params)

      if (result?.requiresAuth) {
        mostrarLoginDialog(result.error || 'Debes iniciar sesión para consultar precios')
        setPrecios([])
      } else if (result?.success && Array.isArray(result.data)) {
        setPrecios(result.data)
        setPrimeraVez(false)
        setHasBuscado(true)
      } else {
        throw new Error(result?.error || 'Formato de respuesta inesperado')
      }
    } catch (error) {
      console.error('Error al cargar precios:', error)
      setError(error.message || 'Error al cargar los precios')
      setPrecios([])
    } finally {
      clearTimeout(loadingTimer)
      setLoading(false)
      setShowLoading(false)
    }
  }

  // Función para actualizar precios cuando cambian los filtros (con debounce)
  const actualizarPrecios = useCallback(
    debounce(() => {
      if (hasBuscado && camposCompletos() && isAuthenticated) {
        buscarPrecios()
      }
    }, 500),
    [hasBuscado, startDate, endDate, terminal, producto, isAuthenticated]
  )

  // Función para debounce
  function debounce(func, delay) {
    let timeoutId
    return function(...args) {
      if (timeoutId) clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  }

  // Efecto para actualizar precios cuando cambian los filtros
  useEffect(() => {
    if (hasBuscado && isAuthenticated && camposCompletos()) {
      actualizarPrecios()
    }
  }, [hasBuscado, startDate, endDate, terminal, producto, isAuthenticated, actualizarPrecios])

  // Manejadores de cambios
  const handleDateRangeChange = ({ startDate: newStartDate, endDate: newEndDate }) => {
    setStartDate(newStartDate)
    setEndDate(newEndDate)
  }

  const handleTerminalChange = (value) => {
    setTerminal(value)
  }

  const handleProductoChange = (value) => {
    setProducto(value)
  }

  // Efecto para mostrar el diálogo de login si no está autenticado
  useEffect(() => {
    if (!isAuthenticated && status !== "loading") {
      mostrarLoginDialog("Déjanos tu correo para acceder a nuestros precios exclusivos de combustible");
    }
  }, [isAuthenticated, status]);

  return (
    <section className="overflow-hidden">
      <LoginDialog
        isOpen={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
        message={loginMessage}
        searchParams={{
          startDate,
          endDate,
          terminal,
          producto
        }}
      />
      <div className="xl:py-24 py-16">
        <div className="container">
          <div>
            <div className="grid lg:grid-cols-2 grid-cols-1 2xl:gap-24 gap-10 items-start justify-center">
              <div className="order-1 flex flex-col items-center text-center">
                <h1 className="text-xl/tight font-medium py-1 px-3 mt-3 mb-1">
                  Selecciona un Período de fechas
                </h1>
                <p className="text-gray-500 text-sm mb-4">
                  Elige la fecha de inicio y la fecha de fin.
                </p>
                <div className="space-y-4 max-w-md">
                  <DateRangePicker
                    onChange={handleDateRangeChange}
                    initialStartDate={startDate}
                    initialEndDate={endDate}
                  />
                </div>
              </div>
              <div className="order-2 flex flex-col">
                <h1 className="text-xl/tight font-medium py-1 px-3 mt-3 mb-4">
                  Selecciona un Combustible
                </h1>
                <ProductoSelector value={producto} onChange={handleProductoChange} />
                <h1 className="text-xl/tight font-medium py-1 px-3 mt-6 mb-4">
                  Selecciona una Terminal
                </h1>
                <TerminalSelector value={terminal} onChange={handleTerminalChange} />
              </div>
            </div>

            {/* Botón de búsqueda */}
            {!hasBuscado && (
              <div className="flex flex-col items-center justify-center my-10 transition-opacity duration-300">
                {/* Versión móvil del botón */}
                <div className="w-full px-16 sm:hidden">
                  <button
                    onClick={() => {
                      if (!isAuthenticated) {
                        mostrarLoginDialog("Déjanos tu correo para acceder a nuestros precios exclusivos de combustible");
                        return;
                      }
                      buscarPrecios();
                    }}
                    disabled={!camposCompletos() || loading}
                    className="inline-flex items-center justify-center gap-2 py-3 px-4 border border-transparent rounded-md bg-blue-600 text-white disabled:bg-gray-400 disabled:cursor-not-allowed disabled:hover:bg-gray-400 enabled:hover:bg-blue-700 enabled:hover:shadow-lg enabled:hover:shadow-primary/80 transition-all focus:border-transparent w-full"
                  >
                    Buscar Precios
                  </button>
                </div>
                
                {/* Versión desktop del botón */}
                <div className="hidden sm:block">
                  <button
                    onClick={() => {
                      if (!isAuthenticated) {
                        mostrarLoginDialog("Déjanos tu correo para acceder a nuestros precios exclusivos de combustible");
                        return;
                      }
                      buscarPrecios();
                    }}
                    disabled={!camposCompletos() || loading}
                    className="inline-flex items-center gap-2 py-2 px-4 border border-transparent rounded-md bg-blue-600 text-white disabled:bg-gray-400 disabled:cursor-not-allowed disabled:hover:bg-gray-400 enabled:hover:bg-blue-700 enabled:hover:shadow-lg enabled:hover:shadow-primary/80 transition-all focus:border-transparent"
                  >
                    Buscar Precios
                  </button>
                </div>
                
                {!isAuthenticated && (
                  <p className="text-gray-500 mt-4 text-sm max-w-md text-center">
                    Inicia sesión para acceder a nuestros precios exclusivos de combustible y gráficas de tendencias.
                  </p>
                )}
              </div>
            )}

            {/* Mensaje cuando no hay precios disponibles */}
            <SimpleCollapse
              open={isAuthenticated && hasBuscado && precios.length === 0 && !loading && !error}
              classNames="transition-all duration-500 ease-in-out transform-gpu"
            >
              <div className="flex justify-center pt-12">
                <div className="bg-yellow-50 border-2 border-amber-100 text-yellow-800 px-4 py-3 rounded-xl max-w-md text-center">
                  <p>No hay precios disponibles para el rango de fechas seleccionado.</p>
                  <p className="text-sm mt-1">Por favor, intenta con un rango de fechas diferente.</p>
                </div>
              </div>
            </SimpleCollapse>

            {hasBuscado && !primeraVez && showLoading && (
              <div className="flex justify-center">
                <div className="px-4 py-1.5 bg-blue-50 text-blue-600 rounded-full text-sm font-medium shadow-sm animate-pulse">
                  {isAuthenticated ? "Actualizando datos..." : "Cargando..."}
                </div>
              </div>
            )}

            {error && (
              <div className="flex justify-center pb-10">
                <div className="mt-12 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded max-w-md">
                  <p className="font-medium">Error:</p>
                  <p>{error}</p>
                </div>
              </div>
            )}

            {/* Dashboard de precios */}
            {precios.length > 0 && isAuthenticated && (
              <div className="opacity-0 animate-fadeIn" style={{ animationDelay: '100ms', animationFillMode: 'forwards' }}>
                <PreciosDashboard
                  loading={false}
                  datos={precios}
                  isAuthenticated={isAuthenticated}
                />
              </div>
            )}

            {/* Sección informativa */}
            <div className="grid lg:grid-cols-2 grid-cols-1 2xl:gap-24 gap-10 items-center mt-16">
              <div data-aos="fade-right" data-aos-duration={1000}>
                <Image 
                  width={496} 
                  height={372} 
                  src={tanqueracks} 
                  alt="Suministro de combustible directo en terminal RACK para máxima eficiencia operativa - Cassiopeia Petrolíferos" 
                  className="rounded-xl" 
                />
              </div>
              <div>
                <span className="text-xs md:text-xs lg:text-sm bg-blue-500/10 text-blue-600 rounded-full px-3 py-1">
                  Carga al instante, ahorro constante
                </span>
                <h1 className="text-2xl md:text-2xl lg:text-3xl/tight font-medium mt-3 mb-4">
                  Combustible directamente en terminal y maximiza la eficiencia operativa.
                </h1>
                <p className="text-base md:text-base lg:text-xl text-gray-500 mb-4">
                  <span className="text-primary font-medium">¿Por qué seguir pagando más?</span> Recoge tu combustible directo en terminal y obtén disponibilidad inmediata, tarifas competitivas y certificación de calidad garantizada.
                </p>
              </div>
            </div>
          </div>

          <div className="md:pt-28 pt-12 md:pb-8 pb-4 text-center">
            <h1 className="text-2xl md:text-2xl lg:text-3xl/tight font-medium mt-3 mb-4 text-center max-w-96 mx-auto">
              Accede Ahora a Combustible al Instante
            </h1>
            <p className="text-base md:text-base lg:text-xl text-gray-500 text-center max-w-xl mx-auto">
              ¡Asegura ya tu combustible! <span className="text-primary font-medium">¡Solicita ahora mismo y recoge tu combustible directo en terminal!</span>
            </p>
            <div className="mt-6 flex justify-center items-center">
              <button>
                <Link
                  href="https://api.whatsapp.com/send/?phone=5559513012"
                  target="_blank"
                  className="inline-flex gap-2 items-center border border-orange-500 text-orange-500 rounded-md hover:bg-orange-500 hover:text-white hover:shadow-lg hover:shadow-orange-500/30 focus:shadow-none focus:outline focus:outline-orange-500/40 transition-all duration-500 py-2 px-4"
                >
                  Más información<FaArrowRight />
                </Link>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
