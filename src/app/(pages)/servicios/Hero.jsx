"use client";
import Image from 'next/image'

//images
import whiteWave from '@/assets/images/shapes/white-wave.svg'
import calidad from '@/assets/images/hero/combustibles-de-calidad.png'

const Hero = () => {
  return (
    <section className="bg-gradient-to-t from-primary/10 relative">
      <section className="relative lg:pt-44 pt-40 lg:pb-36 pb-16">
        <div className="container">
          {/* grid-cols-1 en móvil convierte todo en una sola columna. */}
          <div className="grid lg:grid-cols-2 grid-cols-1 gap-8 items-center">
            {/* order-1 fuerza que el texto salga siempre primero en modo de 1 columna.
                Al llegar a pantallas grandes (lg:), lg:order-1 mantiene el texto a la izquierda */}
            <div className="order-1 lg:order-1 text-center sm:text-start">
              <h1 className="text-3xl/tight sm:text-4xl/tight lg:text-5xl/tight font-semibold mb-7">
                Soluciones pensadas para&nbsp;
                <span className="relative z-0 after:bg-orange-300 after:-z-10 after:absolute after:h-6 after:w-full after:bottom-0 after:end-0">
                  impulsarte
                </span>
              </h1>
              <p className="text-gray-500 text-2xl">
                Tu socio confiable en la industria del combustible; Ofreciendo calidad y eficiencia.
              </p>
              <div className="mt-5">
                <p className="inline-flex bg-primary/10 items-center  text-md rounded-lg py-2 px-5 gap-1">
                  ¿Buscas estos u otros servicios?
                  <a className="font-semibold text-primary cursor-pointer text-lg sm:text-base" target="_blank"
                    onClick={(e) => {
                      e.preventDefault(); // Prevenir la redirección inmediata
                      const adscompleteId = process.env.NEXT_PUBLIC_GOOGLE_ADS_COMPLETE_ID;
                      if (window.gtag) {
                          // Llamar al evento de conversión con un callback
                          gtag("event", "conversion", {
                          "send_to": adscompleteId,
                          "event_callback": () => {
                              window.location.href = "https://api.whatsapp.com/send/?phone=5559513012";
                          }
                          });
                      } else {
                          // Si `gtag` no está disponible, redirigir inmediatamente
                          window.location.href = "https://api.whatsapp.com/send/?phone=5559513012";
                      }
                    }}>
                    Contáctanos
                  </a>
                </p>
              </div>
            </div>
            {/* order-2 fuerza que la imagen salga después del texto en modo de 1 columna.
                Al llegar a pantallas grandes (lg:), lg:order-2 mantiene la imagen a la derecha */}
            <div className="order-2 lg:order-2">
              <div className="relative 2xl:w-[128%]">
                <div className="before:w-28 before:h-28 sm:before:absolute before:-z-10 before:-bottom-8 before:-start-8 before:bg-[url('../assets/images/pattern/dot3.svg')] hidden sm:block" />
                <Image
                  width={655}
                  height={447}
                  alt="Combustibles Cassiopeia - Calidad que Impulsa el Cambio."
                  className="w-full h-full bg-white p-2 rounded-3xl shadow-lg shadow-black/5"
                  src={calidad}
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      <div className="absolute bottom-0 inset-x-0 hidden sm:block">
        <Image
          width={1905}
          height={150}
          src={whiteWave}
          alt="svg"
          className="w-full -scale-x-100 -scale-y-100"
        />
      </div>
    </section>
  )
}

export default Hero
