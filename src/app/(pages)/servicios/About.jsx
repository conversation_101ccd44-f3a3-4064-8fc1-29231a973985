import { FaStar } from 'react-icons/fa6'

const About = () => {
    const review = Array.from(new Array(5))

  return (
    <section className="py-4">
      <div className="container">
        <div className="text-center">
          <div>
            <div className="flex justify-center gap-2 mt-7 mb-4">
                {review.map((icon, idx) => {
                  return (
                    <div
                      className="h-8 w-8 bg-green-400 text-white rounded flex items-center justify-center"
                      key={idx}
                    >
                      <FaStar size={18} />
                    </div>
                  )
                })}
            </div>
            <h2 className="text-2xl md:text-2xl lg:text-3xl/tight font-medium mb-4">
                Multiples Servicios adaptados a tus Necesidades
            </h2>
            <p className="text-base md:text-base lg:text-xl text-gray-500 font-medium">
                En Cassiopeia, somos tu aliado estratégico en el Sector Energético
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
