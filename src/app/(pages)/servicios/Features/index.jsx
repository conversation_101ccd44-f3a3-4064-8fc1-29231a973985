import Link from 'next/link'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import { FaArrowRight, FaStar } from 'react-icons/fa6'
const FeaturesAccordions = dynamic(() => import('./FeaturesAccordions'))
const FeaturesAccordions2 = dynamic(() => import('./FeaturesAccordions2'))

//images
import ingenieria from '@/assets/images/hero/servicios-ingenieria.png'
import cumplimientoregulatorio from '@/assets/images/hero/consultoria.png'
import autoabasto from '@/assets/images/hero/tanque-autoabasto.png'
import combustibles from '@/assets/images/hero/combustible-comercializacion.png'
import obrasenergia from '@/assets/images/hero/obras-de-energia.png'

const Features = () => {
  const review = Array.from(new Array(5))

  return (
    <section className="overflow-hidden">
      <div className="xl:py-24 py-16">
        <div className="container">
          
          <div>
            <div className="grid lg:grid-cols-2 grid-cols-1 2xl:gap-24 gap-10 items-center lg:pb-28 pb-10">
              <div className="order-2 lg:order-2"> {/* Text second on large screens */}
                <span className="text-xs md:text-xs lg:text-sm bg-orange-500/10 text-orange-600 rounded-full px-3 py-1">
                  Abastecimiento de Hidrocarburos
                </span>
                
                <h1 className="text-2xl md:text-2xl lg:text-3xl/tight font-medium mt-3 mb-4">
                  Comercialización de combustibles respaldada por laboratorios certificados
                </h1>
                <p className="text-base md:text-base lg:text-xl text-gray-500 mb-4">
                  Proveemos <span className="text-primary font-medium">Suministro desde 10,000 litros</span>, asegurando el cumplimiento de las más altas exigencias normativas y operativas.
                </p>
                <p className="text-base md:text-base lg:text-xl text-gray-500">
                  Combustibles respaldados por documentación certificada bajo la Norma Oficial Mexicana (NOM-016-CRE-2016). Tu confianza es nuestra prioridad.
                </p>
              </div>
              <div
                className="order-1 lg:order-2"  // Image first on large screens
                data-aos="fade-right"
                data-aos-duration={1000}
              >
                <Image width={496} height={372} src={combustibles} alt="Suministro de combustibles al Mayoreo." className="rounded-xl" />
              </div>
            </div>

            <div className="grid lg:grid-cols-2 grid-cols-1 2xl:gap-24 gap-10 items-center">              
                <div
                  data-aos="fade-right"
                  data-aos-duration={1000}
                >
                <Image width={496} height={372} src={autoabasto} alt="Tanques de Autoabasto." className="rounded-xl" />
                </div>
              <div>
                <span className="text-xs md:text-xs lg:text-sm bg-blue-500/10 text-blue-600 rounded-full px-3 py-1">
                  Proyectos llave en mano
                </span>
                
                <h1 className="text-2xl md:text-2xl lg:text-3xl/tight font-medium mt-3 mb-4">
                  Soluciones llave en mano para instalación de Tanques de Autoabasto
                </h1>
                <p className="text-base md:text-base lg:text-xl text-gray-500 mb-4">
                  Impulsamos la eficiencia con <span className="text-primary font-medium">proyectos llave en mano para estaciones de autoconsumo y uso propio</span>. 
                </p>
                <p className="text-base md:text-base lg:text-xl text-gray-500">
                  Abarcando estudios, trámites, diseño, construcción y puesta en servicio, garantizando soluciones completas.
                </p>
                </div>
            </div>
          </div>

          <div className="pt-28 pb-14 text-center">
            <h1 className="text-2xl md:text-2xl lg:text-3xl/tight font-medium mt-3 mb-4 text-center max-w-96 mx-auto"> 
              Comprometidos con tus Necesidades
            </h1>
            <p className="text-gray-500 text-base md:text-base lg:text-xl text-center">
              Soluciones para ti <span className="text-primary font-medium">¡Conoce cómo podemos ayudarte!</span>
            </p>
            <div className="mt-6 flex justify-center items-center">
              <button>
                <Link
                  href="https://api.whatsapp.com/send/?phone=5559513012"
                  target="_blank"
                  className="inline-flex gap-2 items-center border border-orange-500 text-orange-500 rounded-md hover:bg-orange-500 hover:text-white hover:shadow-lg hover:shadow-orange-500/30 focus:shadow-none focus:outline focus:outline-orange-500/40 transition-all duration-500 py-2 px-4"
                >
                  Más información<FaArrowRight />
                </Link>
              </button>
            </div>
          </div>

          <div className="xl:pt-10 xl:pb-28 py-10">
            <div className="grid lg:grid-cols-2 grid-cols-1 gap-6 items-center">
              <div className="relative">
                <div className="hidden sm:block">
                  <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-start-8 2xl:after:end-0 after:bg-[url('../assets/images/pattern/dot5.svg')]"></div>
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-8 before:-end-8 before:bg-[url('../assets/images/pattern/dot2.svg')]"></div>
                </div>
                <Image
                  src={ingenieria}
                  alt="Servicios de ingeniería en el Sector Energético."
                  data-aos="fade-right"
                  data-aos-duration="400"
                  className="rounded-xl"
                />
              </div>

              <FeaturesAccordions />
            </div>
          </div>

          <div className="xl:pt-16 xl:pb-28 pt-4 pb-16">
            <div className="grid lg:grid-cols-2 grid-cols-1 gap-8 items-center lg:flex-row-reverse"> {/* Added lg:flex-row-reverse */}
              <div>
                <FeaturesAccordions2 />
              </div>

              <div className="relative">
                <div className="hidden sm:block">
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-8 before:-end-8 before:bg-[url('../assets/images/pattern/dot2.svg')]"></div>
                </div>
                <Image
                  src={obrasenergia}
                  alt="Gestión Integral de Proyectos de Energía Eléctrica | Administración y Supervisión de Obras."
                  data-aos="fade-left"
                  data-aos-duration="400"
                  className="rounded-xl"
                />
              </div>
            </div>
          </div>

          <div>
            <div className="grid lg:grid-cols-2 grid-cols-1 2xl:gap-24 gap-10 items-center">              
              <div
                className="order-2 lg:order-1"
                data-aos="fade-right"
                data-aos-duration={1000}
              >
                <Image width={496} height={372} src={cumplimientoregulatorio} alt="Cumplimiento Regulatorio | Normativas ASEA, SENER y Autoridades Locales." className="rounded-xl" />
              </div>
              <div className="order-1 lg:order-2">
                <span className="text-xs md:text-xs lg:text-sm bg-orange-500/10 text-orange-600 rounded-full px-3 py-1">
                  Cumplimiento de Requisistos
                </span>
                
                <h1 className="text-2xl md:text-2xl lg:text-3xl/tight font-medium mt-3 mb-4">
                  Cumplimiento Regulatorio: Normativas ASEA, SENER y Autoridades Locales
                </h1>
                <p className="text-base md:text-base lg:text-xl text-gray-500 mb-4">
                  Brindamos servicios especializados en estudios regulatorios para el sector energético, con enfoque en hidrocarburos. Garantizamos cumplimiento normativo y entregando soluciones.
                </p>
              </div>
            </div>
          </div>

        </div>
      </div>
    </section>
  )
}

export default Features
