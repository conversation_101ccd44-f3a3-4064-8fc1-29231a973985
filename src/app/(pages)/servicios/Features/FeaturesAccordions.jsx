'use client'
import { useState } from 'react'
import { FUCollapse } from '@/components'
import { Scale, HardHat } from 'lucide-react'

const FeaturesAccordions = () => {
  const [accordion, setAccordion] = useState(0)

  const handleAccordion = (index) => () => {
    if (index === accordion) setAccordion(null)
    else setAccordion(index)
  }

  return (
    <div className="lg:ms-24">
      <div data-aos="fade-up" data-aos-duration="500">
        <FUCollapse open={accordion == 0} toggleCollapse={handleAccordion(0)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center gap-x-4 w-full font-medium text-left text-gray-800 transition-all hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-blue-500/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0">
              <HardHat className="h-7 w-7 text-blue-500" />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">
              Servicios de ingeniería en el ramo de hidrocarburos 
            </h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Desarrollo de <span className="text-primary font-medium">proyectos para la Instalación de Tanques de Almacenamiento</span> y servicios integrales de Ingeniería en Hidrocarburos, especializados en el diseño y garantizando el cumplimiento de las normativas más estrictas.
            </p>
          </FUCollapse.Menu>
        </FUCollapse>

        <div className="border-b my-6"></div>

        <FUCollapse open={accordion == 2} toggleCollapse={handleAccordion(2)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center gap-x-4 w-full font-medium text-left text-gray-800 transition hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-orange-500/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0">
              <Scale className="h-7 w-7 text-orange-500" />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">
              Consultoría Legal en el Sector Energético
            </h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-gray-600 dark:text-gray-300 text-lg">
              Te acompañamos en tus trámites regulatorios del sector hidrocarburos con asesoría legal experta. Simplificamos la interpretación de normativas y realizando diagnósticos de cumplimiento.
            </p>
          </FUCollapse.Menu>
        </FUCollapse>
      </div>
    </div>
  )
}

export default FeaturesAccordions
