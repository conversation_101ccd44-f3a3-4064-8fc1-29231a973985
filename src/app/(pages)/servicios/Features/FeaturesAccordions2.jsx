'use client'
import { useState } from 'react'
import { FUCollapse } from '@/components'
import { Zap, ClipboardList } from 'lucide-react'

const FeaturesAccordions = () => {
  const [accordion, setAccordion] = useState(0)

  const handleAccordion = (index) => () => {
    if (index === accordion) setAccordion(null)
    else setAccordion(index)
  }

  return (
    <div className="lg:mr-24">
      <div data-aos="fade-up" data-aos-duration="500">
        <FUCollapse open={accordion == 0} toggleCollapse={handleAccordion(0)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center w-full font-medium text-left text-gray-800 transition-all hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-yellow-600/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0 mr-4"> 
              <Zap className="h-7 w-7 text-yellow-500" />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">
              Administración y supervisión de construccion de obras de energía eléctrica
            </h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-gray-600 dark:text-gray-300 text-lg">
              Administramos, diseñamos y supervisamos sistemas de generación y transmisión de energía eléctrica, personalizados para satisfacer tus necesidades.
            </p>
          </FUCollapse.Menu>
        </FUCollapse>

        <div className="border-b my-6"></div>

        <FUCollapse open={accordion == 2} toggleCollapse={handleAccordion(2)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center w-full font-medium text-left text-gray-800 transition hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-blue-500/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0 mr-4">
              <ClipboardList className="h-7 w-7 text-blue-500" />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">
              Desarrollo de proyectos y administración de proyectos
            </h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-gray-600 dark:text-gray-300 text-lg">
              Ayudamos a empresas a desarrollar casos de negocio y factibilidad para proyectos; programación, administración y seguimiento de actividades de proyectos para asegurar objetivos de tiempo y presupuesto.
            </p>
          </FUCollapse.Menu>
        </FUCollapse>
      </div>
    </div>
  )
}

export default FeaturesAccordions
