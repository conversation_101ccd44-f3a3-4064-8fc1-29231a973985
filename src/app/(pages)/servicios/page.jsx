import Footer2 from '@/components/common/Footer/Footer2'
import Hero from './Hero'
import About from './About'
import Features from './Features'

export const metadata = {
  title: 'Servicios',
  description: 'Tu socio confiable en la industria del combustible; Ofreciendo calidad y eficiencia.',
  Images: [
    {
      url: '@/assets/images/hero/combustibles-de-calidad.png',
      alt: 'Tu socio confiable en la industria del combustible; Ofreciendo calidad y eficiencia.',
    },
  ],
  openGraph: {
    title: 'Servicios',
    description: 'Tu socio confiable en la industria del combustible; Ofreciendo calidad y eficiencia.',
    Images: [
      {
        url: '@/assets/images/hero/combustibles-de-calidad.png',
        alt: 'Tu socio confiable en la industria del combustible; Ofreciendo calidad y eficiencia.',
      },
    ],
  },
  url: "https://cassiopeiamx.com/servicios",
  locale: 'es_MX',
  type: 'website',
}

export const revalidate = 3600

const Startup = () => {
  return (
    <div className="text-gray-700">
      <Hero />
      <About />
      <Features />
      <Footer2 />
    </div>
  )
}

export default Startup
