import Image from 'next/image'

// images
import pemex from '@/assets/images/pemex.svg'
import images from '@/assets/images/canacar.svg'
import imss from '@/assets/images/imss.svg'
import liconsa from '@/assets/images/liconsa.svg'
import cfe from '@/assets/images/cfe.svg'

const brands = [pemex, images, imss, liconsa, cfe]

const ClientsReview = () => {
  return (
    <section className="py-4">
      <div className="container">
        <div className="text-center">
          <div>
            <h1 className="hidden sm:block text-xl text-gray-500 mb-4">Atendemos Grupos Gasolineros y Transportistas</h1>
            <p className="text-2xl font-medium">
              Trabajamos en colaboración con
            </p>
            <div className="flex flex-wrap md:flex-nowrap justify-center gap-10 mt-7">
              {(brands ?? []).map((image, idx) => {
                return (
                  <div key={idx}>
                    <Image
                      alt="Empresas con las que colabora Cassiopeia Petrolíferos."
                      width={112}
                      height={37}
                      loading="lazy"
                      src={image}
                      className="w-28"
                    />
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ClientsReview
