"use client"
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState } from "react"

export default function ContactForm() {
  const [formData, setFormData] = useState({
    nombre: "",
    combustible_interes: "",
    telefono: "",
    correo: "",
    mensaje: "",
  });

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };  

  const handleSelectChange = (value) => {
    setFormData((prevData) => ({
      ...prevData,
      combustible_interes: value
    }));
};

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess(false);
  
    try {
      const response = await fetch("https://formspree.io/f/xzblqdbv", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });
  
      if (response.ok) {
        setSuccess(true);
        setFormData({
          nombre: "",
          combustible_interes: "",
          telefono: "",
          correo: "",
          mensaje: "",
        });
      } else {
        throw new Error("Error al enviar el formulario.");
      }
    } catch (error) {
      setError("Hubo un problema al enviar el formulario.");
    } finally {
      setLoading(false);
    }
  };
  

  return (
    <>
      <section className="py-6 relative" id="contactanos">
      <div className="container">
        
        <div className="text-center">
          <span className="text-sm font-medium rounded-full py-1 px-3 text-primary bg-primary/10">
            Hablemos más
          </span>
          <h1 className="text-3xl font-medium mt-4">Contáctanos</h1>
          <p className="text-xl text-gray-600 mt-2">
            Por favor <span className="text-blue-600 font-bold">complete</span> el siguiente formulario y nos comunicaremos con usted en breve.
          </p>
        </div>

        <div className="lg:flex items-center justify-center">
          <div className="lg:w-1/2 lg:mr-10"> 
            <div className="mb-6 relative bg-clip-border rounded-[0.1875rem]">
              <div className="py-12">

              <form onSubmit={handleSubmit} className="w-full max-w-lg mx-auto space-y-4 mt-6">
                <div>
                  <Label htmlFor="nombre" className="text-lg">Nombre o Razón social</Label>
                  <Input
                    id="nombre"
                    name="nombre"
                    type="text"
                    value={formData.nombre}
                    placeholder="Nombre"
                    onChange={handleChange}
                    className="text-md"
                    required
                  />
                </div>

                <div>
                  <Label className="text-lg">Combustible de interés</Label>
                  <Select value={formData.combustible_interes} onValueChange={handleSelectChange} className="text-md" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccione un combustible" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Diesel Automotriz">Diesel Automotriz</SelectItem>
                      <SelectItem value="Gasolina Regular">Gasolina Regular</SelectItem>
                      <SelectItem value="Gasolina Premium">Gasolina Premium</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-lg" htmlFor="telefono">Teléfono</Label>
                    <Input
                      id="telefono"
                      name="telefono"
                      type="tel"
                      value={formData.telefono}
                      placeholder="Tu teléfono"
                      onChange={handleChange}
                      className="text-md"
                    />
                  </div>
                  <div>
                    <Label htmlFor="correo" className="text-lg">Correo</Label>
                    <Input
                      id="correo"
                      name="correo"
                      type="email"
                      value={formData.correo}
                      placeholder="Tu correo electrónico"
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label className="text-lg" htmlFor="mensaje">Mensaje</Label>
                  <Textarea
                    id="mensaje"
                    name="mensaje"
                    value={formData.mensaje}
                    placeholder="Escribe tu mensaje..."
                    rows={4}
                    onChange={handleChange}
                    required
                    className="text-md"
                  />
                </div>
                
                <button
                  type="submit"
                  className="inline-flex items-center text-md bg-primary text-white font-medium leading-6 text-center align-middle select-none py-2 px-4 rounded-md transition-all hover:shadow-lg hover:shadow-primary/80"
                  disabled={loading}
                >
                  {loading ? "Enviando..." : "Enviar"}
                  <span className="w-4 h-4 ms-1">
                    <svg
                      className="w-full h-full text-white"
                      viewBox="0 0 24 24"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlnsXlink="http://www.w3.org/1999/xlink"
                    >
                      <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
                        <rect id="bound" x={0} y={0} width={24} height={24} />
                        <path
                          d="M3,13.5 L19,12 L3,10.5 L3,3.7732928 C3,3.70255344 3.01501031,3.63261921 3.04403925,3.56811047 C3.15735832,3.3162903 3.45336217,3.20401298 3.70518234,3.31733205 L21.9867539,11.5440392 C22.098181,11.5941815 22.1873901,11.6833905 22.2375323,11.7948177 C22.3508514,12.0466378 22.2385741,12.3426417 21.9867539,12.4559608 L3.70518234,20.6826679 C3.64067359,20.7116969 3.57073936,20.7267072 3.5,20.7267072 C3.22385763,20.7267072 3,20.5028496 3,20.2267072 L3,13.5 Z"
                          id="Combined-Shape"
                          fill="currentcolor"
                        />
                      </g>
                    </svg>
                  </span>
                </button>

                {success && <p className="text-green-600 text-center mt-4 text-lg">¡Formulario enviado con éxito!</p>}
                {error && <p className="text-red-600 text-center mt-4">{error}</p>}
              </form>

              </div>
            </div>
          </div>
          <div className="lg:w-1/2 lg:ml-10 overflow-x-hidden">
            <div> 

            <div className="p-6 m-8 rounded-md shadow-md group-hover:shadow-lg transition-all duration-500">
                  <div className="flex items-start">
                    <span className="flex items-center justify-center w-12 h-12 bg-primary/20 rounded-lg relative me-4 shrink-0">
                        <svg
                          className="w-7 h-7 text-primary"
                          viewBox="0 0 24 24"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          xmlnsXlink="http://www.w3.org/1999/xlink"
                        >
                          <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
                            <rect id="bound" x={0} y={0} width={24} height={24} />
                            <path
                              d="M12.7037037,14 L15.6666667,10 L13.4444444,10 L13.4444444,6 L9,12 L11.2222222,12 L11.2222222,14 L6,14 C5.44771525,14 5,13.5522847 5,13 L5,3 C5,2.44771525 5.44771525,2 6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 L19,13 C19,13.5522847 18.5522847,14 18,14 L12.7037037,14 Z"
                              id="Combined-Shape"
                              fill="currentcolor"
                              opacity="0.3"
                            />
                            <path
                              d="M9.80428954,10.9142091 L9,12 L11.2222222,12 L11.2222222,16 L15.6666667,10 L15.4615385,10 L20.2072547,6.57253826 C20.4311176,6.4108595 20.7436609,6.46126971 20.9053396,6.68513259 C20.9668779,6.77033951 21,6.87277228 21,6.97787787 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,6.97787787 C3,6.70173549 3.22385763,6.47787787 3.5,6.47787787 C3.60510559,6.47787787 3.70753836,6.51099993 3.79274528,6.57253826 L9.80428954,10.9142091 Z"
                              id="Combined-Shape"
                              fill="currentcolor"
                            />
                          </g>
                        </svg>
                      </span>
                      <div className="flex-grow min-w-0">
                          <h5 className="text-lg font-semibold">Correo</h5>
                          <p className="text-md text-gray-700 break-words">
                              <a className="hover:text-primary text-gray-700 no-underline" href="mailto:<EMAIL>">
                                  <EMAIL>
                              </a>
                          </p>
                      </div>
                  </div>
              </div>

              <div className="p-6 m-8 rounded-md shadow-md group-hover:shadow-lg transition-all duration-500">
                  <div className="flex items-start">
                      <span className="flex items-center justify-center w-12 h-12 bg-orange-500/20 rounded-lg relative me-4 shrink-0">
                        <svg
                          className="w-7 h-7 text-orange-500"
                          viewBox="0 0 24 24"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          xmlnsXlink="http://www.w3.org/1999/xlink"
                        >
                          <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
                            <rect id="bound" x={0} y={0} width={24} height={24} />
                            <path
                              d="M5,10.5 C5,6 8,3 12.5,3 C17,3 20,6.75 20,10.5 C20,12.8325623 17.8236613,16.03566 13.470984,20.1092932 C12.9154018,20.6292577 12.0585054,20.6508331 11.4774555,20.1594925 C7.15915182,16.5078313 5,13.2880005 5,10.5 Z M12.5,12 C13.8807119,12 15,10.8807119 15,9.5 C15,8.11928813 13.8807119,7 12.5,7 C11.1192881,7 10,8.11928813 10,9.5 C10,10.8807119 11.1192881,12 12.5,12 Z"
                              id="Combined-Shape"
                              fill="currentcolor"
                            />
                          </g>
                        </svg>
                      </span>
                      <div className="flex-grow">
                          <h5 className="text-lg font-semibold">Dirección</h5>
                          <p className="text-md text-gray-700">Av. Insurgentes Sur 1571, San José Insurgentes, Benito Juárez, 03900 Ciudad de México, CDMX</p>
                      </div>
                  </div>
              </div>

              <div className="p-6 m-8 rounded-md shadow-md group-hover:shadow-lg transition-all duration-500">
                  <div className="flex items-start">
                      <span className="flex items-center justify-center w-12 h-12 bg-teal-500/20 rounded-lg relative me-4 shrink-0">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6 text-teal-500">
                              <g>
                                  <circle cx="12" cy="12" r="10"></circle>
                                  <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                                  <line x1="9" y1="9" x2="9.01" y2="9"></line>
                                  <line x1="15" y1="9" x2="15.01" y2="9"></line>
                              </g>
                          </svg>
                      </span>
                      <div className="flex-grow">
                          <h5 className="text-lg font-semibold">Atención al cliente</h5>
                          <p className="text-sm text-gray-700"><a className="hover:text-primary" href="https://api.whatsapp.com/send/?phone=5559513012" target="_blank">+52 55 5951 3012</a></p>
                      </div>
                  </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </section>
    </>
  )
}
