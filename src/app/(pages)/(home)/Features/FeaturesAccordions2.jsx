'use client'
import { useState } from 'react'
import Link from 'next/link'
import { FaArrowRight } from 'react-icons/fa6'
import { FUCollapse } from '@/components'
import { Scale, ClipboardList, CircleCheckBig } from 'lucide-react'

const FeaturesAccordions = () => {
  const [accordion, setAccordion] = useState(0)

  const handleAccordion = (index) => () => {
    if (index === accordion) setAccordion(null)
    else setAccordion(index)
  }

  return (
    <div className="lg:mr-24">
      <div data-aos="fade-up" data-aos-duration="500">
        <FUCollapse open={accordion == 0} toggleCollapse={handleAccordion(0)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center gap-x-4 w-full font-medium text-left text-gray-800 transition-all hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-orange-500/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0">
              <Scale className="h-7 w-7 text-orange-500" />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">
              Consultoría Legal en el Sector Energético
            </h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-gray-600 dark:text-gray-300 text-md">
              Asesoría Legal en trámites regulatorios y gestión de permisos para Hidrocarburos.
            </p>
            <div className="mt-7 flex items-center">
              <Link
                href="/servicios"
                className="inline-flex gap-2 items-center text-primary"
              >
                Solicita Asesoría Legal Energética
                <FaArrowRight />
              </Link>
            </div>
          </FUCollapse.Menu>
        </FUCollapse>

        <div className="border-b my-6"></div>

        <FUCollapse open={accordion == 1} toggleCollapse={handleAccordion(1)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center gap-x-4 w-full font-medium text-left text-gray-800 transition hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-blue-500/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0">
              <ClipboardList className="text-blue-500 h-7 w-7" />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">
              Desarrollo de proyectos y administración de proyectos
            </h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-gray-600 dark:text-gray-300 text-md">
              Desarrollo de casos de negocio y gestión de proyectos para cumplir tiempo y presupuesto.
            </p>
            <div className="mt-7 flex items-center">
              <Link
                href="/servicios"
                className="text-primary inline-flex gap-2 items-center"
              >
                Cotiza Desarrollo de Proyectos
                <FaArrowRight />
              </Link>


            </div>
          </FUCollapse.Menu>
        </FUCollapse>

        <div className="border-b my-6"></div>

        <FUCollapse open={accordion == 2} toggleCollapse={handleAccordion(2)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center gap-x-4 w-full font-medium text-left text-gray-800 transition hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-green-500/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0">
              <CircleCheckBig className="h-7 w-7 text-green-500" />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">Cumplimiento Regulatorio: Normativas ASEA, SENER y Autoridades Locales</h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-gray-600 dark:text-gray-300 text-md">
              Elaboración de estudios regulatorios para el sector energía con enfoque en hidrocarburos.
            </p>
            <div className="mt-7 flex items-center">
              <Link
                href="/servicios"
                className="text-primary inline-flex gap-2 items-center"
              >
                Cotiza Cumplimiento Regulatorio ASEA
                <FaArrowRight />
              </Link>
            </div>
          </FUCollapse.Menu>
        </FUCollapse>
      </div>
    </div>
  )
}

export default FeaturesAccordions
