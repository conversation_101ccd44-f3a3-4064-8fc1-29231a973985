'use client'
import { useState } from 'react'
import Link from 'next/link'
import { FaArrowRight } from 'react-icons/fa6'
import { FUCollapse } from '@/components'
import { Cog, HardHat, Zap } from 'lucide-react'

const FeaturesAccordions = () => {
  const [accordion, setAccordion] = useState(0)

  const handleAccordion = (index) => () => {
    if (index === accordion) setAccordion(null)
    else setAccordion(index)
  }

  return (
    <div className="lg:ms-24">
      <div data-aos="fade-up" data-aos-duration="500">
        <FUCollapse open={accordion == 0} toggleCollapse={handleAccordion(0)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center gap-x-4 w-full font-medium text-left text-gray-800 transition-all hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-orange-500/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0">
              <Cog className='h-7 w-7 text-orange-500' />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">
              Proyectos llave en mano para Instalación de Tanques de Autoabasto
            </h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-gray-600 dark:text-gray-300 text-md">
              Soluciones completas para estaciones de autoconsumo: estudios, trámites, diseño, construcción y puesta en servicio.
            </p>
            <div className="mt-7 flex items-center">
              <Link
                href="/servicios"
                className="inline-flex gap-2 items-center text-primary"
              >
                Cotiza tu Tanque de Autoabasto
                <FaArrowRight />
              </Link>
            </div>
          </FUCollapse.Menu>
        </FUCollapse>

        <div className="border-b my-6"></div>

        <FUCollapse open={accordion == 1} toggleCollapse={handleAccordion(1)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center gap-x-4 w-full font-medium text-left text-gray-800 transition hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-blue-500/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0">
              <HardHat className="h-7 w-7 text-blue-500" />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">
              Tanques de Almacenamiento y servicios de ingeniería en el ramo de hidrocarburos
            </h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-gray-600 dark:text-gray-300 text-md">
              Desarrollo de proyectos de ingeniería básica y diseño para la instalación de Tanques de Almacenamiento.
            </p>
            <div className="mt-7 flex items-center">
              <Link
                href="/servicios"
                className="text-primary inline-flex gap-2 items-center"
              >
                Cotiza tu Tanque de Almacenamiento
                <FaArrowRight />
              </Link>
            </div>
          </FUCollapse.Menu>
        </FUCollapse>

        <div className="border-b my-6"></div>

        <FUCollapse open={accordion == 2} toggleCollapse={handleAccordion(2)}>
          <FUCollapse.Toggle className="pt-2 inline-flex items-center gap-x-4 w-full font-medium text-left text-gray-800 transition hover:text-gray-500 dark:text-gray-200 dark:hover:text-gray-400">
            <div className="bg-yellow-600/10 rounded-lg flex items-center justify-center h-12 w-12 shrink-0">
              <Zap className="text-yellow-500 h-7 w-7" />
            </div>
            <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-2xl">
              Administración y supervisión de construccion de obras de energía eléctrica
            </h1>
          </FUCollapse.Toggle>
          <FUCollapse.Menu className="w-full overflow-hidden duration-300 ps-16">
            <p className="text-gray-600 dark:text-gray-300 text-md">
              Ingeniería básica y de diseño para la instalación de sistemas de generación y transmisión de energía eléctrica.
            </p>
            <div className="mt-7 flex items-center">
              <Link
                href="/servicios"
                className="text-primary inline-flex gap-2 items-center"
              >
                Cotiza tu Proyecto de Energía Eléctrica
                <FaArrowRight />
              </Link>
            </div>
          </FUCollapse.Menu>
        </FUCollapse>
      </div>
    </div>
  )
}

export default FeaturesAccordions
