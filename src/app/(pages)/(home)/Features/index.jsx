import Link from 'next/link'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import { FaArrowRight, FaStar } from 'react-icons/fa6'
const FeaturesAccordions = dynamic(() => import('./FeaturesAccordions'))
const FeaturesAccordions2 = dynamic(() => import('./FeaturesAccordions2'))

//images
import calidad from '@/assets/images/hero/calidad-de-combustibles.jpg'
import combustibles from '@/assets/images/hero/lideres-en-combustibles.jpg'
import autoabasto from '@/assets/images/hero/tanques-de-autoabasto.jpg'
import personal from '@/assets/images/hero/personal-capacitado.jpg'

const Features = () => {
  const review = Array.from(new Array(5))

  return (
    <section className="overflow-hidden">
      <div className="xl:py-24 py-16">
        <div className="container">
          
          <div>
            <div className="grid lg:grid-cols-2 grid-cols-1 2xl:gap-24 gap-10 items-center">
              <div className="order-2 lg:order-2"> {/* Text second on large screens */}
                <span className="text-xs md:text-xs lg:text-sm bg-green-500/10 text-green-600 rounded-full px-3 py-1">
                  Alta Calidad
                </span>
                
                <h1 className="text-2xl md:text-2xl lg:text-3xl/tight font-medium mt-3 mb-4">
                  Líderes en Abastecimiento de Hidrocarburos de Alta Calidad en México.
                </h1>
                <p className="text-base md:text-base lg:text-xl text-gray-500">
                  Suministramos combustibles certificados en México con la más alta calidad, garantizando seguridad, eficiencia y cumplimiento normativo en cada entrega.
                </p>
                <button className="mt-12 flex items-center">
                  <Link
                    href="/servicios"
                    className="inline-flex gap-2 items-center border border-primary text-primary rounded-md hover:bg-primary hover:text-white hover:shadow-lg hover:shadow-primary/30 focus:shadow-none focus:outline focus:outline-primary/40 transition-all duration-500 py-2 px-4"
                  >
                    Más información<FaArrowRight />
                  </Link>
                </button>
              </div>
              <div
                className="order-1 lg:order-2"  // Image first on large screens
                data-aos="fade-right"
                data-aos-duration={1000}
              >
                <Image className="rounded-xl" width={496} height={372} src={combustibles} loading="lazy" placeholder="blur" alt="Cassiopeia Petrolíferos - Combustibles de alta calidad." />
              </div>
            </div>
          </div>

          <div className="mt-16 text-center">
            <span className="text-xs md:text-xs lg:text-sm font-medium rounded-full py-1 px-3 text-primary bg-primary/10">
              Lidera el Sector Energético
            </span>
            <h1 className="text-2xl md:text-2xl lg:text-3xl/tight font-medium mt-3 mb-4">
              Estrategias Energéticas de Alto Impacto
            </h1>
            <p className="text-base md:text-base lg:text-xl text-gray-500">
              Somos tu aliado estratégico en el Sector Energético
            </p>
          </div>

          <div className="xl:pt-16 xl:pb-16 pt-4 pb-16">
            <div className="grid lg:grid-cols-2 grid-cols-1 gap-6 items-center">
              <div className="relative">
                <div className="hidden sm:block">
                  <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-start-8 2xl:after:end-0 after:bg-[url('../assets/images/pattern/dot5.svg')]"></div>
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-8 before:-end-8 before:bg-[url('../assets/images/pattern/dot2.svg')]"></div>
                </div>
                <Image
                  className="rounded-xl"
                  src={calidad}
                  loading="lazy"
                  placeholder="blur"
                  alt="Cassiopeia Petrolíferos ofrece Calidad que impulsa al Cambio."
                  data-aos="fade-right"
                  data-aos-duration="400"
                />
              </div>

              <FeaturesAccordions />
            </div>
          </div>

          <div className="xl:pt-16 xl:pb-28 pt-4 pb-16">
            <div className="grid lg:grid-cols-2 grid-cols-1 gap-8 items-center lg:flex-row-reverse"> {/* Added lg:flex-row-reverse */}
              <div>
                <FeaturesAccordions2 />
              </div>

              <div className="relative">
                <div className="hidden sm:block">
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-8 before:-end-8 before:bg-[url('../assets/images/pattern/dot2.svg')]"></div>
                </div>
                <Image
                  className="rounded-xl"
                  src={autoabasto}
                  loading="lazy"
                  placeholder="blur"
                  alt="Tanques de Autoabasto - Combustiles Cassiopeia Petrolíferos."
                  data-aos="fade-left"
                  data-aos-duration="400"
                />
              </div>
            </div>
          </div>

          <div>
          <div className="grid lg:grid-cols-2 grid-cols-1 2xl:gap-24 gap-10 items-center">
            {/* Imagen - segundo en móvil, primero en desktop */}
            <div className="order-2 lg:order-1">
              <Image
                className='rounded-xl'
                alt="Cassiopeia Petrolíferos - Personal altamente capacitado."
                width={496}
                height={335}
                loading="lazy"
                placeholder="blur"
                src={personal}
                data-aos="fade-right"
                data-aos-duration={600}
              />
            </div>

            {/* Texto - primero en móvil, segundo en desktop */}
            <div className="order-1 lg:order-2" data-aos="fade-left" data-aos-duration={700}>
              <div className="flex xl:justify-start gap-2 mt-7">
                {review.map((icon, idx) => {
                  return (
                    <div
                      className="h-8 w-8 bg-green-400 text-white rounded flex items-center justify-center"
                      key={idx}
                    >
                      <FaStar size={18} />
                    </div>
                  )
                })}
              </div>
              <h1 className="font-medium mb-4 mt-2 text-xl md:text-xl lg:text-3xl/tight">
                Compromiso con excelencia y confianza
              </h1>
              <div className="flex flex-col gap-4">
                <div className="flex items-center gap-4">
                  <div className="bg-primary/10 rounded-lg flex items-center justify-center h-12 w-12">
                    <svg
                      className="h-6 w-6 text-primary"
                      viewBox="0 0 24 24"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlnsXlink="http://www.w3.org/1999/xlink"
                    >
                      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                        <polygon id="Shape" points="0 0 24 0 24 24 0 24"></polygon>
                        <path
                          d="M18,8 L16,8 C15.4477153,8 15,7.55228475 15,7 C15,6.44771525 15.4477153,6 16,6 L18,6 L18,4 C18,3.44771525 18.4477153,3 19,3 C19.5522847,3 20,3.44771525 20,4 L20,6 L22,6 C22.5522847,6 23,6.44771525 23,7 C23,7.55228475 22.5522847,8 22,8 L20,8 L20,10 C20,10.5522847 19.5522847,11 19,11 C18.4477153,11 18,10.5522847 18,10 L18,8 Z M9,11 C6.790861,11 5,9.209139 5,7 C5,4.790861 6.790861,3 9,3 C11.209139,3 13,4.790861 13,7 C13,9.209139 11.209139,11 9,11 Z"
                          id="Combined-Shape"
                          fill="currentColor"
                          opacity="0.3"
                        ></path>
                        <path
                          d="M0.00065168429,20.1992055 C0.388258525,15.4265159 4.26191235,13 8.98334134,13 C13.7712164,13 17.7048837,15.2931929 17.9979143,20.2 C18.0095879,20.3954741 17.9979143,21 17.2466999,21 C13.541124,21 8.03472472,21 0.727502227,21 C0.476712155,21 -0.0204617505,20.45918 0.00065168429,20.1992055 Z"
                          id="Mask-Copy"
                          fill="currentColor"
                        ></path>
                      </g>
                    </svg>
                  </div>
                  <h1 className="font-medium text-base md:text-lg lg:text-xl">
                    Personal altamente capacitado
                  </h1>
                </div>
                <div className="flex items-center gap-4">
                  <div className="bg-orange-500/20 rounded-lg flex items-center justify-center h-12 w-12">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-orange-500 h-7 w-7 lucide lucide-shield">
                      <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"/>
                    </svg>
                  </div>
                  <h1 className="font-medium text-base md:text-lg lg:text-xl">
                    Seguridad y confianza garantizada
                  </h1>
                </div>
              </div>
            </div>
          </div>
        </div>

        </div>
      </div>
    </section>
  )
}

export default Features
