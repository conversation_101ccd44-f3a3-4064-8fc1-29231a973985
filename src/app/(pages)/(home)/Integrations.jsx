import Image from 'next/image'

import cobertura from '@/assets/images/hero/mapacobertura.webp'
import refineria from '@/assets/images/hero/refineria.png'

const Integrations = () => {
  return (
    <section
      className="my-5 py-6 position-relative optim2"
      style={{
        backgroundImage: `url(${refineria.src})`,
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat"
      }}
    >
      <div
        className="container mx-auto px-4 py-12"
        data-aos="fade-up"
        data-aos-duration="500"
      >
        <div className="text-center mb-8">
          <span className="text-sm bg-orange-500/10 text-semibold text-orange-600 rounded-full px-3 py-1">
            Atención 24/7 los 365 días del año
          </span>
          <h1 className="text-3xl font-medium text-white mt-4">
            Contamos con cobertura nacional
          </h1>
          <p className="text-white text-xl mt-2">
            Estamos{" "}
            <span className="text-orange-500 font-bold">disponibles</span> en
            los siguientes estados de la República.
          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-center justify-center mb-3 pb-6">
            <div className="lg:w-4/12 p-2 mb-3 lg:mb-0">
                <Image
                    src={cobertura}
                    alt="Mapa de cobertura Cassiopeia"
                    className="w-full h-auto rounded-lg"
                    loading="lazy"
                    placeholder="blur"
                />
            </div>
            <div className="w-full lg:w-3/12 text-center mt-4 mx-12 lg:mt-0">
                <p className="text-white text-xl">
                    Ciudad de México, Campeche, Hidalgo, Estado de México, Morelos, Nuevo León, Puebla, Querétaro, Quintana Roo, Tabasco, Tamaulipas, Tlaxcala, Veracruz
                </p>
            </div>
        </div>

      </div>
    </section>
  )
}

export default Integrations
