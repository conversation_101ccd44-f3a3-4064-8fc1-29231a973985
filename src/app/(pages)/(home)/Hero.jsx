'use client';
import Image from 'next/image'
import { FaArrowRight } from 'react-icons/fa6'

//images
import combustiblescassiopeia from '@/assets/images/hero/combustiblescassio.jpg'
import whiteWave from '@/assets/images/shapes/white-wave.svg'

const Hero = () => {
  return (
    <section className="bg-gradient-to-t from-orange-50/80 relative">
      <section className="relative lg:pt-44 pt-40 lg:pb-36 pb-16">
        <div className="container">
          <div className="grid lg:grid-cols-7 grid-cols-1 gap-12 items-center">
            <div className="lg:col-span-3 order-1 lg:order-1 text-center sm:text-start">
              <h1 className="hidden sm:block text-3xl sm:text-4xl lg:text-5xl font-semibold mb-7">
              Hazlo fácil, abastece&nbsp;
                <span className="relative z-0 after:bg-orange-300 after:-z-10 after:absolute after:h-6 after:w-full after:bottom-0 after:end-0">
                  seguro
                </span>
              </h1>
              <h1 className="text-3xl text-gray-500 font-medium">
                Comercializadora de Hidrocarburos que Suministra <span className="hidden sm:inline text-gray-700">Gasolinas y Diésel</span><span className="sm:hidden text-gray-700">Combustibles</span><span className="text-gray-700"> de Alta Calidad</span>.
              </h1>
              <div className="flex w-full justify-center md:justify-start text-lg/tight font-medium sm:text-base sm:font-normal gap-5 lg:mt-8 mt-12 relative z-10">
                <a
                  className="flex gap-3 cursor-pointer items-center lg:py-3 lg:px-6 py-3.5 px-7 lg:rounded-md rounded-lg border border-primary text-white bg-primary hover:shadow-lg hover:shadow-primary/50 focus:outline focus:outline-primary/50 transition-all duration-500"
                  target="_blank"
                  href="https://api.whatsapp.com/send/?phone=5559513012"
                  onClick={(e) => {
                    e.preventDefault(); // Prevenir la redirección inmediata
                    const adscompleteId = process.env.NEXT_PUBLIC_GOOGLE_ADS_COMPLETE_ID;
                    if (window.gtag) {
                        // Llamar al evento de conversión con un callback
                        gtag("event", "conversion", {
                        "send_to": adscompleteId,
                        "event_callback": () => {
                            window.location.href = "https://api.whatsapp.com/send/?phone=5559513012";
                        }
                        });
                    } else {
                        // Si `gtag` no está disponible, redirigir inmediatamente
                        window.location.href = "https://api.whatsapp.com/send/?phone=5559513012";
                    }
                  }}
                >
                  Cotiza Ahora <FaArrowRight />
                </a>
              </div>
            </div>
            <div className="lg:col-span-4 order-2 lg:order- rounded-lg overflow-hidden">
              <div style={{ WebkitMaskImage: 'radial-gradient(ellipse 215% 270%,rgba(0, 0, 0, 1) 15%, transparent 30%)'
               }}>
                <Image
                  width={3184}
                  height={2120}
                  priority
                  src={combustiblescassiopeia}
                  alt="Comercialización de Hidrocarburos Cassiopeia Petrolíferos."
                  className="rounded-3xl"
                  style={{ transform: 'scale(1)', transformOrigin: 'center %'}}
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      <div className="absolute bottom-0 inset-x-0 hidden sm:block">
        <Image
          width={1905}
          height={150}
          src={whiteWave}
          priority
          alt="svg-wave"
          className="w-full -scale-x-100 -scale-y-100"
        />
      </div>
    </section>
  )
}

export default Hero
