
import Footer2 from '@/components/common/Footer/Footer2'
import ClientsReview from './ClientsReview'
import Hero from './<PERSON>'
import Integrations from './Integrations'
import Features from './Features'
import ContactForm from './Contact'

export const metadata = {
  title: 'Comercializadora de Hidrocarburos',
  Images: [
    {
      url: 'https://cassiopeiamx.com/open-graph.png',
      alt: 'Líder en Hidrocarburos de Alta Calidad y Suministro Certificado en México.',
      width: 1200,
      height: 630,
    },
  ],  
}

export const revalidate = 3600

const Startup = () => {
  return (
    <div className="text-gray-700">
      <Hero />
      <ClientsReview />
      <Features />
      <Integrations />
      <ContactForm />
      <Footer2 socialIcon />
    </div>
  )
}

export default Startup
