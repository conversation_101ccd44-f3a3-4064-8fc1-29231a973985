'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MapViewLibre from '@/components/MapViewLibre';
import {
  EstadosQuote,
  CiudadesMQuote,
  geocoder,
} from '@/lib/cotizadorHelper';
import { obtenerTarifasPorKm } from '@/app/actions/tarifas';
import { closestLower } from '@/lib/tarifasUtils';
import { AlertMessage } from '@/components/ui/alert-message';
import Footer2 from '@/components/common/Footer/Footer2';

import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { FaChevronLeft, FaArrowRight, FaPaperPlane, FaMapMarkerAlt, FaInfoCircle, FaRedo } from 'react-icons/fa';
import { PopoverLayout } from '@/components/headless-ui';

/* -------------------------------------------------------------------------- */
/* --------------------------------- TYPES ---------------------------------- */
/* -------------------------------------------------------------------------- */

interface Terminal {
  nombre: string;
  coordinates: {
    lat: number;
    lon: number;
  };
}

// Constante para la opción de terminal más cercana
// IMPORTANTE: Esta opción debe mantenerse siempre en el selector de terminales
// ya que permite al usuario obtener automáticamente la terminal más cercana a su destino
const TERMINAL_MAS_CERCANA = 'Terminal más cercana';

const TERMINALES: Terminal[] = [
  { nombre: 'AXFALTEC', coordinates: { lat: 18.4354445, lon: -93.2003204 } },
  { nombre: 'TUXPAN', coordinates: { lat: 20.951914792440803, lon: -97.3368904753723 } },
  { nombre: 'VOPAK', coordinates: { lat: 19.2154227, lon: -96.1377359 } },
  { nombre: 'VALERO', coordinates: { lat: 19.2452536, lon: -96.1734283 } },
  { nombre: 'TIZAYUCA', coordinates: { lat: 19.8221027, lon: -98.9606588 } },
];

const COORDENADAS_DEFAULT = {
  latitude: 19.3724787,
  longitude: -99.1794602,
  zoom: 5.7
};

/* -------------------------------------------------------------------------- */
/* ---------------------------- COMPONENTE MAIN ----------------------------- */
/* -------------------------------------------------------------------------- */

export default function Tarifas() {
  const router = useRouter();

  /* --------------------------------- STATE -------------------------------- */
  const [cantidadlitros, setCantidadLitros] = useState<string>('');
  const [estadoSeleccionado, setEstadoSeleccionado] = useState<string>('');
  const [ciudadSeleccionada, setCiudadSeleccionada] = useState<string>('');
  const [codigopostal, setCodigoPostal] = useState<string>('');
  const [terminalSeleccionada, setTerminalSeleccionada] = useState<string>(TERMINAL_MAS_CERCANA);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [mostrarResultado, setMostrarResultado] = useState<boolean>(false);
  const [tarifaFinal, setTarifaFinal] = useState<number>(0);
  const [distanciaCalculada, setDistanciaCalculada] = useState<number>(0);
  const [loadingDistance, setLoadingDistance] = useState<boolean>(false);
  // Nuevos estados para almacenar la última consulta y su resultado
  const [ultimaConsulta, setUltimaConsulta] = useState<{
    estado: string;
    ciudad: string;
    cp: string;
    terminal: string;
  } | null>(null);
  const [ultimaDistancia, setUltimaDistancia] = useState<number>(0);
  const [copiado, setCopiado] = useState<boolean>(false);

  // Función para copiar la tarifa al portapapeles
  const copiarTarifa = () => {
    navigator.clipboard.writeText(tarifaFinal.toFixed(2))
      .then(() => {
        setCopiado(true);
        setTimeout(() => setCopiado(false), 2000);
      })
      .catch(err => {
        console.error('Error al copiar: ', err);
      });
  };

  // Coordenadas del usuario para el mapa
  const [userlatitude, setUserLatitude] = useState<number>(19.3724787);
  const [userlongitude, setUserLongitude] = useState<number>(-99.1794602);
  const [userzoom, setUserZoom] = useState<number>(5.7);

  // Estado para almacenar las tarifas por kilómetro
  const [tarifaPorKmMapping, setTarifaPorKmMapping] = useState<Record<string, number>>({});
  const [tarifasCargadas, setTarifasCargadas] = useState<boolean>(false);

  // Estado para manejar alertas
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertType, setAlertType] = useState<'error' | 'success' | 'info'>('info');
  const [showAlert, setShowAlert] = useState<boolean>(false);

  // Función para mostrar una alerta
  const showAlertMessage = (message: string, type: 'error' | 'success' | 'info' = 'info') => {
    setAlertMessage(message);
    setAlertType(type);
    setShowAlert(true);

    // Opcionalmente, ocultar la alerta después de un tiempo
    // setTimeout(() => setShowAlert(false), 5000);
  };

  // Cargar las tarifas por kilómetro al montar el componente
  useEffect(() => {
    async function cargarTarifas() {
      try {
        console.log('Intentando cargar tarifas desde la base de datos...');
        const resultado = await obtenerTarifasPorKm();
        if (resultado && resultado.success && Object.keys(resultado.data).length > 0) {
          console.log('Tarifas por kilómetro cargadas:', resultado.data);
          setTarifaPorKmMapping(resultado.data);
          setTarifasCargadas(true);
        } else {
          console.error('No se pudieron obtener tarifas de la base de datos:', resultado?.error || 'Respuesta vacía');
          showAlertMessage('No se pudieron cargar las tarifas. Por favor, intente nuevamente más tarde.', 'error');
        }
      } catch (error) {
        console.error('Error al cargar tarifas por kilómetro:', error);
        showAlertMessage('No se pudieron cargar las tarifas. Por favor, intente nuevamente más tarde.', 'error');
      }
    }

    cargarTarifas();
  }, []);

  function findClosestDistribuidora(coordinates: { lat: number; lon: number }) {
    // Usar la lista de terminales definida globalmente
    const distribuidoras = TERMINALES;

    let closest = distribuidoras[0];
    let minDistance = calculateDistance(coordinates, closest.coordinates);

    for (const distribuidora of distribuidoras) {
      const distance = calculateDistance(coordinates, distribuidora.coordinates);
      if (distance < minDistance) {
        minDistance = distance;
        closest = distribuidora;
      }
    }

    return closest;
  }

  // Función para calcular distancia usando OSRM a través de nuestra API route
  function calculateDistanceOSRM(coord1: { lat: number; lon: number }, coord2: { lat: number; lon: number }): Promise<number> {
    console.log("Enviando solicitud a API route para cálculo de distancia");
    
    return fetch('/api/calculate-distance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        origin: coord1,
        destination: coord2
      }),
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Error en API route: ${response.status} ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      if (data.error) {
        throw new Error(`Error en cálculo de distancia: ${data.error}`);
      }
      console.log("Distancia calculada (km):", data.distance);
      return data.distance;
    })
    .catch(error => {
      console.error('Error al calcular distancia:', error);
      
      // Usar el cálculo de Haversine como fallback
      console.log("Usando cálculo de distancia Haversine como fallback.");
      const fallbackDistance = calculateDistance(coord1, coord2);
      console.log("Distancia Haversine (fallback, km):", fallbackDistance);
      return fallbackDistance;
    });
  }

  // Mantener la función original como fallback
  function calculateDistance(coord1: { lat: number; lon: number }, coord2: { lat: number; lon: number }) {
    const R = 6371; // Radio de la Tierra en km
    const dLat = deg2rad(coord2.lat - coord1.lat);
    const dLon = deg2rad(coord2.lon - coord1.lon);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(coord1.lat)) * Math.cos(deg2rad(coord2.lat)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  function deg2rad(deg: number) {
    return deg * (Math.PI / 180);
  }

  // Utilizamos la función closestLower importada del server action

  const reiniciarCotizacion = () => {
    // Reiniciar estados
    setCantidadLitros('');
    setEstadoSeleccionado('');
    setCiudadSeleccionada('');
    setCodigoPostal('');
    setTerminalSeleccionada(TERMINAL_MAS_CERCANA);
    setCurrentStep(1);
    setMostrarResultado(false); // Esto quitará el marcador del mapa
    setTarifaFinal(0);
    setDistanciaCalculada(0);

    // Reiniciar coordenadas del mapa a valores por defecto
    setUserLatitude(COORDENADAS_DEFAULT.latitude);
    setUserLongitude(COORDENADAS_DEFAULT.longitude);
    setUserZoom(COORDENADAS_DEFAULT.zoom);
  };

  const calcularCotizacion = async (e: React.FormEvent) => {
    e.preventDefault();
    if (currentStep !== 2) return;

    // Verificar si las tarifas se han cargado
    if (!tarifasCargadas || Object.keys(tarifaPorKmMapping).length === 0) {
      showAlertMessage('No se han cargado las tarifas. Por favor, recargue la página e intente nuevamente.', 'error');
      return;
    }

    // Validar que todos los campos necesarios estén completos
    if (!cantidadlitros) {
      showAlertMessage('Por favor, seleccione una cantidad de combustible', 'info');
      setCurrentStep(1);
      return;
    }

    if (!terminalSeleccionada) {
      showAlertMessage('Por favor, seleccione una terminal de origen', 'info');
      setCurrentStep(1);
      return;
    }

    if (!estadoSeleccionado || !ciudadSeleccionada) {
      showAlertMessage('Por favor, seleccione un estado y una ciudad', 'info');
      return;
    }

    // Verificar si solo cambió la cantidad de combustible
    const mismaUbicacion = ultimaConsulta && 
      ultimaConsulta.estado === estadoSeleccionado &&
      ultimaConsulta.ciudad === ciudadSeleccionada &&
      ultimaConsulta.cp === codigopostal &&
      ultimaConsulta.terminal === terminalSeleccionada;

    let distanceKm = 0;
    
    if (mismaUbicacion) {
      // Si solo cambió la cantidad, usar la distancia calculada anteriormente
      console.log('Solo cambió la cantidad de combustible, reutilizando distancia calculada:', ultimaDistancia);
      distanceKm = ultimaDistancia;
    } else {
      // Si cambió la ubicación, calcular nueva distancia
      setLoadingDistance(true);
      
      try {
        // Construir direcciones para geocodificación
        const direccionestructurada = `${codigopostal || ''} ${ciudadSeleccionada},${estadoSeleccionado},México`;
        const ciudaddireccion = `${ciudadSeleccionada},${estadoSeleccionado},México`;

        console.log('Buscando coordenadas para:', direccionestructurada);

        // Intentar geocodificar con dirección estructurada primero
        let coords = await geocoder(direccionestructurada);

        // Si falla, intentar con solo ciudad y estado
        if (!coords) {
          console.log('Geocodificación con dirección estructurada falló, intentando con:', ciudaddireccion);
          coords = await geocoder(ciudaddireccion);
        }

        if (!coords) {
          showAlertMessage('No se pudieron encontrar las coordenadas para la ubicación seleccionada. Por favor, intente con otra ubicación.', 'error');
          setLoadingDistance(false);
          return;
        }

        console.log('Coordenadas encontradas:', coords);

        // Actualizar coordenadas en el mapa
        setUserLatitude(coords.lat);
        setUserLongitude(coords.lon);
        setUserZoom(16);

        // Determinar qué terminal usar
        let selectedTerminal;
        let terminalNombre;

        if (terminalSeleccionada === TERMINAL_MAS_CERCANA) {
          // Si el usuario eligió "TERMINAL MÁS CERCANA", encontrar la más cercana
          selectedTerminal = findClosestDistribuidora(coords);
          terminalNombre = selectedTerminal.nombre;
          console.log('Terminal más cercana encontrada:', terminalNombre);

          // Actualizar el estado con la terminal más cercana encontrada
          setTerminalSeleccionada(terminalNombre);
        } else {
          // Usar la terminal seleccionada por el usuario
          selectedTerminal = TERMINALES.find(terminal => terminal.nombre === terminalSeleccionada);

          if (!selectedTerminal) {
            showAlertMessage('No se encontró la terminal seleccionada. Por favor, seleccione otra terminal.', 'error');
            setCurrentStep(1);
            setLoadingDistance(false);
            return;
          }

          terminalNombre = selectedTerminal.nombre;
          console.log('Terminal seleccionada por el usuario:', terminalNombre);
        }

        // Calcular distancia desde la terminal seleccionada hasta el destino
        distanceKm = await calculateDistanceOSRM(
          { lat: coords.lat, lon: coords.lon },
          selectedTerminal.coordinates
        );
        console.log('Distancia calculada por OSRM (km):', distanceKm);

        // Guardar la última consulta y su resultado
        setUltimaConsulta({
          estado: estadoSeleccionado,
          ciudad: ciudadSeleccionada,
          cp: codigopostal,
          terminal: terminalSeleccionada
        });
        setUltimaDistancia(distanceKm);
        
      } catch (err) {
        console.error('Error al calcular coordenadas o distancia:', err);
        showAlertMessage('Ocurrió un error al calcular la distancia. Por favor, intente nuevamente.', 'error');
        setLoadingDistance(false);
        return;
      } finally {
        setLoadingDistance(false);
      }
    }

    // Guardar la distancia calculada en el estado
    setDistanciaCalculada(distanceKm);

    // Determinar tarifa base según distancia
    const key = closestLower(distanceKm, tarifaPorKmMapping);
    console.log('Rango de tarifa seleccionado:', key);

    let tarifa = tarifaPorKmMapping[key];
    console.log('Tarifa base:', tarifa);

    // Ajustar tarifa según cantidad de litros
    if (cantidadlitros === '31,000') {
      tarifa = tarifa * 1.20;
      console.log('Tarifa ajustada para 31,000L (+25%):', tarifa);
    } else if (cantidadlitros === '45,000') {
      tarifa = tarifa * 1.10;
      console.log('Tarifa ajustada para 45,000L (+0.1):', tarifa);
    }

    // Actualizar estado con el resultado
    setTarifaFinal(tarifa);
    setMostrarResultado(true);

    console.log('Cálculo de tarifa completado:', tarifa);
  };

  /* -------------------------------------------------------------------------- */
  /* ---------------------------------- UI ----------------------------------- */
  /* -------------------------------------------------------------------------- */

  return (
    <div className="text-gray-700">
    <section className="min-h-screen bg-gray-50 flex items-center justify-center pt-36 pb-8 sm:pt-40 md:pt-44 sm:pb-10 md:pb-14">
      <div className="w-full max-w-6xl px-3 sm:px-4 lg:px-6 xl:px-0">
        {/* Mostrar alerta si es necesario */}
        {showAlert && (
          <div className="mb-6 mt-2 sm:mt-0 relative z-20">
            <AlertMessage
              type={alertType}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          </div>
        )}

        {/* hero - mejorado para responsividad */}
        <header className="text-center mb-6 sm:mb-8 md:mb-10 px-4 sm:px-6 md:px-0">
          <h1 className="text-black text-4xl font-bold leading-tight">
            <span className="block sm:inline">Calcula tus{' '}</span>
            <span className="relative inline-block mt-1 sm:mt-0">
              <span className="relative z-10">tarifas de logística</span>
              <span className="absolute -inset-x-1 sm:-inset-x-0.5 -inset-y-1 -z-10 bg-amber-300/60 rounded-lg transform -rotate-1 sm:rotate-0"></span>
            </span>
          </h1>
          <p className="mt-3 sm:mt-4 text-base sm:text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
            Calcula al instante el Costo de transportar combustible desde cualquier terminal hasta donde lo necesites.
          </p>
        </header>

        {/* Card grid */}
        <div className="grid md:grid-cols-12 bg-white shadow-[1px_0px_30px_0px_rgba(0,0,0,0.1)] rounded-xl mb-6 overflow-hidden">
          {/* Formulario */}
          <div className="bg-white border border-gray-200/70 shadow-[1px_0px_15px_0px_rgba(0,0,0,0.1)] p-6 sm:p-8 md:p-10 lg:p-12 rounded-t-xl md:rounded-t-none md:rounded-s-xl xl:col-span-5 md:col-span-5 relative z-10">
            {/* No hay botón de reiniciar cotización aquí */}

            {/* Indicador de progreso */}
            <div className="bg-gray-100/40 rounded-lg py-2 px-4 mb-6 text-center">
              <p className="text-sm text-gray-600">Paso {currentStep} de 2</p>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${(currentStep / 2) * 100}%` }}
                ></div>
              </div>
            </div>

            <h2 className="text-lg sm:text-xl font-medium mb-4 sm:mb-6 text-gray-800 leading-snug">
              {currentStep === 1 ? (
                <>
                  <span className="text-primary font-semibold block sm:inline">Calcula tus Tarifas:</span>
                  <span className="block sm:inline sm:ml-1">Selecciona el Volumen de Litros y la Terminal.</span>
                </>
              ) : (
                "Indica la Ubicación a la que deseas Transportar el Combustible."
              )}
            </h2>

            <form onSubmit={calcularCotizacion} className="space-y-4">
              {currentStep === 1 && (
                <>
                  <div className="space-y-6">
                    {/* Cantidad */}
                    <div>
                      <label className="block text-md font-medium text-gray-600 mb-2">
                        Cantidad de combustible <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={cantidadlitros}
                        onValueChange={setCantidadLitros}
                        required
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Seleccione una cantidad" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="31,000">31,000 lts</SelectItem>
                          <SelectItem value="45,000">45,000 lts</SelectItem>
                          <SelectItem value="62,000">62,000 lts</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Terminal */}
                    <div>
                      <label className="block text-md font-medium text-gray-600 mb-2">
                        Terminal de origen <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={terminalSeleccionada}
                        onValueChange={setTerminalSeleccionada}
                        required
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Seleccione una terminal" />
                        </SelectTrigger>
                        <SelectContent>
                          {/*
                            IMPORTANTE: Mantener siempre esta opción en el selector
                            Permite al usuario obtener automáticamente la terminal más cercana
                          */}
                          <SelectItem
                            className="border-b pb-2 mb-1"
                            key={TERMINAL_MAS_CERCANA}
                            value={TERMINAL_MAS_CERCANA}
                          >
                            {TERMINAL_MAS_CERCANA}
                          </SelectItem>
                          {/* Listado de terminales específicas */}
                          {TERMINALES.map((terminal) => (
                            <SelectItem key={terminal.nombre} value={terminal.nombre}>
                              {terminal.nombre}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:justify-end pt-4 gap-3 sm:gap-2">
                      <PopoverLayout
                        menuClass="w-full sm:w-80 md:w-96 bg-white p-4 sm:p-5 rounded-xl border border-gray-100 shadow-lg z-50"
                        toggler={
                          <div
                            className="inline-flex items-center justify-center w-9 h-9 border border-gray-100 bg-white rounded-full shadow-sm hover:bg-gray-50 cursor-pointer"
                          >
                            <FaInfoCircle className="h-4 w-4 text-primary" />
                          </div>
                        }
                        togglerClass="inline-flex sm:self-center"
                      >
                        <div className="text-sm text-gray-700 space-y-1">
                          <h4 className="font-medium text-gray-900 mb-1">Incrementos por Volumen</h4>

                          <p>
                            <span className="font-medium">31,000&nbsp;lts |</span>
                            &nbsp;Se aplica un aumento del&nbsp;<span className="font-semibold text-primary">20%</span> por litro.
                          </p>

                          <p>
                            <span className="font-medium">42,000&nbsp;lts |</span>
                            &nbsp;Se aplica un aumento de&nbsp;<span className="font-semibold text-primary">10%</span> por litro.
                          </p>
                          <p>
                            <span className="font-medium">62,000&nbsp;lts |</span>
                            &nbsp;Se aplica un aumento de&nbsp;<span className="font-semibold text-primary">$0</span> por litro.
                          </p>
                        </div>
                      </PopoverLayout>

                      <button
                        type="button"
                        onClick={() => setCurrentStep(2)}
                        disabled={!cantidadlitros || !terminalSeleccionada}
                        className="w-full sm:w-auto bg-primary text-white rounded-lg text-sm inline-flex gap-2 items-center shadow-md shadow-primary/20 focus:shadow-none focus:outline focus:outline-primary/40 px-6 py-2.5 sm:py-2 justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Siguiente <FaArrowRight className="h-4 w-4 ml-1" />
                      </button>
                    </div>
                    <div className="mt-8 pt-6 border-t border-gray-200">
                    </div>
                  </div>
                </>
              )}

              {currentStep === 2 && (
                <div className="space-y-6">
                  {/* Estado */}
                  <div>
                    <label className="block text-md font-medium text-gray-600 mb-2">
                      Estado <span className="text-red-500">*</span>
                    </label>
                    <Select
                      value={estadoSeleccionado}
                      onValueChange={(newValue) => {
                        setEstadoSeleccionado(newValue);
                        setCiudadSeleccionada(''); // Solo limpiamos la ciudad, mantenemos el código postal
                      }}
                      required
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Seleccione un Estado" />
                      </SelectTrigger>
                      <SelectContent>
                        {EstadosQuote.map((estado) => (
                          <SelectItem key={estado.value} value={estado.value}>
                            {estado.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Ciudad */}
                  <div>
                    <label className="block text-md font-medium text-gray-600 mb-2">
                      Ciudad / Municipio <span className="text-red-500">*</span>
                    </label>
                    <Select
                      value={ciudadSeleccionada}
                      onValueChange={setCiudadSeleccionada}
                      required
                      disabled={!estadoSeleccionado}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Seleccione Ciudad / Municipio" />
                      </SelectTrigger>
                      <SelectContent>
                        {estadoSeleccionado && CiudadesMQuote[estadoSeleccionado]?.map((ciudad, index) => (
                          <SelectItem 
                            key={`${estadoSeleccionado}-${ciudad.value}-${index}`}
                            value={ciudad.value}
                          >
                            {ciudad.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Código Postal */}
                  <div>
                    <label className="block text-md font-medium text-gray-600 mb-2">
                      Código Postal <span className="text-xs text-gray-500">(opcional, recomendado)</span>
                    </label>
                    <Input
                      type="text"
                      value={codigopostal}
                      onChange={(e) => setCodigoPostal(e.target.value)}
                      placeholder="Ingrese el código postal"
                      pattern="[0-9]{5}"
                      maxLength={5}
                    />
                  </div>

                  <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-2">
                    <button
                      type="button"
                      onClick={() => setCurrentStep(1)}
                      className="order-2 sm:order-1 w-full sm:w-auto inline-flex border border-gray-100 bg-white text-gray-700 rounded-lg text-sm items-center shadow-sm px-6 py-2.5 sm:py-2 justify-center"
                    >
                      <FaChevronLeft className="h-3 w-3 me-2" /> Atrás
                    </button>

                    <button
                      type="submit"
                      disabled={!estadoSeleccionado || !ciudadSeleccionada || loadingDistance}
                      className="order-1 sm:order-2 w-full sm:w-auto inline-flex h-max bg-primary text-white rounded-lg text-sm gap-2 items-center shadow-md shadow-primary/20 focus:shadow-none focus:outline focus:outline-primary/40 px-6 py-2.5 sm:py-2 justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loadingDistance ? (
                        <>Calculando Tarifa...</>
                      ) : (
                        <><FaPaperPlane className="h-4 w-4" /> Calcular Tarifa</>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </form>

            {/* Resultado */}
            {currentStep === 2 && mostrarResultado && (
              <div className="mt-4 bg-white p-2">
                {/* Título con tarifa normal y con IVA */}
                <div className="flex justify-between items-center">
                  <h3 className="text-xs font-semibold text-gray-600 uppercase mb-">
                    Tarifa Calculada
                  </h3>
                  <h3 className="text-xs font-semibold text-gray-600 uppercase mb-">
                    Tarifa Calculada + IVA (16%)
                  </h3>
                </div>

                {/* Importes destacados en fila */}
                <div className="flex justify-between mt-1">
                  <div className="flex items-baseline gap-2">
                    <button
                      onClick={copiarTarifa}
                      className="text-xl font-extrabold text-gray-700 flex items-center gap-1 cursor-pointer hover:text-primary/90 transition-colors relative group"
                      title="Haz clic para copiar"
                    >
                      ${tarifaFinal.toFixed(2)}
                      {copiado ? (
                        <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1 px-2 rounded opacity-90">
                          ¡Copiado!
                        </span>
                      ) : (
                        <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-90 transition-opacity">
                          Copiar
                        </span>
                      )}
                    </button>
                    <span className="text-sm text-gray-500 self-end">
                      MXN/L
                    </span>
                  </div>
                  
                  <div className="flex items-baseline gap-2">
                    <span className="text-lg font-bold text-primary">
                      ${(tarifaFinal * 1.16).toFixed(2)}
                    </span>
                    <span className="text-sm text-gray-500 self-end">
                      MXN/L
                    </span>
                  </div>
                </div>
                
                {/* Botón de reiniciar cotización */}
                <div className="pt-3 flex justify-center">
                  <button
                    type="button"
                    onClick={reiniciarCotizacion}
                    className="flex items-center justify-center border border-red-100 hover:border-red-200 rounded-2xl gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 text-sm font-medium transition-all py-1.5 sm:py-2 px-4 sm:px-5"
                  >
                    <FaRedo className="h-3.5 w-3.5" />
                    <span>Reiniciar cotización</span>
                  </button>
                </div>
              </div>
            )}
              {currentStep === 1 && (
                <>
                  <p className="text-sm text-gray-500 mb-4 text-center">
                    v0.2 · Actualizado el 29 de abril de 2025
                  </p>
                  <p className="text-sm text-gray-500 text-center">
                    Los precios mostrados son aproximados y pueden variar según condiciones de ruta.
                  </p>
                </>
              )}
          </div>

          {/* Mapa */}
          <div className="hidden md:block relative bg-white xl:col-span-7 md:col-span-7 rounded-e-xl">
            <div className="h-[520px] relative">
              <MapViewLibre
                userLatitude={userlatitude}
                userlongitude={userlongitude}
                userzoom={userzoom}
                terminales={TERMINALES}
                terminalSeleccionada={terminalSeleccionada}
                showUserMarker={mostrarResultado} // Solo muestra el marcador cuando mostrarResultado es true
              />
            </div>
            <div className="p-[1.27rem] border-t border-gray-100">
              <p className="my-1 flex items-center space-x-2 text-md border-gray-100 text-gray-600">
                    <span className="flex h-4 w-4 text-center items-center justify-center mr-1">
                      <svg
                        width="10"
                        height="10"
                        viewBox="0 0 10 10"
                        xmlns="http://www.w3.org/2000/svg"
                        role="img"
                        aria-label="Map marker"
                      >
                        <circle
                          cx="5"
                          cy="5"
                          r="5"
                          fill="#3A7CFB"
                        />
                      </svg>
                    </span>
                    Terminales de Almacenamiento y Reparto
              </p>
              {mostrarResultado && (
                <>
                  <p className="my-1 flex items-center space-x-2 text-md border-gray-100 text-gray-600">
                    <span className="flex h-4 w-4 text-center items-center justify-center mr-1">
                      <svg
                        width="10"
                        height="10"
                        viewBox="0 0 10 10"
                        xmlns="http://www.w3.org/2000/svg"
                        role="img"
                        aria-label="Map marker"
                      >
                        <circle
                          cx="5"
                          cy="5"
                          r="5"
                          fill="#fb7c3a"
                        />
                      </svg>
                    </span>
                    Ubicación de entrega del combustible
                  </p>
                  <p className="my-1 flex items-center space-x-2 text-md border-gray-100 text-gray-600">
                    <FaMapMarkerAlt className="w-4 h-4 text-primary" />
                    <span>Desde <span className="font-medium text-gray-800">{terminalSeleccionada.charAt(0).toUpperCase() + terminalSeleccionada.slice(1).toLowerCase()}</span> <span>hasta</span> <span className="font-medium text-gray-800">{ciudadSeleccionada.charAt(0).toUpperCase() + ciudadSeleccionada.slice(1).toLowerCase()}, {estadoSeleccionado.charAt(0).toUpperCase() + estadoSeleccionado.slice(1).toLowerCase()}</span> <span className="text-sm">({distanciaCalculada.toFixed(1)} kms)</span>.</span>
                  </p>
                </>
              )}
              <div className="flex mb-2 font-medium text-sm">
                <span className="text-gray-500">Para obtener una Cotización más precisa, ingresa el Código Postal exacto del lugar de Entrega.</span>
              </div>
            </div>
          </div>
        </div>
        <div className="text-center text-sm text-gray-500 mb-20 mt-4 lg:max-w-3xl sm:max-w-md md:max-w-lg  mx-auto">
          <p>Herramienta interna - Uso exclusivo de operaciones - No divulgar a terceros.</p>
        </div>
      </div>
    </section>
    <Footer2 socialIcon />
    </div>
  );
}
