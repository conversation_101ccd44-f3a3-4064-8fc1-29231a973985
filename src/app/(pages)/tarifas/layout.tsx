import { redirect } from 'next/navigation';
import { requireRole } from '@/lib/roles';
import { getSession } from '@/lib/auth';

export default async function TarifasLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  try {
    // Verificar que el usuario tiene el rol requerido (WORKER o superior)
    await requireRole('WORKER');

    // Si llegamos aquí, el usuario tiene los permisos necesarios
    return <>{children}</>;
  } catch (error) {
    // Obtener la sesión para verificar si el usuario está autenticado
    const session = await getSession();

    if (!session?.user) {
      // Si no hay sesión, redirigir a la página de inicio de sesión
      redirect('/auth/signin?callbackUrl=/tarifas');
    } else {
      // Si hay sesión pero no tiene el rol requerido, redirigir a la página de inicio
      redirect('/');
    }
  }
}
