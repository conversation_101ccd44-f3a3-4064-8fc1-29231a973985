// Definición de los tipos de productos disponibles
export const TipoProducto = {
  GASOLINA_REGULAR: 'Gasolina Regular',
  GASOLINA_PREMIUM: 'Gasolina Premium',
  DIESEL: 'Diesel'
} as const;

// Tipo para los productos
export type TipoProducto = typeof TipoProducto[keyof typeof TipoProducto];

// Función para obtener todos los productos disponibles
export function getProductosArray(): string[] {
  return Object.values(TipoProducto);
}
