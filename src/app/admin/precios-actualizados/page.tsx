import { redirect } from "next/navigation";
import { requireRole } from "@/lib/roles";
import Link from "next/link";

export default async function AdminPage() {
  try {
    // Verificar que el usuario tiene rol de administrador
    const user = await requireRole("ADMIN");
    
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">Panel de Administración en Construcción</h1>
        <p className="mb-4">
          Bienvenido, {user.name || user.email}. Tienes acceso a esta página porque tienes el rol de {user.role}.
        </p>
        <div className="bg-white shadow-lg rounded-xl p-6">
          <h2 className="text-xl font-semibold mb-3">Funciones de Administrador</h2>
          <p className="text-gray-600">
            Esta página solo es accesible para usuarios con rol de ADMIN o SUPER_ADMIN.
            Los roles solo pueden ser asignados a través de Prisma Studio.
          </p>
          <div className="mt-4">
            <Link 
              href="/" 
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Volver al inicio
            </Link>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    // Si el usuario no tiene permisos, redirigir a la página de inicio
    redirect("/");
  }
}
