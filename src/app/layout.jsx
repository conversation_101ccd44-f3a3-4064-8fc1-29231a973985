import Script from 'next/script'
import Image from 'next/image'
import { <PERSON><PERSON> } from 'next/font/google'
import NextTopLoader from 'nextjs-toploader'
import ClientProviders from '@/components/ClientProviders'
import { GoogleTagManager } from '@next/third-parties/google'
import { GoogleAdsInjector } from "@/lib/googleAds.ts";
import WhatsAppButton from '@/components/WhatsAppButton'

import appLogo from '@/assets/images/logo-combustibles-cassiopeia.png'
import './globals.css'
import 'aos/dist/aos.css'

const roboto = Roboto({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto',
  weight: ['100', '300', '400', '500', '700', '900']
});

export const metadata = {
  title: {
    template: '%s | Combustibles Cassiopeia - Distribución de Combustible al Mayoreo en México',
    default: 'Combustibles Cassiopeia - Venta de Comustible al Mayoreo de la más alta Calidad',
  },
  description: 'Cassiopeia Petrolíferos | Líder en Hidrocarburos de Alta Calidad y Suministro Certificado en México. Suministramos gasolinas y diésel de alta calidad con cumplimiento normativo bajo la Norma Oficial Mexicana (NOM-016-CRE-2016).',
  metadataBase: new URL('https://cassiopeiamx.com'),
  authors: [{ name: 'Cassiopeia Industries' }],
  openGraph: {
    title: 'Líder en Hidrocarburos de Alta Calidad y Suministro Certificado en México.',
    description: 'Combustibles Cassiopeia | Suministramos Gasolinas y Diésel de alta Calidad con cumplimiento normativo bajo la Norma Oficial Mexicana (NOM-016-CRE-2016).',
    url: 'https://cassiopeiamx.com',
    Images: [
      {
        url: '/open-graph.png',
        width: 1200,
        height: 630,
      },
    ],
    type: 'website',
    siteName: 'Combustibles Cassiopeia',
    locale: 'es_MX',
    countryName: 'México',
    robots: {
      index: true,
      follow: true,
      nocache: true,
      maxSnippet: -1,
      maxImagePreview: 'large',
      maxVideoPreview: -1,
    },
    icons: [
      {
        url: '/favicon.ico',
        type: 'image/x-icon',
      },
    ],
    alternates: {
      canonical: 'https://cassiopeia.com.mx',
    },
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: '/open-graph.png',
      },
    ],
  },
  alternates: {
    canonical: 'https://cassiopeia.com.mx',
  },
  icons: [
    {
      url: '/favicon.ico',
      type: 'image/x-icon',
    },
  ],
}

const splashScreenStyles = `
  #splash-screen {
    position: fixed;
    top: 50%;
    left: 50%;
    background: white;
    display: flex;
    height: 100%;
    width: 100%;
    transform: translate(-50%, -50%);
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: all 0.3s ease-out; /* Reducido de 0.5s a 0.3s */
  }

  #splash-screen.remove {
    opacity: 0;
    pointer-events: none;
    visibility: hidden;
  }
`

export default function RootLayout({ children }) {
  
  const gtmId = process.env.NEXT_PUBLIC_GOOGLE_GTM_ID
  const adsId = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID

  return (
    <html lang="es">
      <head>
        {/* Google Tag Manager 2024 */}
        <GoogleTagManager gtmId={gtmId} />
        {/* Fin Google Tag Manager */}

        {/* Google Ads octubre 2024 gtag.js */}
        <Script
          async
          src={`https://www.googletagmanager.com/gtag/js?id=${adsId}`}
        />
        <Script id="gtag-config">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config','${adsId}');
          `}
        </Script>
        {/* Estilos para splash screen */}
        <style>{splashScreenStyles}</style>
        
        {/* WhatsApp Button CSS */}
        <link
          rel="stylesheet"
          href="https://cdn.positus.global/production/resources/robbu/whatsapp-button/whatsapp-button.css"
        />
      </head>
      <body className={`${roboto.className} relative w-full max-w-[100vw] overflow-x-hidden`}>
        <GoogleAdsInjector />
        <NextTopLoader color='#2864e6' showSpinner={false} />
        <ClientProviders>
          <div id="splash-screen">
            <Image 
              alt="Logo Cassiopeia Petrolíferos | Combustibles Cassiopeia" 
              width={355} 
              src={appLogo} 
              style={{ height: '10%', width: 'auto' }} 
              priority
              loading="eager"
            />
          </div>
          {children}
          <WhatsAppButton />
        </ClientProviders>
      </body>
    </html>
  )
}
