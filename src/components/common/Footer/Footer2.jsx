'use client'
import Image from 'next/image'
import Link from 'next/link'
import { footerLinks } from './data'
import { BusinessInfoSection } from './BusinessInfo'
import { Facebook, Linkedin, Instagram } from 'lucide-react'

const year = new Date().getFullYear();

//image
import logoDark from '@/assets/images/logo-combustibles-cassiopeia.png'

const Footer2 = ({ socialIcon }) => {
  return (
    <>
    <BusinessInfoSection />
    <footer className="py-3 bg-gray-100 relative">
      <div className="container pt-4">
        <div className="grid lg:grid-cols-2 grid-cols-1 gap-14 py-6">
          <div>
            <Link href="/">
              <Image
                alt="Logo Cassiopeia Petrolífero | Combustibles Cassiopeia"
                height={32}
                src={logoDark}
                className="h-8"
              />
            </Link>
            <p className="text-gray-500/80 mt-5 w-4/5">
              En Cassiopeia Petrolíferos, garantizamos un suministro seguro y eficiente de productos petrolíferos de alta calidad con confianza y responsabilidad.
            </p>
          </div>
          <div>
            <div className="flex flex-col sm:flex-row gap-14 flex-wrap sm:flex-nowrap justify-between">
              {Object.keys(footerLinks)
                .slice(0, 3)
                .map((title, idx) => {
                  return (
                    <div className="flex flex-col gap-3" key={idx}>
                      <h5 className="mb-3 uppercase">{title}</h5>
                      {footerLinks[title].links.map((link, idx) => {
                        return (
                          <div className="text-gray-500/80" key={idx}>
                            <Link href={link === 'Aviso de privacidad' ? '/aviso-de-privacidad' :
                                        link === 'Nosotros' ? '/nosotros' :
                                        link === 'Servicios' ? '/servicios' :
                                        ''}>
                              {link}
                            </Link>
                          </div>
                        )
                      })}
                    </div>
                  )
                })}
            </div>
          </div>
        </div>

        <hr className="my-8 border-t border-gray-300" />

        <div className="flex flex-col md:flex-row justify-between items-center text-center md:text-start">
          <p className="text-muted-foreground text-sm">
            {year} © Cassiopeia Petrolíferos. Todos los derechos reservados.
          </p>
          <div className="flex gap-4 mt-4 md:mt-0">
            <a
              href="https://www.facebook.com/CombustiblesCassiopeia"
              target="_blank"
              aria-label="Visítanos en Facebook"
            >
              <Facebook className="w-7 h-7 hover:text-blue-500" />
            </a>
            <a
              href="https://www.linkedin.com/company/cassiopeiapetroliferos/"
              target="_blank"
              aria-label="Síguenos en LinkedIn"
            >
              <Linkedin className="w-7 h-7 hover:text-blue-500" />
            </a>
            <a
              href="https://www.instagram.com/combustiblescassiopeia/"
              target="_blank"
              aria-label="Síguenos en Instagram"
            >
              <Instagram className="w-7 h-7 hover:text-blue-500" />
            </a>
          </div>
        </div>
      </div>
    </footer>
    </>
  )
}

export default Footer2
