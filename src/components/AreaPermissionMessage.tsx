"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { canEditAreaData } from "@/lib/areaAccess";
import { AlertCircle, Lock, Eye, Edit3 } from "lucide-react";

interface AreaPermissionMessageProps {
  areaName: string;
  action?: "view" | "edit" | "add" | "delete";
  className?: string;
  showOnlyIfRestricted?: boolean;
}

export default function AreaPermissionMessage({ 
  areaName, 
  action = "edit", 
  className = "",
  showOnlyIfRestricted = false 
}: AreaPermissionMessageProps) {
  const { data: session } = useSession();
  const [permissions, setPermissions] = useState<{
    canEdit: boolean;
    canView: boolean;
    userArea?: string;
    reason?: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkPermissions() {
      if (!session?.user?.id) {
        setLoading(false);
        return;
      }

      try {
        const result = await canEditAreaData(areaName);
        setPermissions(result);
      } catch (error) {
        console.error("Error al verificar permisos:", error);
      } finally {
        setLoading(false);
      }
    }

    checkPermissions();
  }, [session?.user?.id, areaName]);

  if (loading) {
    return null; // No mostrar nada mientras carga
  }

  if (!permissions) {
    return null;
  }

  // Si showOnlyIfRestricted es true, solo mostrar si hay restricciones
  if (showOnlyIfRestricted && permissions.canEdit) {
    return null;
  }

  // Determinar el tipo de mensaje según la acción y permisos
  const getMessageConfig = () => {
    if (!permissions.canView) {
      return {
        type: "error" as const,
        icon: Lock,
        title: "Acceso Denegado",
        message: "No tienes permisos para ver esta área.",
        bgColor: "bg-red-50",
        borderColor: "border-red-200",
        textColor: "text-red-800",
        iconColor: "text-red-600"
      };
    }

    if (!permissions.canEdit && ["edit", "add", "delete"].includes(action)) {
      return {
        type: "warning" as const,
        icon: AlertCircle,
        title: "Solo Lectura",
        message: permissions.reason || "No puedes modificar datos en esta área.",
        bgColor: "bg-amber-50",
        borderColor: "border-amber-200",
        textColor: "text-amber-800",
        iconColor: "text-amber-600"
      };
    }

    if (permissions.canView && permissions.canEdit) {
      return {
        type: "success" as const,
        icon: Edit3,
        title: "Acceso Completo",
        message: "Puedes ver y editar datos en esta área.",
        bgColor: "bg-green-50",
        borderColor: "border-green-200",
        textColor: "text-green-800",
        iconColor: "text-green-600"
      };
    }

    return {
      type: "info" as const,
      icon: Eye,
      title: "Solo Lectura",
      message: "Puedes ver los datos pero no modificarlos.",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      textColor: "text-blue-800",
      iconColor: "text-blue-600"
    };
  };

  const config = getMessageConfig();
  const Icon = config.icon;

  return (
    <div className={`p-3 rounded-lg border ${config.bgColor} ${config.borderColor} ${className}`}>
      <div className="flex items-start gap-3">
        <Icon className={`h-5 w-5 ${config.iconColor} flex-shrink-0 mt-0.5`} />
        <div className="flex-1 min-w-0">
          <h4 className={`text-sm font-medium ${config.textColor}`}>
            {config.title}
          </h4>
          <p className={`text-sm ${config.textColor} mt-1`}>
            {config.message}
          </p>
          {permissions.userArea && session?.user?.role === "WORKER" && (
            <p className={`text-xs ${config.textColor} mt-2 opacity-75`}>
              Tu área asignada: <span className="font-medium">{permissions.userArea}</span>
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

// Componente específico para mostrar solo cuando hay restricciones
export function AreaRestrictionBanner({ areaName, action = "edit", className = "" }: AreaPermissionMessageProps) {
  return (
    <AreaPermissionMessage 
      areaName={areaName} 
      action={action} 
      className={className}
      showOnlyIfRestricted={true}
    />
  );
}

// Hook personalizado para usar en componentes
export function useAreaPermissions(areaName: string) {
  const { data: session } = useSession();
  const [permissions, setPermissions] = useState<{
    canEdit: boolean;
    canView: boolean;
    userArea?: string;
    reason?: string;
    loading: boolean;
  }>({
    canEdit: false,
    canView: false,
    loading: true
  });

  useEffect(() => {
    async function checkPermissions() {
      if (!session?.user?.id) {
        setPermissions(prev => ({ ...prev, loading: false }));
        return;
      }

      try {
        const result = await canEditAreaData(areaName);
        setPermissions({
          ...result,
          loading: false
        });
      } catch (error) {
        console.error("Error al verificar permisos:", error);
        setPermissions(prev => ({ ...prev, loading: false }));
      }
    }

    checkPermissions();
  }, [session?.user?.id, areaName]);

  return permissions;
}
