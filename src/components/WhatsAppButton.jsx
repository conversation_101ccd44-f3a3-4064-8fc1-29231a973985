
'use client';
import React from 'react';
import { usePathname } from 'next/navigation';

export default function WhatsAppButton() {
  const pathname = usePathname();
  
  // No mostrar el botón en las páginas de signin y signup
  if (pathname === '/auth/signin' || pathname === '/auth/signup' || pathname === '/auth/error' || pathname === '/tarifas') {
    return null;
  }
  
  const handleClick = (e) => {
    e.preventDefault();
    const url = 'https://api.whatsapp.com/send/?phone=5559513012';
    const adscompleteId = process.env.NEXT_PUBLIC_GOOGLE_ADS_COMPLETE_ID;
    
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'conversion', {
        send_to: adscompleteId,
        event_callback: () => {
          window.open(url, '_blank');
        },
      });
    } else {
      window.open(url, '_blank');
    }
  };

  return (
    <a
      id="robbu-whatsapp-button"
      className="right cursor-pointer"
      style={{ transform: 'scale(0.86)' }}
      target="_blank"
      rel="noopener noreferrer"
      href="https://api.whatsapp.com/send/?phone=5559513012"
      onClick={handleClick}
    >
      <img
        src="https://cdn.positus.global/production/resources/robbu/whatsapp-button/whatsapp-icon.svg"
        alt="¡Cotiza Ahora en WhatsApp!"
      />
    </a>
  );
}

