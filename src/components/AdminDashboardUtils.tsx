"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';

// Registrar componentes de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler,
  ChartDataLabels
);

// Plugin personalizado para establecer fondo blanco en todas las gráficas
const backgroundColorPlugin = {
  id: 'backgroundColor',
  beforeDraw: (chart: any) => {
    const ctx = chart.canvas.getContext('2d');
    ctx.save();
    ctx.globalCompositeOperation = 'destination-over';
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, chart.width, chart.height);
    ctx.restore();
  }
};

// Registrar el plugin globalmente
ChartJS.register(backgroundColorPlugin);

// Configuración global de Chart.js para establecer fondo blanco por defecto
ChartJS.defaults.backgroundColor = '#ffffff';
// Removido: ChartJS.defaults.color para usar el color por defecto de Chart.js

// Componente SemicircleGauge
interface SemicircleGaugeProps {
  actualValue: number
  maxValue?: number
  label?: string
}

export function SemicircleGauge({ actualValue, maxValue = 40, label = "Neutral" }: SemicircleGaugeProps) {
  // Calcular el valor inicial para evitar animación desde 0 en la primera carga
  const getInitialValue = () => {
    const score = Math.min(8, Math.max(1, Math.ceil((actualValue / maxValue) * 8)));
    return ((score - 1) / 7) * 100;
  };

  const [animatedValue, setAnimatedValue] = useState(getInitialValue);
  const animationRef = useRef<number | null>(null);
  const isFirstRender = useRef(true);

  // Calcular el score basado en el valor actual y máximo
  const getScore = (value: number, max: number) => {
    const step = max / 8;
    if (value >= max) return 8;
    if (value >= max - step) return 7;
    if (value >= max - (step * 2)) return 6;
    if (value >= max - (step * 3)) return 5;
    if (value >= max - (step * 4)) return 4;
    if (value >= max - (step * 5)) return 3;
    if (value >= max - (step * 6)) return 2;
    return 1;
  };

  const score = getScore(actualValue, maxValue);

  // Convertir score (1-8) a porcentaje (0-100) para el tacómetro
  const targetValue = ((score - 1) / 7) * 100;

  // Animar el valor cuando cambie
  useEffect(() => {
    // En la primera renderización, establecer el valor sin animación
    if (isFirstRender.current) {
      setAnimatedValue(targetValue);
      isFirstRender.current = false;
      return;
    }

    // Cancelar animación anterior si existe
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    const duration = 1200; // 1.2 segundos de animación
    const startTime = Date.now();
    const startValue = animatedValue;
    const valueChange = targetValue - startValue;

    // Si no hay cambio significativo, no animar
    if (Math.abs(valueChange) < 0.5) {
      setAnimatedValue(targetValue);
      return;
    }

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Función de easing suave (ease-out-cubic)
      const easeOut = 1 - Math.pow(1 - progress, 3);

      const currentValue = startValue + (valueChange * easeOut);
      setAnimatedValue(currentValue);

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        // Asegurar que termine exactamente en el valor objetivo
        setAnimatedValue(targetValue);
        animationRef.current = null;
      }
    };

    animationRef.current = requestAnimationFrame(animate);

    // Cleanup function
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    };
  }, [targetValue]);

  // Calcular la posición del indicador basado en el valor animado
  // Coordenadas exactas basadas en las líneas SVG del semicírculo
  const centerX = 72  // Centro horizontal del semicírculo
  const centerY = 68  // Centro vertical del semicírculo (línea base)
  const radius = 59   // Radio del semicírculo (igual al de las líneas SVG)

  // Calcular ángulo: de π (180°) a 0 (0°) para ir de izquierda a derecha
  // animatedValue va de 0 a 100, lo convertimos a ángulo de π a 0
  // Verificación de puntos clave:
  // - animatedValue = 0 → angle = π → x = 72 + 59*(-1) = 13, y = 68 - 59*0 = 68 ✓
  // - animatedValue = 100 → angle = 0 → x = 72 + 59*1 = 131, y = 68 - 59*0 = 68 ✓
  // - animatedValue = 50 → angle = π/2 → x = 72 + 59*0 = 72, y = 68 - 59*1 = 9 ✓
  const angle = Math.PI - (animatedValue / 100) * Math.PI

  // Posición del indicador exactamente sobre la línea del semicírculo
  const indicatorX = centerX + radius * Math.cos(angle)
  const indicatorY = centerY - radius * Math.sin(angle)

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        height: "100%",
      }}
    >
      <div
        style={{
          position: "relative",
          width: "144px",
          height: "78px",
          display: "flex",
          justifyContent: "center",
          alignItems: "flex-end",
        }}
      >
        <svg width="144" height="78" viewBox="0 0 144 78">
          {/* Segmento rojo */}
          <path
            d="M 13 67.99999999999999 A 59 59 0 0 1 20.699799159192082 38.85742987153037"
            stroke="#EA3943"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Segmento naranja */}
          <path
            d="M 25.25491104204376 32.001435329825206 A 59 59 0 0 1 49.136580399325936 13.610074056278464"
            stroke="#EA8C00"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Segmento amarillo */}
          <path
            d="M 56.928700281788366 10.957420072336895 A 59 59 0 0 1 87.07129971821165 10.957420072336895"
            stroke="#F3D42F"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Segmento verde claro */}
          <path
            d="M 94.86341960067408 13.61007405627847 A 59 59 0 0 1 118.74508895795626 32.00143532982522"
            stroke="#93D900"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Segmento verde */}
          <path
            d="M 123.30020084080792 38.85742987153038 A 59 59 0 0 1 131 68"
            stroke="#16C784"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Indicador circular con animación JavaScript */}
          <circle
            cx={indicatorX}
            cy={indicatorY}
            r="7"
            fill="none"
            stroke="white"
            strokeWidth="4"
          />
          <circle
            cx={indicatorX}
            cy={indicatorY}
            r="5"
            fill="black"
          />
        </svg>

        {/* Texto del valor y etiqueta */}
        <div
          style={{
            position: "absolute",
            bottom: "0px",
            width: "100%",
            textAlign: "center",
            marginBottom: "2.5px",
          }}
        >
          <div>
            <div>
              <span
                style={{
                  fontSize: "32px",
                  fontWeight: "bold",
                  color: "#000",
                  fontFamily: "system-ui, -apple-system, sans-serif",
                }}
              >
                {actualValue}
              </span>
            </div>
            <span
              className="text-xs text-gray-500"
              style={{
                display: "block",
                fontFamily: "system-ui, -apple-system, sans-serif",
              }}
            >
              {label}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * MEJORAS EN EXPORTACIÓN DE GRÁFICOS CHART.JS
 *
 * Implementación de mejores prácticas basadas en la documentación oficial:
 *
 * 1. EXPORTACIÓN PNG INDIVIDUAL:
 *    - Usa toBase64Image() de Chart.js (método recomendado)
 *    - Manejo de animaciones con onComplete callback
 *    - Fallback a canvas.toDataURL() si falla Chart.js
 *    - Método moderno con canvas.toBlob() para mejor calidad
 *    - Nombres de archivo seguros y sanitizados
 *
 * 2. EXPORTACIÓN PDF MÚLTIPLE:
 *    - Canvas temporal con dimensiones HD (900x500px)
 *    - Configuración optimizada sin animaciones
 *    - Calidad máxima (1.0) para imágenes nítidas
 *    - Layout A4 vertical con 2 gráficos por página
 *    - Método de impresión nativo del navegador
 *
 * 3. CONFIGURACIÓN OPTIMIZADA:
 *    - devicePixelRatio: 2 para pantallas HD
 *    - animation: false para exportación
 *    - responsive: false para dimensiones fijas
 *    - Fuentes y colores optimizados para impresión
 *
 * 4. ETIQUETAS DE DATOS (DATALABELS):
 *    - Configuración automática según tipo de gráfico
 *    - Formateo inteligente (porcentajes, números abreviados)
 *    - Posicionamiento adaptativo (center para pie/doughnut, end para line)
 *    - Filtrado por relevancia (oculta valores < 3% en gráficos circulares)
 *    - Fondo con color del gráfico y texto blanco para máximo contraste
 *    - Estilos optimizados: bordes redondeados, padding personalizado
 *
 * 5. MANEJO DE ERRORES:
 *    - Múltiples métodos de fallback
 *    - Logging detallado para debugging
 *    - Validación de elementos DOM
 *    - Limpieza de recursos temporales
 */

// Utilidades para exportación de gráficos (siguiendo mejores prácticas)
export const ChartExportUtils = {
  // Configuración optimizada para exportación con etiquetas de datos
  getExportConfig: (baseOptions: any) => ({
    ...baseOptions,
    responsive: false,
    maintainAspectRatio: false,
    animation: false,
    devicePixelRatio: 2, // Para mejor calidad en pantallas HD
    plugins: {
      ...baseOptions.plugins,
      legend: {
        display: true,
        position: 'bottom' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 14,
            weight: 'bold' as const
          },
          color: '#374151'
        }
      },
      // Configuración de etiquetas de datos para exportación
      datalabels: {
        align: function(context: any) {
          const chartType = context.chart.config.type;

          // Para gráficos circulares, usar configuración específica
          if (chartType === 'pie' || chartType === 'doughnut') {
            return 'center';
          }

          // Para gráficos lineales, usar lógica inteligente como en getSmartDataLabelsConfig
          if (chartType === 'line') {
            // Validar que tenemos todos los datos necesarios
            if (!context || !context.chart || !context.parsed || typeof context.parsed.y !== 'number') {
              return 'start';
            }

            const chart = context.chart;
            const yScale = chart.scales?.y;
            const xScale = chart.scales?.x;

            // Validar que las escalas existen y tienen valores válidos
            if (!yScale || typeof yScale.max !== 'number' || typeof yScale.min !== 'number') {
              return 'start';
            }

            // Verificar posición horizontal
            let isNearRightEdge = false;
            if (xScale && typeof context.dataIndex === 'number' && context.dataset?.data) {
              const dataLength = context.dataset.data.length;
              const dataIndex = context.dataIndex;
              // Considerar que está cerca del borde derecho si está en el último 30% de los datos
              isNearRightEdge = dataIndex >= (dataLength * 0.7);
            }

            // Lógica de posicionamiento inteligente
            if (isNearRightEdge) {
              return 'end'; // Si está cerca del borde derecho, mostrar al final
            } else {
              return 'start'; // Posición por defecto al inicio
            }
          }

          // Para otros tipos de gráfico
          return 'end';
        },
        anchor: function(context: any) {
          const chartType = context.chart.config.type;

          // Para gráficos circulares, usar configuración específica basada en datasetIndex
          if (chartType === 'pie' || chartType === 'doughnut') {
            const datasetIndex = context.datasetIndex || 0;
            if (datasetIndex === 0) return 'end';
            if (datasetIndex === 1) return 'center';
            return 'start';
          }

          // Para gráficos lineales, usar lógica inteligente
          if (chartType === 'line') {
            // Usar anchor dinámico para mejor posicionamiento
            if (!context || !context.chart || !context.parsed || typeof context.parsed.y !== 'number') {
              return 'start';
            }

            const xScale = context.chart.scales?.x;
            if (xScale && typeof context.dataIndex === 'number' && context.dataset?.data) {
              const dataLength = context.dataset.data.length;
              const dataIndex = context.dataIndex;
              const isNearRightEdge = dataIndex >= (dataLength * 0.7);

              if (isNearRightEdge) {
                return 'end'; // Para etiquetas al final
              }
            }

            return 'start'; // Anchor por defecto al inicio
          }

          // Para otros tipos de gráfico
          return 'end';
        },
        backgroundColor: function(context: any) {
          const dataset = context.dataset;
          const chartType = context.chart.config.type;

          // Para gráficos circulares (pie/doughnut), usar backgroundColor del dataset
          if (chartType === 'pie' || chartType === 'doughnut') {
            if (dataset.backgroundColor) {
              if (Array.isArray(dataset.backgroundColor)) {
                return dataset.backgroundColor[context.dataIndex];
              }
              return dataset.backgroundColor;
            }
          }

          // Para gráficos lineales, usar borderColor (como en getSmartDataLabelsConfig)
          if (chartType === 'line') {
            const datasetColor = dataset.borderColor || dataset.backgroundColor;
            return datasetColor;
          }

          // Para otros tipos, usar backgroundColor
          if (dataset.backgroundColor) {
            if (Array.isArray(dataset.backgroundColor)) {
              return dataset.backgroundColor[0];
            }
            return dataset.backgroundColor;
          }

          // Fallback a borderColor
          if (dataset.borderColor) {
            if (Array.isArray(dataset.borderColor)) {
              return dataset.borderColor[0];
            }
            return dataset.borderColor;
          }

          // Color por defecto
          return '#374151';
        },
        borderColor: function(context: any) {
          const chartType = context.chart.config.type;
          // Para gráficos lineales, usar borde semi-transparente como en los extendidos
          if (chartType === 'line') {
            return 'rgba(255, 255, 255, 0.8)';
          }
          // Para gráficos circulares, usar borde blanco sólido
          return 'white';
        },
        borderRadius: function(context: any) {
          const chartType = context.chart.config.type;
          // Para gráficos circulares, usar bordes más redondeados
          if (chartType === 'pie' || chartType === 'doughnut') {
            return 25;
          }
          // Para gráficos lineales, usar bordes menos redondeados
          return 8;
        },
        borderWidth: function(context: any) {
          const chartType = context.chart.config.type;
          // Para gráficos circulares, usar borde más grueso
          if (chartType === 'pie' || chartType === 'doughnut') {
            return 2;
          }
          // Para gráficos lineales, usar borde más delgado
          return 1;
        },
        color: '#ffffff',
        font: {
          size: 11,
          weight: 'bold' as const,
          family: 'Arial, sans-serif'
        },
        padding: function(context: any) {
          const chartType = context.chart.config.type;
          // Para gráficos circulares, usar padding más grande
          if (chartType === 'pie' || chartType === 'doughnut') {
            return 6;
          }
          // Para gráficos lineales, usar padding más pequeño
          return 4;
        },
        formatter: function(value: any, context: any) {
          try {
            const chartType = context.chart.config.type;

            // Asegurar que tenemos un valor numérico válido
            const numValue = typeof value === 'number' ? value : parseFloat(value);
            if (isNaN(numValue)) {
              return value; // Retornar valor original si no es numérico
            }

            if (chartType === 'pie' || chartType === 'doughnut') {
              const total = context.dataset.data.reduce((sum: number, val: number) => sum + (val || 0), 0);
              if (total === 0) return '0%';
              const percentage = ((numValue / total) * 100).toFixed(2);
              return `${percentage}%`;
            } else if (chartType === 'line') {
              if (numValue >= 1000000) {
                return `${(numValue / 1000000).toFixed(1)}M`;
              } else if (numValue >= 1000) {
                return `${(numValue / 1000).toFixed(1)}K`;
              } else if (numValue % 1 !== 0) {
                return numValue.toFixed(2);
              }
              return numValue.toString();
            }

            // Para otros tipos de gráfico, formatear números grandes
            if (numValue >= 1000000) {
              return `${(numValue / 1000000).toFixed(1)}M`;
            } else if (numValue >= 1000) {
              return `${(numValue / 1000).toFixed(1)}K`;
            }

            return numValue.toString();
          } catch (error) {
            console.warn('Error en formatter de datalabels:', error);
            return value;
          }
        },
        display: function(context: any) {
          try {
            const chartType = context.chart.config.type;

            // Para gráficos circulares, usar la lógica de getDoughnutDataLabelsConfig
            if (chartType === 'pie' || chartType === 'doughnut') {
              const dataset = context.dataset;
              const count = dataset.data.length;
              const dataValue = dataset.data[context.dataIndex];
              return dataValue > count * 1.5;
            }

            // Para gráficos lineales, mostrar siempre (como en getSmartDataLabelsConfig)
            if (chartType === 'line') {
              return true;
            }

            // Para otros tipos, validar que hay valor
            let value = context.parsed?.y ?? context.parsed ?? context.raw ?? context.dataset.data[context.dataIndex];

            return value !== undefined && value !== null;
          } catch (error) {
            console.warn('Error en display function de datalabels:', error);
            return false;
          }
        }
      }
    }
  }),

  // Función simplificada para esperar renderizado (evita problemas de recursión)
  waitForChartRender: (chartInstance: any): Promise<void> => {
    return new Promise((resolve) => {
      // Usar un delay fijo simple para evitar problemas de recursión
      const delay = chartInstance.options?.animation === false ? 100 : 500;
      setTimeout(resolve, delay);
    });
  },

  // Función para crear nombre de archivo seguro
  sanitizeFilename: (title: string): string => {
    return title
      .replace(/[^a-zA-Z0-9\s]/g, '_')
      .replace(/\s+/g, '_')
      .replace(/_+/g, '_')
      .trim();
  },

  // Función para descargar imagen usando blob (más moderno)
  downloadImageBlob: (canvas: HTMLCanvasElement, filename: string): void => {
    canvas.toBlob((blob) => {
      if (!blob) {
        console.error('No se pudo crear blob de la imagen');
        return;
      }

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Limpiar URL del blob
      URL.revokeObjectURL(url);
    }, 'image/png', 1.0);
  }
};

// Función helper para configuración inteligente de etiquetas
export const getSmartDataLabelsConfig = (formatter: (value: any) => string) => ({
  display: true,
  align: function(context: any) {
    // Validar que tenemos todos los datos necesarios
    if (!context || !context.chart || !context.parsed || typeof context.parsed.y !== 'number') {
      return 'start'; // Valor por defecto seguro
    }

    const chart = context.chart;
    const yScale = chart.scales?.y;
    const xScale = chart.scales?.x;

    // Validar que las escalas existen y tienen valores válidos
    if (!yScale || typeof yScale.max !== 'number' || typeof yScale.min !== 'number') {
      return 'start'; // Valor por defecto seguro
    }

    const value = context.parsed.y;
    const max = yScale.max;
    const min = yScale.min;
    const range = max - min;

    // Evitar división por cero
    if (range === 0) {
      return 'start';
    }

    // Verificar posición horizontal si tenemos acceso a la escala X
    let isNearRightEdge = false;
    if (xScale && typeof context.dataIndex === 'number' && context.dataset?.data) {
      const dataLength = context.dataset.data.length;
      const dataIndex = context.dataIndex;
      // Considerar que está cerca del borde derecho si está en el último 30% de los datos
      isNearRightEdge = dataIndex >= (dataLength * 0.7);
    }

    // Lógica de posicionamiento según la configuración proporcionada
    if (isNearRightEdge) {
      return 'end'; // Si está cerca del borde derecho, mostrar al final
    } else {
      return 'start'; // Posición por defecto al inicio
    }
  },
  anchor: function(context: any) {
    // Usar anchor dinámico para mejor posicionamiento
    if (!context || !context.chart || !context.parsed || typeof context.parsed.y !== 'number') {
      return 'start';
    }

    const xScale = context.chart.scales?.x;
    if (xScale && typeof context.dataIndex === 'number' && context.dataset?.data) {
      const dataLength = context.dataset.data.length;
      const dataIndex = context.dataIndex;
      const isNearRightEdge = dataIndex >= (dataLength * 0.7);

      if (isNearRightEdge) {
        return 'end'; // Para etiquetas al final
      }
    }

    return 'start'; // Anchor por defecto al inicio
  } as any,
  backgroundColor: function(context: any) {
    // Usar el color del dataset pero con opacidad completa
    const datasetColor = context.dataset.borderColor || context.dataset.backgroundColor;
    return datasetColor;
  },
  borderRadius: 8,
  color: 'white',
  font: {
    weight: 'bold'
  },
  formatter: formatter || Math.round,
  padding: 4,
  // Agregar borde para mejor definición
  borderColor: 'rgba(255, 255, 255, 0.8)',
  borderWidth: 1
});

// Función helper para configuración de etiquetas en gráficas circulares (doughnut/pie)
export const getDoughnutDataLabelsConfig = () => ({
  backgroundColor: function(context: any) {
    return context.dataset.backgroundColor;
  },
  borderColor: 'white',
  borderRadius: 25,
  borderWidth: 2,
  color: 'white',
  display: function(context: any) {
    var dataset = context.dataset;
    var count = dataset.data.length;
    var value = dataset.data[context.dataIndex];
    return value > count * 1.5;
  },
  font: {
    weight: 'bold'
  },
  padding: 6,
  formatter: Math.round,
  anchor: function(context: any) {
    // Lógica para determinar el anchor basado en el índice del dataset
    const datasetIndex = context.datasetIndex || 0;
    if (datasetIndex === 0) return 'end';
    if (datasetIndex === 1) return 'center';
    return 'start';
  }
});

// Configuración de layout con padding para etiquetas
export const getChartLayout = () => ({
  padding: {
    top: 32,
    right: 16,
    bottom: 16,
    left: 8
  }
});

// Configuración estandarizada para escalas en gráficas expandidas (igual que "Costos en Pesos ($)")
export const getExpandedChartScalesConfig = (xTitle: string, yTitle: string, yTickCallback?: (value: any) => string) => ({
  x: {
    display: true,
    title: {
      display: true,
      text: xTitle
    }
  },
  y: {
    display: true,
    title: {
      display: true,
      text: yTitle
    },
    ...(yTickCallback && {
      ticks: {
        callback: yTickCallback
      }
    })
  }
});

// Configuración para escalas stacked en gráficas de barras
export const getStackedChartScalesConfig = (xTitle: string, yTitle: string, yTickCallback?: (value: any) => string) => ({
  x: {
    display: true,
    stacked: true,
    title: {
      display: true,
      text: xTitle
    }
  },
  y: {
    display: true,
    stacked: true,
    title: {
      display: true,
      text: yTitle
    },
    ...(yTickCallback && {
      ticks: {
        callback: yTickCallback
      }
    })
  }
});

// Configuración específica para gráficas circulares (doughnut/pie)
export const getDoughnutChartOptions = () => ({
  plugins: {
    datalabels: getDoughnutDataLabelsConfig()
  },
  // Core options
  aspectRatio: 4 / 3,
  cutout: '32%', // Equivalente a cutoutPercentage: 32
  layout: {
    padding: 32
  },
  elements: {
    line: {
      fill: false
    },
    point: {
      hoverRadius: 7,
      radius: 5
    }
  }
});
