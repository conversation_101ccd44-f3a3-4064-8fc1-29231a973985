"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { getCurrentUserArea, type AreaData } from "@/app/actions/areas";
import { getAreaDisplayName, getAreaColor } from "@/lib/areaConstants";

interface UserAreaBadgeProps {
  className?: string;
  showOnlyForWorkers?: boolean;
}

export default function UserAreaBadge({ className = "", showOnlyForWorkers = false }: UserAreaBadgeProps) {
  const { data: session } = useSession();
  const [userArea, setUserArea] = useState<AreaData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadUserArea() {
      if (!session?.user?.id) {
        setLoading(false);
        return;
      }

      // Si showOnlyForWorkers es true, solo mostrar para usuarios WORKER
      if (showOnlyForWorkers && session.user.role !== "WORKER") {
        setLoading(false);
        return;
      }

      try {
        const result = await getCurrentUserArea();
        if (result.success && result.data) {
          setUserArea(result.data);
        }
      } catch (error) {
        console.error("Error al cargar área del usuario:", error);
      } finally {
        setLoading(false);
      }
    }

    loadUserArea();
  }, [session?.user?.id, session?.user?.role, showOnlyForWorkers]);

  if (loading) {
    return (
      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-400 animate-pulse ${className}`}>
        Cargando...
      </div>
    );
  }

  if (!userArea) {
    // Si es un WORKER sin área asignada, mostrar un badge indicativo
    if (session?.user?.role === "WORKER") {
      return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ${className}`}>
          Sin área asignada
        </span>
      );
    }
    
    // Para otros roles sin área, no mostrar nada
    return null;
  }

  // Obtener el color del área
  const areaColor = userArea.color || getAreaColor(userArea.nombre as any);
  
  // Convertir el color hex a clases de Tailwind o usar estilos inline
  const getBadgeStyle = (color: string) => {
    // Mapeo de colores hex a clases de Tailwind
    const colorMap: Record<string, string> = {
      "#3b82f6": "bg-blue-100 text-blue-800",      // blue-500
      "#10b981": "bg-emerald-100 text-emerald-800", // emerald-500
      "#f59e0b": "bg-amber-100 text-amber-800",     // amber-500
      "#8b5cf6": "bg-violet-100 text-violet-800",   // violet-500
      "#6366f1": "bg-indigo-100 text-indigo-800",   // indigo-500
      "#ef4444": "bg-red-100 text-red-800",         // red-500
      "#06b6d4": "bg-cyan-100 text-cyan-800",       // cyan-500
      "#059669": "bg-emerald-100 text-emerald-800", // emerald-600
    };

    return colorMap[color] || "bg-gray-100 text-gray-800";
  };

  const badgeClasses = getBadgeStyle(areaColor);

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badgeClasses} ${className}`}>
      {userArea.displayName}
    </span>
  );
}
