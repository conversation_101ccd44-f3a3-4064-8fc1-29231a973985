"use client"
import { useEffect, useState } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getTerminales } from "@/app/actions/terminales"

interface Terminal {
  id: string
  nombre: string
  valor?: string
}

interface TerminalSelectorProps {
  value: string
  onChange: (value: string) => void
}

// Terminales predeterminadas en caso de error (nombres en minúsculas en la base de datos, pero mostrados con primera letra mayúscula)
const TERMINALES_PREDETERMINADAS: Terminal[] = [
  { id: '1', nombre: 'Axfaltec', valor: 'axfaltec' },
  { id: '2', nombre: 'Cadereyta', valor: 'cadereyta' },
  { id: '3', nombre: 'Escamela', valor: 'escamela' },
  { id: '4', nombre: 'Madero', valor: 'madero' },
  { id: '5', nombre: 'Pajaritos', valor: 'pajaritos' },
  { id: '6', nombre: 'Progreso', valor: 'progreso' },
  { id: '7', nombre: 'Valero', valor: 'valero' },
  { id: '8', nombre: 'Tizayuca', valor: 'tizayuca' },
  { id: '9', nombre: 'Tuxpan', valor: 'tuxpan' },
  { id: '10', nombre: 'Tula', valor: 'tula' },
  { id: '11', nombre: 'Vopak', valor: 'vopak' },
  { id: '12', nombre: 'Villahermosa', valor: 'villahermosa' }
];

export default function TerminalSelector({ value, onChange }: TerminalSelectorProps) {
  const [terminales, setTerminales] = useState<Terminal[]>(TERMINALES_PREDETERMINADAS)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [usandoPredeterminadas, setUsandoPredeterminadas] = useState(false)

  useEffect(() => {
    const cargarTerminales = async () => {
      try {
        setLoading(true)
        console.log('Intentando cargar terminales desde el servidor...')
        const data = await getTerminales()

        if (data && Array.isArray(data) && data.length > 0) {
          // Capitalizar la primera letra de cada nombre de terminal para mostrar
          // pero mantener el valor original en minúsculas para enviar al servidor
          const terminalesCapitalizadas = data.map(terminal => ({
            ...terminal,
            nombre: terminal.nombre.charAt(0).toUpperCase() + terminal.nombre.slice(1),
            valor: terminal.nombre.toLowerCase() // Asegurar que el valor siempre esté en minúsculas
          }))
          setTerminales(terminalesCapitalizadas)
          setUsandoPredeterminadas(false)
          console.log('Terminales cargadas desde el servidor:', terminalesCapitalizadas)
        } else {
          console.warn('No se recibieron terminales válidas del servidor, usando predeterminadas')
          setTerminales(TERMINALES_PREDETERMINADAS)
          setUsandoPredeterminadas(true)
        }
      } catch (err) {
        console.error('Error al cargar terminales:', err)
        setError('No se pudieron cargar las terminales del servidor, usando valores predeterminados')
        setTerminales(TERMINALES_PREDETERMINADAS)
        setUsandoPredeterminadas(true)
      } finally {
        setLoading(false)
      }
    }

    cargarTerminales()
  }, [])

  return (
    <div className="w-full md:w-3/4">
      <div className="mb-4">
        {loading ? (
          <div className="text-sm text-gray-500">Cargando terminales...</div>
        ) : error ? (
          <div>
            <div className="text-sm text-red-500 mb-2">{error}</div>
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger>
                <SelectValue placeholder="Elija Terminales de Almacenamiento y Reparto" className="font-medium" />
              </SelectTrigger>
              <SelectContent>
                {terminales.map((terminal) => (
                  <SelectItem key={terminal.id} value={terminal.valor || terminal.nombre}>
                    {terminal.nombre}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {usandoPredeterminadas && (
              <div className="text-xs text-amber-600 mt-1">Usando terminales predeterminadas</div>
            )}
          </div>
        ) : (
          <div>
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger>
                <SelectValue placeholder="Elija Terminales de Almacenamiento y Reparto" className="font-medium" />
              </SelectTrigger>
              <SelectContent>
                {terminales.map((terminal) => (
                  <SelectItem key={terminal.id} value={terminal.valor || terminal.nombre}>
                    {terminal.nombre}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {usandoPredeterminadas && (
              <div className="text-xs text-amber-600 mt-1">Usando terminales predeterminadas</div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
