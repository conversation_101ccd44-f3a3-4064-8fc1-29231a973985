"use client";

import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";
import SessionPersistence from "./SessionPersistence";

interface AuthProviderProps {
  children: ReactNode;
}

export default function AuthProvider({ children }: AuthProviderProps) {
  return (
    <SessionProvider
      // Refetch session every 5 minutes
      refetchInterval={5 * 60}
      // Refetch session when window is focused
      refetchOnWindowFocus={true}
      // Keep session alive when browser is idle
      refetchWhenOffline={false}
    >
      <SessionPersistence />
      {children}
    </SessionProvider>
  );
}
