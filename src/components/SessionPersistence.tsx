"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";

/**
 * Componente para mejorar la persistencia de sesiones
 * Maneja la renovación automática y la sincronización entre pestañas
 */
export default function SessionPersistence() {
  const { data: session, status } = useSession();

  useEffect(() => {
    // Solo ejecutar en el cliente
    if (typeof window === "undefined") return;

    // Función para manejar cambios de visibilidad de la página
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && session) {
        // Cuando la página se vuelve visible, verificar la sesión
        window.dispatchEvent(new Event("focus"));
      }
    };

    // Función para manejar el almacenamiento local
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "nextauth.message") {
        // Sincronizar sesión entre pestañas
        window.location.reload();
      }
    };

    // Función para mantener la sesión activa
    const keepSessionAlive = () => {
      if (session && status === "authenticated") {
        // Hacer una petición silenciosa para mantener la sesión activa
        fetch("/api/auth/session", {
          method: "GET",
          credentials: "include",
        }).catch(() => {
          // Ignorar errores silenciosamente
        });
      }
    };

    // Configurar event listeners
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("storage", handleStorageChange);

    // Mantener sesión activa cada 10 minutos
    const keepAliveInterval = setInterval(keepSessionAlive, 10 * 60 * 1000);

    // Cleanup
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("storage", handleStorageChange);
      clearInterval(keepAliveInterval);
    };
  }, [session, status]);

  // Este componente no renderiza nada
  return null;
}
