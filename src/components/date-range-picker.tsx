"use client"
import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

interface DateRange {
  startDate: Date | null
  endDate: Date | null
}

interface DateRangePickerProps {
  onChange?: (range: DateRange) => void
  initialStartDate?: Date | null
  initialEndDate?: Date | null
  className?: string
}

export function DateRangePicker({
  onChange,
  initialStartDate = null,
  initialEndDate = null,
  className,
}: DateRangePickerProps) {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date())
  const [startDate, setStartDate] = useState<Date | null>(initialStartDate)
  const [endDate, setEndDate] = useState<Date | null>(initialEndDate)
  const [hoverDate, setHoverDate] = useState<Date | null>(null)

  // Obtener la fecha actual para la validación
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // Actualizar las fechas cuando cambien las props
  useEffect(() => {
    if (initialStartDate !== startDate && initialStartDate !== null) {
      setStartDate(initialStartDate);
      // Si hay una fecha de inicio, también actualizamos el mes actual para mostrar esa fecha
      if (initialStartDate) {
        setCurrentMonth(new Date(initialStartDate.getFullYear(), initialStartDate.getMonth(), 1));
      }
    }
    if (initialEndDate !== endDate && initialEndDate !== null) {
      setEndDate(initialEndDate);
    }
  }, [initialStartDate, initialEndDate, startDate, endDate])

  // Utilizamos notificación directa en los manejadores de eventos

  const handleDateClick = (date: Date): void => {
    // Solo validamos que no sea fecha futura
    if (date > today) return

    if (!startDate || (startDate && endDate)) {
      // Inicio de un nuevo rango
      setStartDate(date)
      setEndDate(null)
      if (onChange) {
        onChange({ startDate: date, endDate: null })
      }
    } else {
      // Completando un rango
      if (date < startDate) {
        // Si la fecha seleccionada es anterior a la fecha de inicio,
        // intercambiamos el orden
        setEndDate(startDate)
        setStartDate(date)
        if (onChange) {
          onChange({ startDate: date, endDate: startDate })
        }
      } else {
        setEndDate(date)
        if (onChange) {
          onChange({ startDate, endDate: date })
        }
      }
    }
  }

  const handleMouseEnter = (date: Date): void => {
    if (startDate && !endDate) {
      setHoverDate(date)
    }
  }

  const handleMouseLeave = (): void => {
    setHoverDate(null)
  }

  const isDateInRange = (date: Date): boolean => {
    if (startDate && endDate) {
      return date > startDate && date < endDate
    }
    if (startDate && hoverDate) {
      return (date > startDate && date <= hoverDate) || (date < startDate && date >= hoverDate)
    }
    return false
  }

  const isDateSelected = (date: Date): boolean => {
    if (!startDate && !endDate) return false;
    return (
      (startDate?.toDateString() === date.toDateString()) ||
      (endDate?.toDateString() === date.toDateString()) ||
      false
    );
  }

  const prevMonth = (): void => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1))
  }

  const nextMonth = (): void => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1))
  }

  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (year: number, month: number): number => {
    return new Date(year, month, 1).getDay()
  }

  const renderCalendar = () => {
    const year = currentMonth.getFullYear()
    const month = currentMonth.getMonth()
    const daysInMonth = getDaysInMonth(year, month)
    const firstDayOfMonth = getFirstDayOfMonth(year, month)

    const monthNames: string[] = [
      "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
      "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
    ]

    const weekdays: string[] = ["Do", "Lu", "Ma", "Mi", "Ju", "Vi", "Sa"]

    const days = []
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="w-8 h-8"></div>)
    }

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day)
      const isSelected = isDateSelected(date)
      const inRange = isDateInRange(date)
      const isFutureDate = date > today
      const isToday = date.toDateString() === today.toDateString()

      days.push(
        <button
          key={day}
          className={cn(
            "w-8 h-8 flex items-center justify-center text-sm rounded-full",
            "transition-all duration-200",
            "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50",
            "relative",
            isSelected && "bg-primary text-white",
            !isSelected && "bg-transparent",
            inRange && !isSelected && "bg-primary/10",
            inRange && hoverDate && !endDate && !isSelected && "bg-primary/5",
            !isSelected && !inRange && !isFutureDate && "hover:bg-muted",
            isFutureDate && "opacity-50 cursor-not-allowed text-gray-400",
            isToday && !isSelected && "border border-primary",
          )}
          onClick={() => handleDateClick(date)}
          onMouseEnter={() => handleMouseEnter(date)}
          onMouseLeave={handleMouseLeave}
          disabled={isFutureDate}
          aria-label={`Día ${day}`}
          aria-selected={isSelected}
        >
          {day}
        </button>
      )
    }

    return (
      <div className="bg-card border rounded-lg shadow-md p-4 w-[320px] h-auto shrink-0">
        <div className="flex justify-between items-center mb-4">
          <button
            className="bg-transparent border-none text-muted-foreground p-0 flex items-center justify-center w-8 h-8 hover:bg-muted rounded-full"
            onClick={prevMonth}
            aria-label="Mes anterior"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
          <span className="text-base font-medium text-foreground">
            {monthNames[month]} {year}
          </span>
          <button
            className="bg-transparent border-none text-muted-foreground p-0 flex items-center justify-center w-8 h-8 hover:bg-muted rounded-full"
            onClick={nextMonth}
            disabled={new Date(year, month + 1, 1) > today}
            aria-label="Mes siguiente"
          >
            <ChevronRight className="h-4 w-4" />
          </button>
        </div>
        <div className="grid grid-cols-7 gap-1 mb-2">
          {weekdays.map((day) => (
            <div key={day} className="text-center text-xs text-muted-foreground">
              {day}
            </div>
          ))}
        </div>
        <div className="grid grid-cols-7 gap-1">{days}</div>
      </div>
    )
  }

  const formatDate = (date: Date | null): string => {
    if (!date) return ""
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`
  }

  return (
    <div className={cn("flex flex-col", className)}>
      {renderCalendar()}
      {(startDate || endDate) && (
        <div className="mt-2 p-2 bg-primary/5 border border-primary/20 rounded-md">
          <div className="text-sm font-medium text-gray-700">Período seleccionado:</div>
          <div className="text-base font-semibold text-primary mt-1">
            {formatDate(startDate)} - {formatDate(endDate)}
          </div>
        </div>
      )}
    </div>
  )
}
