import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { KpiLogisticaData } from '@/app/actions/kpis-logistica';
import { getLastCompletedWeekInfo, getWeekInfo, canAddDataForWeek, getWeekDataRestrictionMessage, type WeekInfo } from '@/lib/utils/weekUtils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AlertDialog from './ui/AlertDialog';

// Interfaz para el formulario que permite valores string o number
interface FormKpiLogisticaData extends Omit<KpiLogisticaData, 'unidadesConfirmadas' | 'unidadesSolicitadas' | 'porcentajeEntregasTiempo' | 'porcentajeRetardos' | 'porcentajeReprogramaciones' | 'promedioKmOperacion' | 'promedioCostoFleteLitro' | 'promedioCostoFleteOperacion' | 'pagoSemanalFlete' | 'pagoSemanalPenalizaciones' | 'porcentajeRutasCotizadas' | 'porcentajeTransportistas'> {
  unidadesConfirmadas: number | string;
  unidadesSolicitadas: number | string;
  porcentajeEntregasTiempo: number | string;
  porcentajeRetardos: number | string;
  porcentajeReprogramaciones: number | string;
  promedioKmOperacion: number | string;
  promedioCostoFleteLitro: number | string;
  promedioCostoFleteOperacion: number | string;
  pagoSemanalFlete: number | string;
  pagoSemanalPenalizaciones: number | string;
  porcentajeRutasCotizadas: number | string;
  porcentajeTransportistas: number | string;
}

interface KpiLogisticaInlineFormProps {
  onClose: () => void;
  onSave: (data: KpiLogisticaData) => Promise<void>;
  editingKpi?: KpiLogisticaData | null;
  isAddingOldWeek?: boolean;
  existingKpis?: any[];
}

// Componente Tooltip personalizado
interface TooltipProps {
  content: string;
  children: React.ReactNode;
  className?: string;
}

const Tooltip: React.FC<TooltipProps> = ({ content, children, className = "" }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: false, left: false, right: false });
  const containerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isVisible && containerRef.current && tooltipRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      const newPosition = {
        top: containerRect.top < tooltipRect.height + 10,
        left: containerRect.left + tooltipRect.width > viewportWidth - 10,
        right: containerRect.right - tooltipRect.width < 10
      };

      setPosition(newPosition);
    }
  }, [isVisible]);

  const getTooltipClasses = () => {
    let classes = "absolute z-[9999] px-3 py-2 text-sm text-white rounded-lg shadow-lg pointer-events-none";
    
    if (position.top) {
      classes += " top-full mt-2";
    } else {
      classes += " bottom-full mb-2";
    }
    
    if (position.left) {
      classes += " right-0";
    } else if (position.right) {
      classes += " left-0";
    } else {
      classes += " left-1/2 transform -translate-x-1/2";
    }
    
    return classes;
  };

  const getArrowClasses = () => {
    let classes = "absolute w-2 h-2 transform rotate-45";
    
    if (position.top) {
      classes += " top-0 -mt-1 bg-gray-900";
    } else {
      classes += " bottom-0 -mb-1 bg-gray-900";
    }
    
    if (position.left) {
      classes += " right-3";
    } else if (position.right) {
      classes += " left-3";
    } else {
      classes += " left-1/2 -translate-x-1/2";
    }
    
    return classes;
  };

  const getArrowStyle = () => {
    const style: React.CSSProperties = {};
    
    if (position.top) {
      style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
      style.borderTop = '1px solid rgba(255, 255, 255, 0.1)';
      style.borderLeft = '1px solid rgba(255, 255, 255, 0.1)';
    } else {
      style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
      style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
      style.borderRight = '1px solid rgba(255, 255, 255, 0.1)';
    }
    
    return style;
  };

  return (
    <div
      ref={containerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(4px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            maxWidth: '280px',
            width: 'max-content',
            whiteSpace: 'normal',
            wordWrap: 'break-word',
            lineHeight: '1.4'
          }}
        >
          {content}
          <div
            className={getArrowClasses()}
            style={getArrowStyle()}
          ></div>
        </div>
      )}
    </div>
  );
};

const KpiLogisticaInlineForm: React.FC<KpiLogisticaInlineFormProps> = ({
  onClose,
  onSave,
  editingKpi,
  isAddingOldWeek = false,
  existingKpis = []
}) => {
  const [loading, setLoading] = useState(false);
  const [currentWeek, setCurrentWeek] = useState<WeekInfo | null>(null);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [showDuplicateAlert, setShowDuplicateAlert] = useState(false);
  const [showWeekRestrictionAlert, setShowWeekRestrictionAlert] = useState(false);
  const [weekRestrictionMessage, setWeekRestrictionMessage] = useState("");
  const [showValidationErrorAlert, setShowValidationErrorAlert] = useState(false);
  const [validationErrorMessage, setValidationErrorMessage] = useState("");

  // Estados para manejo de duplicados y modo read-only
  const [isReadOnlyMode, setIsReadOnlyMode] = useState(false);
  const [existingData, setExistingData] = useState<KpiLogisticaData | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [showSuccessBanner, setShowSuccessBanner] = useState(false);
  const [formData, setFormData] = useState<FormKpiLogisticaData>({
    year: new Date().getFullYear(),
    weekNumber: 1,
    weekStartDate: "",
    weekEndDate: "",
    unidadesConfirmadas: "",
    unidadesSolicitadas: "",
    porcentajeEntregasTiempo: "",
    porcentajeRetardos: "",
    porcentajeReprogramaciones: "",
    promedioKmOperacion: "",
    promedioCostoFleteLitro: "",
    promedioCostoFleteOperacion: "",
    pagoSemanalFlete: "",
    pagoSemanalPenalizaciones: "",
    porcentajeRutasCotizadas: "",
    porcentajeTransportistas: ""
  });

  // Función para verificar si ya existe un KPI para la semana actual
  const checkExistingKpi = (year: number, weekNumber: number) => {
    const existing = existingKpis.find(kpi => kpi.year === year && kpi.weekNumber === weekNumber);
    
    if (existing && !editingKpi && !isAddingOldWeek) {
      setExistingData(existing);
      setIsReadOnlyMode(true);
      setShowSuccessBanner(true);
      // Pre-llenar el formulario con datos existentes
      setFormData({
        year: existing.year,
        weekNumber: existing.weekNumber,
        weekStartDate: existing.weekStartDate,
        weekEndDate: existing.weekEndDate,
        unidadesConfirmadas: existing.unidadesConfirmadas,
        unidadesSolicitadas: existing.unidadesSolicitadas,
        porcentajeEntregasTiempo: existing.porcentajeEntregasTiempo,
        porcentajeRetardos: existing.porcentajeRetardos,
        porcentajeReprogramaciones: existing.porcentajeReprogramaciones,
        promedioKmOperacion: existing.promedioKmOperacion,
        promedioCostoFleteLitro: existing.promedioCostoFleteLitro,
        promedioCostoFleteOperacion: existing.promedioCostoFleteOperacion,
        pagoSemanalFlete: existing.pagoSemanalFlete,
        pagoSemanalPenalizaciones: existing.pagoSemanalPenalizaciones,
        porcentajeRutasCotizadas: existing.porcentajeRutasCotizadas,
        porcentajeTransportistas: existing.porcentajeTransportistas
      });
    } else if (existing && isAddingOldWeek) {
      // En modo "Agregar Semana Antigua", permitir edición de datos existentes
      setExistingData(existing);
      setIsReadOnlyMode(false);
      setShowSuccessBanner(true);
      // Pre-llenar el formulario con datos existentes para editar
      setFormData({
        year: existing.year,
        weekNumber: existing.weekNumber,
        weekStartDate: existing.weekStartDate,
        weekEndDate: existing.weekEndDate,
        unidadesConfirmadas: existing.unidadesConfirmadas,
        unidadesSolicitadas: existing.unidadesSolicitadas,
        porcentajeEntregasTiempo: existing.porcentajeEntregasTiempo,
        porcentajeRetardos: existing.porcentajeRetardos,
        porcentajeReprogramaciones: existing.porcentajeReprogramaciones,
        promedioKmOperacion: existing.promedioKmOperacion,
        promedioCostoFleteLitro: existing.promedioCostoFleteLitro,
        promedioCostoFleteOperacion: existing.promedioCostoFleteOperacion,
        pagoSemanalFlete: existing.pagoSemanalFlete,
        pagoSemanalPenalizaciones: existing.pagoSemanalPenalizaciones,
        porcentajeRutasCotizadas: existing.porcentajeRutasCotizadas,
        porcentajeTransportistas: existing.porcentajeTransportistas
      });
    } else {
      setIsReadOnlyMode(false);
      setExistingData(null);
      setShowSuccessBanner(false);
    }
  };

  // Inicializar datos del formulario
  useEffect(() => {
    if (editingKpi) {
      // Modo edición
      setFormData({
        year: editingKpi.year,
        weekNumber: editingKpi.weekNumber,
        weekStartDate: editingKpi.weekStartDate,
        weekEndDate: editingKpi.weekEndDate,
        unidadesConfirmadas: editingKpi.unidadesConfirmadas,
        unidadesSolicitadas: editingKpi.unidadesSolicitadas,
        porcentajeEntregasTiempo: editingKpi.porcentajeEntregasTiempo,
        porcentajeRetardos: editingKpi.porcentajeRetardos,
        porcentajeReprogramaciones: editingKpi.porcentajeReprogramaciones,
        promedioKmOperacion: editingKpi.promedioKmOperacion,
        promedioCostoFleteLitro: editingKpi.promedioCostoFleteLitro,
        promedioCostoFleteOperacion: editingKpi.promedioCostoFleteOperacion,
        pagoSemanalFlete: editingKpi.pagoSemanalFlete,
        pagoSemanalPenalizaciones: editingKpi.pagoSemanalPenalizaciones,
        porcentajeRutasCotizadas: editingKpi.porcentajeRutasCotizadas,
        porcentajeTransportistas: editingKpi.porcentajeTransportistas
      });
      
      const weekInfo = getWeekInfo(editingKpi.year, editingKpi.weekNumber);
      setCurrentWeek(weekInfo);
      setSelectedYear(editingKpi.year);
      setSelectedWeek(editingKpi.weekNumber);
    } else {
      // Para nueva entrada de datos, usar la última semana completada
      try {
        const weekInfo = getLastCompletedWeekInfo();
        setCurrentWeek(weekInfo);
        setSelectedYear(weekInfo.year);
        setSelectedWeek(weekInfo.weekNumber);
        setFormData({
          year: weekInfo.year,
          weekNumber: weekInfo.weekNumber,
          weekStartDate: weekInfo.startDate.toISOString(),
          weekEndDate: weekInfo.endDate.toISOString(),
          unidadesConfirmadas: "" as any,
          unidadesSolicitadas: "" as any,
          porcentajeEntregasTiempo: "" as any,
          porcentajeRetardos: "" as any,
          porcentajeReprogramaciones: "" as any,
          promedioKmOperacion: "" as any,
          promedioCostoFleteLitro: "" as any,
          promedioCostoFleteOperacion: "" as any,
          pagoSemanalFlete: "" as any,
          pagoSemanalPenalizaciones: "" as any,
          porcentajeRutasCotizadas: "" as any,
          porcentajeTransportistas: "" as any
        });
        
        // Verificar si ya existe un KPI para esta semana
        checkExistingKpi(weekInfo.year, weekInfo.weekNumber);
      } catch (error) {
        console.error("Error al inicializar última semana completada:", error);
      }
    }
  }, [editingKpi, existingKpis]);

  // useEffect separado para manejar cambios de año/semana en modo agregar semana antigua
  useEffect(() => {
    if (isAddingOldWeek && !editingKpi) {
      try {
        const weekInfo = getWeekInfo(selectedYear, selectedWeek);
        setCurrentWeek(weekInfo);

        // Asegurar que las fechas estén en formato ISO correcto
        const startDateISO = new Date(weekInfo.startDate).toISOString();
        const endDateISO = new Date(weekInfo.endDate).toISOString();

        // Verificar si ya existen datos para esta semana
        const existing = existingKpis.find(kpi => kpi.year === selectedYear && kpi.weekNumber === selectedWeek);

        if (existing) {
          // Si existen datos, cargarlos en el formulario
          setExistingData(existing);
          setIsReadOnlyMode(false);
          setShowSuccessBanner(true);
          setFormData({
            year: selectedYear,
            weekNumber: selectedWeek,
            weekStartDate: startDateISO,
            weekEndDate: endDateISO,
            unidadesConfirmadas: existing.unidadesConfirmadas,
            unidadesSolicitadas: existing.unidadesSolicitadas,
            porcentajeEntregasTiempo: existing.porcentajeEntregasTiempo,
            porcentajeRetardos: existing.porcentajeRetardos,
            porcentajeReprogramaciones: existing.porcentajeReprogramaciones,
            promedioKmOperacion: existing.promedioKmOperacion,
            promedioCostoFleteLitro: existing.promedioCostoFleteLitro,
            promedioCostoFleteOperacion: existing.promedioCostoFleteOperacion,
            pagoSemanalFlete: existing.pagoSemanalFlete,
            pagoSemanalPenalizaciones: existing.pagoSemanalPenalizaciones,
            porcentajeRutasCotizadas: existing.porcentajeRutasCotizadas,
            porcentajeTransportistas: existing.porcentajeTransportistas
          });
        } else {
          // Si no existen datos, resetear el formulario con campos vacíos
          setExistingData(null);
          setIsReadOnlyMode(false);
          setShowSuccessBanner(false);
          setFormData({
            year: selectedYear,
            weekNumber: selectedWeek,
            weekStartDate: startDateISO,
            weekEndDate: endDateISO,
            unidadesConfirmadas: "" as any,
            unidadesSolicitadas: "" as any,
            porcentajeEntregasTiempo: "" as any,
            porcentajeRetardos: "" as any,
            porcentajeReprogramaciones: "" as any,
            promedioKmOperacion: "" as any,
            promedioCostoFleteLitro: "" as any,
            promedioCostoFleteOperacion: "" as any,
            pagoSemanalFlete: "" as any,
            pagoSemanalPenalizaciones: "" as any,
            porcentajeRutasCotizadas: "" as any,
            porcentajeTransportistas: "" as any
          });
        }
      } catch (error) {
        console.error("Error al actualizar semana:", error);
      }
    }
  }, [selectedYear, selectedWeek, isAddingOldWeek, editingKpi, existingKpis]);

  // Manejar cambios en el año
  const handleYearChange = (year: number) => {
    setSelectedYear(year);
    setSelectedWeek(1);

    const weekInfo = getWeekInfo(year, 1);
    setCurrentWeek(weekInfo);
    setFormData(prev => ({
      ...prev,
      year: year,
      weekNumber: 1,
      weekStartDate: weekInfo.startDate.toISOString(),
      weekEndDate: weekInfo.endDate.toISOString()
    }));

    checkExistingKpi(year, 1);
  };

  // Manejar cambios en la semana
  const handleWeekChange = (week: number) => {
    setSelectedWeek(week);

    const weekInfo = getWeekInfo(selectedYear, week);
    setCurrentWeek(weekInfo);
    setFormData(prev => ({
      ...prev,
      weekNumber: week,
      weekStartDate: weekInfo.startDate.toISOString(),
      weekEndDate: weekInfo.endDate.toISOString()
    }));

    checkExistingKpi(selectedYear, week);
  };

  // Manejar cambios en los inputs
  const handleInputChange = (field: keyof FormKpiLogisticaData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Marcar que hay cambios si estamos en modo read-only
    if (isReadOnlyMode && existingData) {
      const currentValue = existingData[field as keyof KpiLogisticaData];
      const newValue = value === "" ? "" : parseFloat(value);
      setHasChanges(currentValue !== newValue);
    }
  };

  // Validar formulario
  const validateForm = (): boolean => {
    const requiredFields: (keyof FormKpiLogisticaData)[] = [
      'unidadesConfirmadas', 'unidadesSolicitadas', 'porcentajeEntregasTiempo',
      'porcentajeRetardos', 'porcentajeReprogramaciones', 'promedioKmOperacion',
      'promedioCostoFleteLitro', 'promedioCostoFleteOperacion', 'pagoSemanalFlete',
      'pagoSemanalPenalizaciones', 'porcentajeRutasCotizadas', 'porcentajeTransportistas'
    ];

    for (const field of requiredFields) {
      const value = formData[field];
      if (value === "" || value === null || value === undefined) {
        return false;
      }
    }

    return true;
  };

  // Manejar envío del formulario
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Verificar duplicados para nuevos KPIs
    if (!editingKpi && !isReadOnlyMode) {
      const existingKpi = existingKpis.find(
        kpi => kpi.year === formData.year && kpi.weekNumber === formData.weekNumber
      );

      if (existingKpi) {
        setShowDuplicateAlert(true);
        return;
      }
    }

    // Validar restricciones de semana (solo para nuevos KPIs, no para edición)
    if (!editingKpi && !isReadOnlyMode && !canAddDataForWeek(formData.year, formData.weekNumber)) {
      setWeekRestrictionMessage(getWeekDataRestrictionMessage(formData.year, formData.weekNumber));
      setShowWeekRestrictionAlert(true);
      return;
    }

    setLoading(true);

    try {
      const kpiData: KpiLogisticaData = {
        ...formData,
        unidadesConfirmadas: Number(formData.unidadesConfirmadas),
        unidadesSolicitadas: Number(formData.unidadesSolicitadas),
        porcentajeEntregasTiempo: Number(formData.porcentajeEntregasTiempo),
        porcentajeRetardos: Number(formData.porcentajeRetardos),
        porcentajeReprogramaciones: Number(formData.porcentajeReprogramaciones),
        promedioKmOperacion: Number(formData.promedioKmOperacion),
        promedioCostoFleteLitro: Number(formData.promedioCostoFleteLitro),
        promedioCostoFleteOperacion: Number(formData.promedioCostoFleteOperacion),
        pagoSemanalFlete: Number(formData.pagoSemanalFlete),
        pagoSemanalPenalizaciones: Number(formData.pagoSemanalPenalizaciones),
        porcentajeRutasCotizadas: Number(formData.porcentajeRutasCotizadas),
        porcentajeTransportistas: Number(formData.porcentajeTransportistas)
      };

      if (editingKpi) {
        kpiData.id = editingKpi.id;
      }

      await onSave(kpiData);
      onClose();
    } catch (error) {
      console.error("Error al guardar KPI:", error);
      if (error instanceof Error && (error.message.includes("ZodError") || error.message.includes("must be less than or equal to"))) {
        setValidationErrorMessage("Los datos ingresados no son válidos. Por favor verifica que todos los valores estén dentro de los rangos permitidos.");
        setShowValidationErrorAlert(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const kpiFields = [
    {
      key: "unidadesConfirmadas" as keyof FormKpiLogisticaData,
      title: "UNIDADES CONFIRMADAS",
      label: "Unidades Confirmadas",
      description: "Número total de unidades confirmadas para entrega",
      type: "number",
      min: 0,
      max: 100000,
      step: 1
    },
    {
      key: "unidadesSolicitadas" as keyof FormKpiLogisticaData,
      title: "UNIDADES SOLICITADAS",
      label: "Unidades Solicitadas",
      description: "Número total de unidades solicitadas por los clientes",
      type: "number",
      min: 0,
      max: 100000,
      step: 1
    },
    {
      key: "porcentajeEntregasTiempo" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE ENTREGAS A TIEMPO (%)",
      label: "Porcentaje de Entregas a Tiempo (%)",
      description: "Porcentaje de entregas realizadas dentro del tiempo acordado",
      type: "number",
      min: 0,
      max: 100,
      step: 0.1
    },
    {
      key: "porcentajeRetardos" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE RETARDOS (%)",
      label: "Porcentaje de Retardos (%)",
      description: "Porcentaje de entregas con retraso respecto al tiempo acordado",
      type: "number",
      min: 0,
      max: 500,
      step: 0.1
    },
    {
      key: "porcentajeReprogramaciones" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE REPROGRAMACIONES (%)",
      label: "Porcentaje de Reprogramaciones (%)",
      description: "Porcentaje de entregas que requirieron reprogramación",
      type: "number",
      min: 0,
      max: 500,
      step: 0.1
    },
    {
      key: "promedioKmOperacion" as keyof FormKpiLogisticaData,
      title: "PROMEDIO DE KM POR OPERACIÓN",
      label: "Promedio de KM por Operación",
      description: "Distancia promedio recorrida por operación de entrega",
      type: "number",
      min: 0,
      max: 10000,
      step: 0.1
    },
    {
      key: "promedioCostoFleteLitro" as keyof FormKpiLogisticaData,
      title: "PROMEDIO DE COSTO DE FLETE POR LITRO ($)",
      label: "Promedio de Costo de Flete por Litro ($)",
      description: "Costo promedio de flete por litro transportado",
      type: "number",
      min: 0,
      max: 100,
      step: 0.01
    },
    {
      key: "promedioCostoFleteOperacion" as keyof FormKpiLogisticaData,
      title: "PROMEDIO DE COSTO DE FLETE POR OPERACIÓN ($)",
      label: "Promedio de Costo de Flete por Operación ($)",
      description: "Costo promedio de flete por operación completa",
      type: "number",
      min: 0,
      max: 1000000,
      step: 0.01
    },
    {
      key: "pagoSemanalFlete" as keyof FormKpiLogisticaData,
      title: "PAGO SEMANAL DE FLETE ($)",
      label: "Pago Semanal de Flete ($)",
      description: "Monto total pagado por concepto de flete en la semana",
      type: "number",
      min: 0,
      max: 10000000,
      step: 0.01
    },
    {
      key: "pagoSemanalPenalizaciones" as keyof FormKpiLogisticaData,
      title: "PAGO SEMANAL POR PENALIZACIONES ($)",
      label: "Pago Semanal por Penalizaciones ($)",
      description: "Monto total pagado por penalizaciones en la semana",
      type: "number",
      min: 0,
      max: 1000000,
      step: 0.01
    },
    {
      key: "porcentajeRutasCotizadas" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE RUTAS COTIZADAS (%)",
      label: "Porcentaje de Rutas Cotizadas (%)",
      description: "Porcentaje de rutas que fueron cotizadas antes de la operación",
      type: "number",
      min: 0,
      max: 100,
      step: 0.1
    },
    {
      key: "porcentajeTransportistas" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE TRANSPORTISTAS (%)",
      label: "Porcentaje de Transportistas (%)",
      description: "Porcentaje de transportistas activos en el período",
      type: "number",
      min: 0,
      max: 100,
      step: 0.1
    }
  ];

  return (
    <>
      <motion.div
        className="space-y-8 p-6"
        initial={{ opacity: 0, y: 8 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -8 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
      >
        {/* Banner de éxito para semana ya registrada - no mostrar en Agregar Semana Antigua */}
        {showSuccessBanner && existingData && !isAddingOldWeek && (
          <motion.div
            className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 mb-2"
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.15, delay: 0.05 }}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-600">
                  Semana {existingData.weekNumber} registrada ({currentWeek?.startDate.toLocaleDateString('es-ES', { day: '2-digit', month: 'short' })} – {currentWeek?.endDate.toLocaleDateString('es-ES', { day: '2-digit', month: 'short' })})
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Header */}
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, delay: 0.05 }}
        >
          <div className="flex items-center gap-3 mb-2">
            <h2 className="text-xl md:text-xl lg:text-2xl text-black font-medium">
              {editingKpi ? "Editar" : isAddingOldWeek ? "Agregar Semana Antigua" : isReadOnlyMode ? "Datos registrados" : "Agregar nueva semana"}
            </h2>
            {isReadOnlyMode && !isAddingOldWeek && (
              <button
                onClick={() => setIsReadOnlyMode(false)}
                className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-primary/10 transition-colors"
                title="Modificar los datos registrados"
              >
                Editar
              </button>
            )}
          </div>

          {/* Badge de semana - mostrar en todos los modos excepto Agregar Semana Antigua */}
          {!isAddingOldWeek && currentWeek && (
            <div className="mb-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs lg:text-sm font-medium bg-primary/10 text-primary">
                Semana {currentWeek.weekNumber}/{currentWeek.year} ({currentWeek.startDate.toLocaleDateString()} - {currentWeek.endDate.toLocaleDateString()})
              </span>
            </div>
          )}
        </motion.div>

        {/* Form */}
        <motion.form
          onSubmit={handleSubmit}
          className="space-y-6"
          initial={{ opacity: 0, y: 6 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.25, delay: 0.1 }}
        >
          {/* Selectores de Año y Semana para semanas antiguas */}
          {isAddingOldWeek && !editingKpi && (
            <div className="bg-primary/10 rounded-lg p-3">
              <h3 className="text-sm font-medium text-blue-800 mb-2">Seleccionar Semana</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="flex flex-col h-full">
                  <label className="block text-sm font-medium text-blue-800 mb-1">
                    Año
                  </label>
                  <Select
                    value={selectedYear.toString()}
                    onValueChange={(value) => {
                      const year = parseInt(value);
                      if (!isNaN(year)) {
                        handleYearChange(year);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full bg-white border-blue-300 focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue placeholder="Seleccionar año" />
                    </SelectTrigger>
                    <SelectContent
                      position="popper"
                      sideOffset={4}
                      className="z-[9999] max-h-[200px] overflow-y-auto"
                    >
                      {Array.from({ length: new Date().getFullYear() - 2021 + 1 }, (_, i) => {
                        const year = new Date().getFullYear() - i;
                        return (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex flex-col h-full">
                  <label className="block text-sm font-medium text-blue-800 mb-1">
                    Semana
                  </label>
                  <Select
                    value={selectedWeek.toString()}
                    onValueChange={(value) => {
                      const week = parseInt(value);
                      if (!isNaN(week)) {
                        handleWeekChange(week);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full bg-white border-blue-300 focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue placeholder="Seleccionar semana" />
                    </SelectTrigger>
                    <SelectContent
                      position="popper"
                      sideOffset={4}
                      className="z-[9999] max-h-[200px] overflow-y-auto"
                    >
                      {Array.from({ length: 52 }, (_, i) => {
                        const week = i + 1;
                        return (
                          <SelectItem key={week} value={week.toString()}>
                            Semana {week}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {/* Badge con fecha de la semana seleccionada */}
              {currentWeek && (
                <div className="mt-3 flex">
                  <div className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                    <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {currentWeek.startDate.toLocaleDateString('es-ES', {
                      day: '2-digit',
                      month: 'short',
                      year: 'numeric'
                    })} - {currentWeek.endDate.toLocaleDateString('es-ES', {
                      day: '2-digit',
                      month: 'short',
                      year: 'numeric'
                    })}
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            {kpiFields.map((field, index) => (
              <motion.div
                key={field.key}
                className="space-y-2"
                initial={{ opacity: 0, y: 4 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: 0.15 + (index * 0.02) }}
              >
                <div className="space-y-1">
                  <Tooltip content={field.description}>
                    <label className="font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-600 text-md cursor-default">
                      {field.title}
                    </label>
                  </Tooltip>
                </div>
                <input
                  type="text"
                  inputMode="decimal"
                  pattern="[0-9]*\.?[0-9]*"
                  placeholder="Ingrese un valor numérico"
                  value={formData[field.key]}
                  onChange={(e) => handleInputChange(field.key, e.target.value)}
                  className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 md:text-sm text-md ${
                    isReadOnlyMode
                      ? 'disabled:cursor-not-allowed disabled:opacity-50'
                      : ''
                  }`}
                  readOnly={isReadOnlyMode}
                  disabled={isReadOnlyMode}
                  required={!isReadOnlyMode}
                />
              </motion.div>
            ))}
          </div>

          {/* Footer */}
          <motion.div
            className="flex items-center justify-start space-x-4 pt-2"
            initial={{ opacity: 0, y: 4 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.3 }}
          >
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:border-transparent transition-colors"
            >
              {isReadOnlyMode ? "Cerrar" : "Cancelar"}
            </button>

            {!isReadOnlyMode && (
              <button
                type="submit"
                disabled={loading || (!!existingData && !hasChanges)}
                className="inline-flex items-center text-md bg-primary text-white font-medium leading-6 text-center align-middle select-none py-2 px-4 rounded-md transition-all hover:shadow-lg hover:shadow-primary/80 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? "Guardando" : (existingData ? "Actualizar" : "Guardar")}
                {!loading && (
                  <span className="w-4 h-4 ms-1">
                    <svg className="w-full h-full text-white" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                        <rect id="bound" x="0" y="0" width="24" height="24"></rect>
                        <path d="M3,13.5 L19,12 L3,10.5 L3,3.7732928 C3,3.70255344 3.01501031,3.63261921 3.04403925,3.56811047 C3.15735832,3.3162903 3.45336217,3.20401298 3.70518234,3.31733205 L21.9867539,11.5440392 C22.098181,11.5941815 22.1873901,11.6833905 22.2375323,11.7948177 C22.3508514,12.0466378 22.2385741,12.3426417 21.9867539,12.4559608 L3.70518234,20.6826679 C3.64067359,20.7116969 3.57073936,20.7267072 3.5,20.7267072 C3.22385763,20.7267072 3,20.5028496 3,20.2267072 L3,13.5 Z" id="Combined-Shape" fill="currentcolor"></path>
                      </g>
                    </svg>
                  </span>
                )}
              </button>
            )}
          </motion.div>
        </motion.form>
      </motion.div>

      {/* Alert Dialog para KPI duplicado */}
      <AlertDialog
        isOpen={showDuplicateAlert}
        onClose={() => setShowDuplicateAlert(false)}
        title="Datos ya registrados"
        message={`Los KPIs de logística de la semana ${formData.weekNumber}/${formData.year} ya están registrados. Puedes editarlos usando el botón "Editar" o seleccionar una semana diferente.`}
        type="warning"
        buttonText="Entendido"
      />

      {/* Alert Dialog para restricción de semana */}
      <AlertDialog
        isOpen={showWeekRestrictionAlert}
        onClose={() => setShowWeekRestrictionAlert(false)}
        title="Restricción de Semana"
        message={weekRestrictionMessage}
        type="warning"
        buttonText="Entendido"
      />

      {/* Alert Dialog para errores de validación */}
      <AlertDialog
        isOpen={showValidationErrorAlert}
        onClose={() => setShowValidationErrorAlert(false)}
        title="Error de validación"
        message={validationErrorMessage}
        type="error"
        buttonText="Corregir"
      />
    </>
  );
};

export default KpiLogisticaInlineForm;