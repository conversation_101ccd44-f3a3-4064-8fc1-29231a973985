'use client'

import dynamic from "next/dynamic"
import AuthProvider from "@/components/AuthProvider"

const AppProviders = dynamic(() => import("@/components/AppProviders"), {
  ssr: false,
  loading: () => null
})

export default function ClientProviders({ children }) {
  return (
    <AuthProvider>
      <AppProviders>
        <div id='__next_splash'>
          {children}
        </div>
      </AppProviders>
    </AuthProvider>
  )
}
