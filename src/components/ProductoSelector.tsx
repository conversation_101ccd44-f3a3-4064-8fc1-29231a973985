"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getProductosArray } from "@/app/types/productos"

interface ProductoSelectorProps {
  value: string
  onChange: (value: string) => void
}

export default function ProductoSelector({ value, onChange }: ProductoSelectorProps) {
  const productos = getProductosArray();
  
  // Función para mostrar el nombre formateado en la UI
  const formatProductName = (producto: string) => {
    return producto === 'Diesel' ? 'Diésel' : producto;
  };
  
  return (
    <div className="w-full md:w-3/4">
      <div className="mb-4">
        <Select value={value} onValueChange={onChange}>
          <SelectTrigger>
            <SelectValue placeholder="Seleccione un producto">
              {value && formatProductName(value)}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {productos.map((producto) => (
              <SelectItem key={producto} value={producto}>
                {formatProductName(producto)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
