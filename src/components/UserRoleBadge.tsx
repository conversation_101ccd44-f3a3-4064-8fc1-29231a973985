"use client";

import { useSession } from "next-auth/react";
import { USER_ROLES } from "@/lib/roleConstants";

const roleColors = {
  [USER_ROLES.USER]: "bg-blue-100 text-blue-800",
  [USER_ROLES.WORKER]: "bg-green-100 text-green-800",
  [USER_ROLES.ADMIN]: "bg-purple-100 text-purple-800",
  [USER_ROLES.SUPER_ADMIN]: "bg-red-100 text-red-800",
};

export default function UserRoleBadge() {
  const { data: session } = useSession();

  if (!session?.user?.role) return null;

  const role = session.user.role;
  const colorClass = roleColors[role as keyof typeof roleColors] || "bg-gray-100 text-gray-800";

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
      {role}
    </span>
  );
}
