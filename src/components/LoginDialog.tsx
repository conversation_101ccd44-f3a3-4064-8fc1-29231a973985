"use client";
import { useState, useEffect, FormEvent } from "react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { FaArrowRight, FaStar } from "react-icons/fa";
import { AiOutlineStock } from "react-icons/ai";
import { MdDone } from "react-icons/md";

interface LoginDialogProps {
  isOpen: boolean;
  onClose: () => void;
  message?: string;
  searchParams?: {
    startDate?: Date | null;
    endDate?: Date | null;
    terminal?: string;
    producto?: string;
  };
}

export default function LoginDialog({
  isOpen,
  onClose,
  searchParams,
}: LoginDialogProps) {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);
  const [email, setEmail] = useState("");
  const [step, setStep] = useState<"email" | "auth">("email");
  const [isValidEmail, setIsValidEmail] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setStep("email");
      setEmail("");
      setIsValidEmail(true);
      setIsSubmitting(false);
    } else {
      const timer = setTimeout(() => setIsVisible(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  if (!isOpen && !isVisible) return null;

  const validateEmail = (email: string) => {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(email);
  };

  const handleSubmitEmail = async (e: FormEvent) => {
    e.preventDefault();
    if (!email.trim() || !validateEmail(email)) {
      setIsValidEmail(false);
      return;
    }
    setIsValidEmail(true);
    setIsSubmitting(true);
    try {
      console.log('LoginDialog: Guardando lead con email:', email);
      const response = await fetch("/api/leads-db", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, source: "recoge-combustible" }),
      });
      let data;
      try {
        data = await response.json();
      } catch {
        setStep("auth");
        return;
      }
      if (!response.ok && response.status !== 409) {
        console.error("LoginDialog: Error al guardar lead:", data.message);
      }
      setStep("auth");
    } catch (error) {
      console.error("LoginDialog: Error general al guardar lead:", error);
      setStep("auth");
    } finally {
      setIsSubmitting(false);
    }
  };

  const saveSearchParams = () => {
    if (searchParams && typeof window !== "undefined") {
      localStorage.setItem(
        "searchParams",
        JSON.stringify({
          startDate: searchParams.startDate
            ? searchParams.startDate.toISOString()
            : null,
          endDate: searchParams.endDate
            ? searchParams.endDate.toISOString()
            : null,
          terminal: searchParams.terminal || "",
          producto: searchParams.producto || "",
        })
      );
    }
  };

  // Función para validar URLs de redirección
  const validateRedirectUrl = (url: string): string => {
    if (!url) return "/";
    
    try {
      // Verificar si la URL es relativa
      if (url.startsWith("/")) {
        const sanitizedPath = url.replace(/[^\w\s\-_\/\?\&\=\.]/gi, "");
        if (sanitizedPath.includes("//")) return "/";
        return sanitizedPath;
      }
      
      // Si la URL es absoluta, verificar que pertenece a nuestro dominio
      const currentHost = window.location.host;
      const urlObj = new URL(url);
      
      if (urlObj.host === currentHost) {
        return urlObj.pathname + urlObj.search;
      }
      
      return "/";
    } catch (error) {
      return "/";
    }
  };

  const handleSignIn = () => {
    saveSearchParams();
    const currentUrl = typeof window !== "undefined" ? window.location.href : "/";
    const safeUrl = validateRedirectUrl(currentUrl);
    signIn(undefined, { callbackUrl: safeUrl, email });
  };

  const handleSignUp = () => {
    saveSearchParams();
    router.push("/auth/signup");
  };

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 transition-opacity duration-300 ${
        isOpen ? "opacity-100" : "opacity-0"
      }`}
      onClick={onClose}
    >
      <div
        className={`bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-transform duration-300 ${
          isOpen ? "scale-100" : "scale-95"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {step === "email" ? (
          <>
            <div className="text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <AiOutlineStock className="h-7 w-7 text-blue-600" />
              </div>
              <h3 className="mt-2 text-xl font-medium text-gray-900">
                Descubre Precios Exclusivos al Instante
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Inicia sesión y accede de manera inmediata a precios mayoristas exclusivos.{" "}
                {[...Array(5)].map((_, i) => (
                  <FaStar key={i} className="inline-block text-yellow-500" />
                ))}
              </p>
            </div>
            <form onSubmit={handleSubmitEmail} className="mt-4">
              <div className="mb-1">
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 text-left mb-1"
                >
                  Correo electrónico
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`w-full px-3 py-2 border ${
                    !isValidEmail ? "border-red-500" : "border-gray-300"
                  } rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary`}
                  placeholder="<EMAIL>"
                  required
                />
                {!isValidEmail && (
                  <p className="mt-1 text-sm text-red-600">
                    Por favor, introduce un correo electrónico válido.
                  </p>
                )}
              </div>
              <div className="mt-4 space-y-3">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                      Procesando...
                    </span>
                  ) : (
                    <span className="flex items-center justify-center gap-2">
                      Ver Precios Ahora
                      <FaArrowRight />
                    </span>
                  )}
                </button>
                <button
                  type="button"
                  className="w-full text-sm text-gray-500 hover:text-gray-700 "
                  onClick={onClose}
                >
                  Cancelar
                </button>
              </div>
              <p className="mt-1 text-xs text-gray-400 text-center">
                Al continuar, autorizas que te enviemos información relevante
                sobre nuestros productos y servicios.
              </p>
            </form>
          </>
        ) : (
          <>
            <div className="text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <MdDone className="h-7 w-7 text-green-600" />
              </div>
              <h3 className="mt-2 text-lg font-medium text-gray-900">
                ¡Gracias por tu interés!
              </h3>
              <p className="mt-2 text-lg text-gray-600">
                <span className="text-primary font-medium">
                  Regístrate o inicia sesión
                </span>{" "}
                para consultar precios exclusivos.
              </p>
            </div>
            <div className="mt-4 space-y-3">
              <button
                type="button"
                className="w-full flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                onClick={handleSignUp}
              >
                <span>Crear Cuenta Gratis</span>
                <FaArrowRight className="ml-2" />
              </button>
              <button
                type="button"
                className="w-full justify-center rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-500 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                onClick={handleSignIn}
              >
                Iniciar sesión
              </button>
              <button
                type="button"
                className="w-full text-sm text-gray-500 hover:text-gray-700 py-1"
                onClick={onClose}
              >
                Cancelar
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
