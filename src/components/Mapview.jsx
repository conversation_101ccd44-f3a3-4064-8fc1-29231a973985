'use client';
import React, { useEffect, useRef } from 'react';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

const MapView = ({ userLatitude, userlongitude, userzoom }) => {
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const markerRef = useRef(null);

  useEffect(() => {
    // Verificar si estamos en el navegador
    if (typeof window === 'undefined') return;

    // Si ya existe una instancia del mapa, la eliminamos
    if (mapInstanceRef.current) {
      mapInstanceRef.current.remove();
    }

    // Crear una nueva instancia del mapa
    const map = L.map(mapRef.current).setView([userLatitude, userlongitude], userzoom);
    mapInstanceRef.current = map;

    // Añadir capa de mapa base
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Crear un icono personalizado para el marcador
    const customIcon = L.divIcon({
      className: 'custom-div-icon',
      html: `<div style="background-color: #fb7c3a; width: 12px; height: 12px; border-radius: 50%; opacity: 0.4;"></div>`,
      iconSize: [12, 12],
      iconAnchor: [6, 6]
    });

    // Añadir marcador en la ubicación del usuario
    markerRef.current = L.marker([userLatitude, userlongitude], { icon: customIcon }).addTo(map);

    // Añadir círculo para mostrar área de cobertura
    L.circle([userLatitude, userlongitude], {
      color: '#fb7c3a',
      fillColor: '#fb7c3a',
      fillOpacity: 0.1,
      radius: 50000 // 50km de radio
    }).addTo(map);

    // Limpiar al desmontar
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [userLatitude, userlongitude, userzoom]);

  return <div ref={mapRef} style={{ width: '100%', height: '100%' }} />;
};

export default MapView;
