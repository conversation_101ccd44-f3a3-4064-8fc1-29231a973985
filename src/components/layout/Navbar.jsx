'use client'
import { useEffect, useRef } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { getHorizontalMenuItems, getMenuItems } from '@/helpers/menu'
import { OffcanvasLayout } from '@/components'
import AppMenu from './Menu'
import VerticalMenu from './VerticalMenu'
import { useToggle } from '@/hooks'
import AuthStatus from '@/components/AuthStatus'

//images
import logoDark from '@/assets/images/logo-combustibles-cassiopeia.png'
import { FaBars, FaXmark } from 'react-icons/fa6'

const Navbar = () => {
  const [isOpenOffcanvas, toggleOffcanvas, _openOffcanvas, closeOffcanvas] =
    useToggle()

  const navbarRef = useRef(null)

  useEffect(() => {
    document.addEventListener('scroll', (e) => {
      e.preventDefault()
      if (navbarRef.current) {
        if (window.scrollY >= 80)
          navbarRef.current.classList.add('bg-white', 'shadow', 'lg:bg-white')
        else
          navbarRef.current.classList.remove(
            'bg-white',
            'shadow',
            'lg:bg-white'
          )
      }
    })
  }, [])

  return (
    <>
      <header 
        id="navbar"
        ref={navbarRef}
        className="fixed top-0 inset-x-0 flex items-center z-50 w-full lg:bg-transparent bg-white transition-all py-5"
      >
        <div className="container">
          <nav className="flex items-center text-lg">
            <Link href="/">
              <Image src={logoDark} width={224} alt="Logo" />
            </Link>
            <div className="lg:block hidden ms-auto">
              <AppMenu menuItems={getHorizontalMenuItems()} />
            </div>
            <div className="hidden lg:flex items-center ms-3">
              <AuthStatus />
            </div>
            <div className="lg:hidden flex items-center ms-auto px-2.5">
              <button 
                type="button" 
                onClick={toggleOffcanvas}
                aria-label="Abrir menú"
              >
                <FaBars size={24} />
              </button>
            </div>
          </nav>
        </div>
      </header>
      
      {/* Offcanvas móvil */}
      <OffcanvasLayout
        placement="end"
        sizeClassName="w-[447px] bg-white border-s"
        open={isOpenOffcanvas}
        toggleOffcanvas={closeOffcanvas}
      >
        <div className="flex flex-col h-[90vh]">
          {/* Mobile Menu Topbar Logo (Header) */}
          <div className="p-8 flex items-center justify-between">
            <Link href="/">
              <Image src={logoDark} width={126} alt="Logo" />
            </Link>
            <button className="flex items-center px-2" onClick={closeOffcanvas}>
              <FaXmark size={20} />
            </button>
          </div>
          {/* Mobile Menu Link List */}
          <div className="p-6 overflow-scroll h-full" id="right-menu">
            <VerticalMenu menuItems={getMenuItems()} closeMenu={closeOffcanvas} />
          </div>
          {/* Mobile Menu Auth Status (Footer) */}
          <div className="p-6 border border-gray-100 rounded-2xl">
            <div className="w-full">
              <h3 className="text-base font-medium text-gray-800 mb-4">Tu cuenta</h3>
              <AuthStatus />
            </div>
          </div>
        </div>
      </OffcanvasLayout>
    </>
  )
}

export default Navbar
