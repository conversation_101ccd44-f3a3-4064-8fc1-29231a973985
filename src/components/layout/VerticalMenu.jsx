import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { Fragment, useCallback, useEffect, useRef, useState, } from 'react'
import { FaAngleDown } from 'react-icons/fa6'
import SimpleCollapse from '@/components/frost-ui/SimpleCollapse'

// hooks
import { useNavigation } from '@/hooks'

// helpers
import { findAllParent, findMenuItem, getMenuItemFromURL } from '@/helpers/menu'

const MenuItemWithChildren = ({
  item,
  linkClassName,
  subMenuClassNames,
  activeMenuItems,
  toggleMenu,
  closeMenu,
}) => {
  const [open, setOpen] = useState(activeMenuItems.includes(item.key))

  useEffect(() => {
    setOpen(activeMenuItems.includes(item.key))
  }, [activeMenuItems, item])

  const toggleMenuItem = (e) => {
    // Prevent the event from bubbling up to parent elements
    e.stopPropagation()

    const status = !open
    setOpen(status)
    if (toggleMenu) toggleMenu(item, status)
    // Don't return false as it can prevent event propagation
  }

  return (
    <li className={`nav-item`}>
      <div
        className={`${linkClassName} ${activeMenuItems.includes(item['key']) ? 'active' : ''} text-lg font-medium py-2`}
        aria-expanded={open}
        data-menu-key={item.key}
        onClick={toggleMenuItem}
      >
        {item.label}
        {!item.badge && (
          <FaAngleDown
            style={{ marginLeft: 'auto', verticalAlign: 'middle' }}
            size={20}
          />
        )}
      </div>
      <SimpleCollapse
        open={open}
        as="ul"
        classNames={subMenuClassNames + ' sub-menu'}
      >
        {(item.children ?? []).map((child) => {
          return (
            <Fragment key={child.key}>
              {child.children ? (
                <MenuItemWithChildren
                  item={child}
                  toggleMenu={toggleMenu}
                  activeMenuItems={activeMenuItems}
                  subMenuClassNames="sub-menu"
                  linkClassName={
                    activeMenuItems.includes(child.key) ? 'active' : ''
                  }
                  closeMenu={closeMenu}
                />
              ) : (
                <MenuItem
                  item={child}
                  className={`ms-3 nav-item`}
                  linkClassName={
                    activeMenuItems.includes(child.key) ? 'active' : ''
                  }
                  closeMenu={closeMenu}
                />
              )}
            </Fragment>
          )
        })}
      </SimpleCollapse>
    </li>
  )
}

const MenuItem = ({ item, className, linkClassName, closeMenu }) => {
  return (
    <li className={`${className}`}>
      <MenuItemLink item={item} className={linkClassName} closeMenu={closeMenu} />
    </li>
  )
}

// Componente para enlaces de título que cierra el menú después de la navegación
const TitleLink = ({ href, label, closeMenu }) => {
  const { isNavigating } = useNavigation();
  const [shouldCloseMenu, setShouldCloseMenu] = useState(false);

  useEffect(() => {
    if (shouldCloseMenu && !isNavigating) {
      if (closeMenu) {
        closeMenu();
      }
      setShouldCloseMenu(false);
    }
  }, [isNavigating, shouldCloseMenu, closeMenu]);

  const handleClick = (e) => {
    e.stopPropagation();
    setShouldCloseMenu(true);
  };

  return (
    <Link
      href={href}
      className="nav-link py-2"
      onClick={handleClick}
    >
      <div className="text-lg font-medium">{label}</div>
    </Link>
  );
};

const MenuItemLink = ({ item, className, closeMenu }) => {
  const { isNavigating } = useNavigation();
  const [shouldCloseMenu, setShouldCloseMenu] = useState(false);

  useEffect(() => {
    if (shouldCloseMenu && !isNavigating) {
      if (closeMenu) {
        closeMenu();
      }
      setShouldCloseMenu(false);
    }
  }, [isNavigating, shouldCloseMenu, closeMenu]);

  const handleClick = (e) => {
    e.stopPropagation();
    setShouldCloseMenu(true);
  };

  return (
    <Link
      href={item.url}
      target={item.target}
      className={`nav-link ${className} py-2`}
      data-menu-key={item.key}
      onClick={handleClick}
    >
      {item.icon ? (
        <div className="flex items-center -ms-1.5">
          <span className="bg-blue-600/10 flex justify-center items-center w-10 h-10 shadow rounded me-3">
            {item.icon}
          </span>
          <div className="text-lg font-medium">{item.label}</div>
        </div>
      ) : (
        <div className="text-lg font-medium">{item.label}</div>
      )}
    </Link>
  )
}

const VerticalMenu = ({ menuItems, closeMenu }) => {
  const location = usePathname()

  const menuRef = useRef(null)

  const [activeMenuItems, setActiveMenuItems] = useState([])

  /**
   * toggle the menus
   */
  const toggleMenu = (menuItem, show) => {
    if (show) {
      setActiveMenuItems([
        menuItem['key'],
        // ...findAllParent(menuItems, menuItem),
      ])
    }
  }

  const activeMenu = useCallback(() => {
    const trimmedURL = location?.replaceAll('', '')
    const matchingMenuItem = getMenuItemFromURL(menuItems, trimmedURL)

    if (matchingMenuItem) {
      const activeMt = findMenuItem(menuItems, matchingMenuItem.key)
      if (activeMt) {
        setActiveMenuItems([
          activeMt['key'],
          ...findAllParent(menuItems, activeMt),
        ])
      }

      // Define scroll functions outside of setTimeout to avoid recreation
      // scrollTo (Right Side Bar Active Menu)
      const easeInOutQuad = (t, b, c, d) => {
        t /= d / 2
        if (t < 1) return (c / 2) * t * t + b
        t--
        return (-c / 2) * (t * (t - 2) - 1) + b
      }

      const scrollTo = (element, to, duration) => {
        if (!element) return;

        // Use requestAnimationFrame for smoother animation
        const start = element.scrollTop;
        const change = to - start;
        const startTime = performance.now();

        const animateScroll = (currentTime) => {
          const elapsedTime = currentTime - startTime;

          if (elapsedTime > duration) {
            element.scrollTop = to;
            return;
          }

          const val = easeInOutQuad(elapsedTime, start, change, duration);
          element.scrollTop = val;
          requestAnimationFrame(animateScroll);
        };

        requestAnimationFrame(animateScroll);
      };

      // Delay scrolling to ensure DOM is ready
      setTimeout(() => {
        try {
          const activatedItem = document.querySelector(
            `#right-menu a[href="${trimmedURL}"]`
          );

          if (activatedItem) {
            const simplebarContent = document.querySelector('#right-menu');
            if (!simplebarContent) return;

            const offset = activatedItem.offsetTop - 150;

            // Only scroll if needed
            if (simplebarContent && offset > 100) {
              scrollTo(simplebarContent, offset, 300); // Reduced duration for faster scrolling
            }
          }
        } catch (error) {
          console.error("Error during menu scroll:", error);
        }
      }, 300); // Increased delay to ensure DOM is ready
    }
  }, [location, menuItems])

  useEffect(() => {
    if (menuItems && menuItems.length > 0) activeMenu()
  }, [activeMenu, menuItems])

  return (
    <ul
      className="navbar-nav flex flex-col gap-3 menu"
      ref={menuRef}
      id="main-side-menu"
      onClick={(e) => e.stopPropagation()}
    >
      {(menuItems ?? []).map((item) => {
        return (
          <Fragment key={item.key}>
            {item.isTitle ? (
              <li className="nav-item">
                {item.url ? (
                  <TitleLink
                    href={item.url}
                    label={item.label}
                    closeMenu={closeMenu}
                  />
                ) : (
                  <div className="text-lg font-medium py-2">{item.label}</div>
                )}
              </li>
            ) : (
              <>
                {item.children ? (
                  <MenuItemWithChildren
                    item={item}
                    toggleMenu={toggleMenu}
                    subMenuClassNames="space-y-2"
                    activeMenuItems={activeMenuItems}
                    linkClassName="nav-link"
                    closeMenu={closeMenu}
                  />
                ) : (
                  <MenuItem
                    item={item}
                    linkClassName={`${activeMenuItems.includes(item.key) ? 'active' : ''
                      }`}
                    className={'nav-item'}
                    closeMenu={closeMenu}
                  />
                )}
              </>
            )}
          </Fragment>
        )
      })}
    </ul>
  )
}

export default VerticalMenu
