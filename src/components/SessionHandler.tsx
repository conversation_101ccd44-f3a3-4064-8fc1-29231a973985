"use client";
import { useEffect, useRef } from "react";
import { useSession } from "next-auth/react";

/**
 * Componente que maneja las solicitudes de sesión para evitar errores 400
 * Este componente intercepta las solicitudes a /api/auth/session y limita su frecuencia
 */
export default function SessionHandler() {
  const { status } = useSession();
  const originalFetch = useRef<typeof fetch | null>(null);
  const sessionCache = useRef<{ user: any; expires: string } | null>(null);

  useEffect(() => {
    // Solo configurar el interceptor una vez
    if (typeof window === "undefined" || originalFetch.current) return;

    // Guardar la función fetch original
    originalFetch.current = window.fetch;

    // Reemplazar la función fetch con nuestra versión personalizada
    window.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
      const url = input.toString();

      // Interceptar TODAS las solicitudes a /api/auth/session
      if (url.includes("/api/auth/session")) {
        console.log("SessionHandler: Interceptando solicitud a /api/auth/session");

        // Si tenemos una sesión en caché, devolverla
        if (sessionCache.current) {
          console.log("SessionHandler: Devolviendo sesión en caché");
          return new Response(JSON.stringify(sessionCache.current), {
            status: 200,
            headers: { "Content-Type": "application/json" }
          });
        }

        // Si no tenemos una sesión en caché, hacer la solicitud real
        try {
          console.log("SessionHandler: Haciendo solicitud real a /api/auth/session");
          if (!originalFetch.current) {
            throw new Error("Fetch original no disponible");
          }
          const response = await originalFetch.current(input, init);

          // Si la solicitud fue exitosa, guardar la respuesta en caché
          if (response.ok) {
            const data = await response.clone().json();
            sessionCache.current = data;
            console.log("SessionHandler: Sesión guardada en caché", data);
          }

          return response;
        } catch (error) {
          console.error("SessionHandler: Error al obtener la sesión", error);

          // En caso de error, devolver una respuesta vacía
          return new Response(JSON.stringify({
            user: null,
            expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          }), {
            status: 200,
            headers: { "Content-Type": "application/json" }
          });
        }
      }

      // Para todas las demás solicitudes, usar la función fetch original
      if (!originalFetch.current) {
        throw new Error("Fetch original no disponible");
      }
      return originalFetch.current(input, init);
    };

    // Limpiar el interceptor al desmontar el componente
    return () => {
      if (originalFetch.current) {
        window.fetch = originalFetch.current;
      }
    };
  }, []);

  // Actualizar la caché cuando cambia el estado de la sesión
  useEffect(() => {
    if (status === "authenticated") {
      // Forzar una solicitud a /api/auth/session para actualizar la caché
      fetch("/api/auth/session").then(async (response) => {
        if (response.ok) {
          const data = await response.json();
          sessionCache.current = data;
          console.log("SessionHandler: Sesión actualizada en caché", data);
        }
      }).catch(error => {
        console.error("SessionHandler: Error al actualizar la sesión", error);
      });
    } else if (status === "unauthenticated") {
      // Limpiar la caché si el usuario no está autenticado
      sessionCache.current = null;
    }
  }, [status]);

  // Este componente no renderiza nada
  return null;
}
