'use client';
import React, { useEffect, useRef } from 'react';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

interface Terminal {
  nombre: string;
  coordinates: {
    lat: number;
    lon: number;
  };
}

interface MapViewProps {
  userLatitude: number;
  userlongitude: number;
  userzoom: number;
  terminales?: Terminal[];
  terminalSeleccionada?: string;
}

const MapView: React.FC<MapViewProps> = ({
  userLatitude,
  userlongitude,
  userzoom,
  terminales = [],
  terminalSeleccionada = ''
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markerRef = useRef<L.Marker | null>(null);

  useEffect(() => {
    // Verificar si estamos en el navegador
    if (typeof window === 'undefined') return;

    // Si ya existe una instancia del mapa, la eliminamos
    if (mapInstanceRef.current) {
      mapInstanceRef.current.remove();
    }

    if (!mapRef.current) return;

    // Crear una nueva instancia del mapa
    const map = L.map(mapRef.current).setView([userLatitude, userlongitude], userzoom);
    mapInstanceRef.current = map;

    // Añadir capa de mapa base
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Crear un icono personalizado para el marcador
    const customIcon = L.divIcon({
      className: 'custom-div-icon',
      html: `<div style="background-color: #fb7c3a; width: 12px; height: 12px; border-radius: 50%; opacity: 0.4;"></div>`,
      iconSize: [12, 12],
      iconAnchor: [6, 6]
    });

    // Añadir marcador en la ubicación del usuario
    markerRef.current = L.marker([userLatitude, userlongitude], { icon: customIcon }).addTo(map);

    // Añadir círculo para mostrar área de cobertura
    L.circle([userLatitude, userlongitude], {
      color: '#fb7c3a',
      fillColor: '#fb7c3a',
      fillOpacity: 0.1,
      radius: 20000 // 50km de radio
    }).addTo(map);

    // Añadir marcadores para las terminales si existen
    if (terminales && terminales.length > 0) {
      const terminalIcon = L.divIcon({
        className: 'terminal-div-icon',
        html: `<div style="background-color: #3a7cfb; width: 10px; height: 10px; border-radius: 50%; opacity: 0.8;"></div>`,
        iconSize: [10, 10],
        iconAnchor: [5, 5]
      });

      const terminalSelectedIcon = L.divIcon({
        className: 'terminal-selected-div-icon',
        html: `<div style="background-color: #3a7cfb; width: 14px; height: 14px; border-radius: 50%; opacity: 1; border: 2px solid white;"></div>`,
        iconSize: [14, 14],
        iconAnchor: [7, 7]
      });

      terminales.forEach(terminal => {
        const isSelected = terminal.nombre === terminalSeleccionada;
        const icon = isSelected ? terminalSelectedIcon : terminalIcon;

        const marker = L.marker([terminal.coordinates.lat, terminal.coordinates.lon], {
          icon: icon
        }).addTo(map);

        marker.bindTooltip(terminal.nombre, {
          permanent: false,
          direction: 'top'
        });
      });
    }

    // Limpiar al desmontar
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [userLatitude, userlongitude, userzoom, terminales, terminalSeleccionada]);

  return <div ref={mapRef} style={{ width: '100%', height: '100%' }} />;
};

export default MapView;
