"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { deleteMultipleProveedores } from "@/app/actions/proveedores"
import { useToast } from "@/hooks/useToast"
import { ToastContainer } from "@/components/ToastContainer"

interface Provider {
  id?: string
  nombre: string
  activo: boolean
}

interface DeleteProvidersDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  providers: Provider[]
  onDelete?: (selectedProviders: string[]) => void
  onSuccess?: () => void
  onRefreshProviders?: () => void
}

export function DeleteProvidersDialog({ open, onOpenChange, providers, onDelete, onSuccess, onRefreshProviders }: DeleteProvidersDialogProps) {
  const [selectedProviders, setSelectedProviders] = useState<string[]>([])
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toasts, removeToast, success: showSuccess, error: showError } = useToast()

  const activeProviders = providers.filter((p) => p.activo)
  const allSelected = selectedProviders.length === activeProviders.length && activeProviders.length > 0

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProviders(activeProviders.map((p) => p.nombre))
    } else {
      setSelectedProviders([])
    }
  }

  const handleSelectProvider = (providerName: string, checked: boolean) => {
    if (checked) {
      setSelectedProviders([...selectedProviders, providerName])
    } else {
      setSelectedProviders(selectedProviders.filter((name) => name !== providerName))
    }
  }

  const handleDelete = async () => {
    if (selectedProviders.length === 0) return

    setIsDeleting(true)
    setError(null)

    try {
      const result = await deleteMultipleProveedores(selectedProviders)

      if (result.success) {
        // Llamar callback personalizado si existe
        if (onDelete) {
          onDelete(selectedProviders)
        }

        // Llamar callback de éxito si existe
        if (onSuccess) {
          onSuccess()
        }

        // Refrescar proveedores si existe el callback
        if (onRefreshProviders) {
          onRefreshProviders()
        }

        // Limpiar estado y cerrar modal
        setSelectedProviders([])
        onOpenChange(false)

        // Mostrar mensaje de éxito con toast
        if (result.data) {
          const { eliminados, proveedoresNoEncontrados } = result.data
          let title = `${eliminados} proveedor${eliminados > 1 ? 'es' : ''} eliminado${eliminados > 1 ? 's' : ''} correctamente`
          let description = undefined

          if (proveedoresNoEncontrados && proveedoresNoEncontrados.length > 0) {
            description = `Proveedores no encontrados: ${proveedoresNoEncontrados.join(', ')}`
          }

          showSuccess(title, description, 4000)
        }
      } else {
        const errorMessage = result.error || 'Error al eliminar proveedores'
        setError(errorMessage)
        showError('Error al eliminar', errorMessage, 5000)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error inesperado'
      setError(errorMessage)
      showError('Error inesperado', errorMessage, 5000)
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide cursor-default">
            Eliminar Proveedores
          </DialogTitle>
          <DialogDescription>Selecciona los proveedores que deseas eliminar</DialogDescription>
        </DialogHeader>

        {/* Error message */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Action buttons when providers are selected */}
        {selectedProviders.length > 0 && (
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="text-sm text-gray-700">
              {selectedProviders.length} seleccionado{selectedProviders.length > 1 ? "s" : ""}
            </span>
            <button
              type="button"
              onClick={handleDelete}
              disabled={isDeleting}
              className={`inline-flex items-center justify-center rounded-lg text-xs font-semibold border border-red-500 text-red-500 transition-all hover:shadow-lg hover:bg-red-500 hover:text-white hover:shadow-red-500/30 focus:shadow-none focus:outline focus:outline-red-500/40 px-3 py-2 ${
                isDeleting
                  ? 'cursor-not-allowed opacity-50'
                  : ''
              }`}
            >
              {isDeleting ? 'Eliminando...' : 'Eliminar'}
            </button>
          </div>
        )}

        {/* Providers list */}
        <ScrollArea className="max-h-80">
          <div className="space-y-2">
            {/* Select all checkbox */}
            <div
              className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-md cursor-pointer"
              onClick={() => handleSelectAll(!allSelected)}
            >
              <input
                type="checkbox"
                id="select-all"
                checked={allSelected}
                onChange={() => {}} // Manejado por el onClick del div
                className="w-4 h-4 rounded-xl border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2 pointer-events-none"
              />
              <label htmlFor="select-all" className="text-sm font-medium cursor-pointer flex-1 pointer-events-none">
                Seleccionar todos
              </label>
            </div>

            <Separator />

            {/* Individual provider checkboxes */}
            <div className="space-y-1">
              {activeProviders.map((provider, index) => (
                <div
                  key={provider.id || `provider-${index}`}
                  className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-md cursor-pointer"
                  onClick={() => handleSelectProvider(provider.nombre, !selectedProviders.includes(provider.nombre))}
                >
                  <input
                    type="checkbox"
                    id={`provider-${provider.id || index}`}
                    checked={selectedProviders.includes(provider.nombre)}
                    onChange={() => {}} // Manejado por el onClick del div
                    className="w-4 h-4 rounded-xl border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2 pointer-events-none"
                  />
                  <label htmlFor={`provider-${provider.id || index}`} className="text-sm cursor-pointer flex-1 pointer-events-none">
                    {provider.nombre}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </ScrollArea>
      </DialogContent>

      {/* Toast notifications */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </Dialog>
  )
}
