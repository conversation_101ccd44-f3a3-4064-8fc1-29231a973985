"use client"
import { useState, useEffect } from "react"
import { Loader2 } from "lucide-react"
import { Line } from "@/components/ui/chart"
import { GoDotFill } from "react-icons/go"
import { formatProductName } from "@/utils/format-producto";

interface Precio {
  id: string
  fecha: string | Date
  producto: string
  terminal: string
  valor: number
}

interface PreciosDashboardProps {
  loading: boolean
  datos: Precio[]
  isAuthenticated?: boolean
}

export default function PreciosDashboard({ loading, datos = [], isAuthenticated = false }: PreciosDashboardProps) {
  // Siempre inicializar activeTab con un valor por defecto para evitar saltos en la UI
  const [activeTab, setActiveTab] = useState<"precios"|"grafica">("precios")

  // Actualizar activeTab cuando cambia isAuthenticated
  useEffect(() => {
    // Si el usuario se desloguea, forzamos volver al tab por defecto
    if (!isAuthenticated) {
      setActiveTab("precios")
    }
  }, [isAuthenticated])

  // Formatear los datos para la tabla y el gráfico
  const datosFormateados = datos && datos.length > 0 ? datos.map(item => ({
    id: item.id || `precio-${Math.random().toString(36).substring(2, 11)}`,
    fecha: item.fecha instanceof Date ? item.fecha.toLocaleDateString() : new Date(item.fecha).toLocaleDateString(),
    producto: item.producto || 'Diésel',
    terminal: item.terminal || '',
    precio: `$${(typeof item.valor === 'number' ? item.valor : 0).toFixed(2)}`,
    valor: typeof item.valor === 'number' ? item.valor : 0
  })) : []

  const chartData = {
    labels: datosFormateados.map((item) => item.fecha),
    datasets: [
      {
        label: "Precio Diésel",
        data: datosFormateados.map((item) => item.valor),
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        tension: 0.3,
      },
    ],
  }

  const getProductDotColor = (producto: string) => {
    switch (producto) {
      case 'Diesel':
        return 'text-gray-500';
      case 'Gasolina Regular':
        return 'text-green-600';
      case 'Gasolina Premium':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  }

  return (
    <div className="w-full max-w-5xl mx-auto py-8">
      {/* Mantener siempre el contenedor de pestañas en el DOM con altura fija */}
      <div className="flex justify-center mb-6" style={{ minHeight: "48px" }}>
        {isAuthenticated ? (
          <ul className="flex flex-wrap justify-center gap-3">
            <li className="inline-block">
              <button
                onClick={() => {
                  if (isAuthenticated) {
                    setActiveTab("precios")
                  }
                }}
                disabled={!isAuthenticated}
                className={`inline-flex items-center border text-md font-medium leading-6 text-center align-middle select-none py-2 px-4 rounded-md transition-all ${
                  activeTab === "precios"
                    ? "bg-primary text-white"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                } ${!isAuthenticated && "opacity-50 cursor-not-allowed"}`}
              >
                Precios
              </button>
            </li>
            <li className="inline-block">
              <button
                onClick={() => {
                  if (isAuthenticated) {
                    setActiveTab("grafica")
                  }
                }}
                disabled={!isAuthenticated}
                className={`inline-flex items-center border text-md font-medium leading-6 text-center align-middle select-none py-2 px-4 rounded-md transition-all ${
                  activeTab === "grafica"
                    ? "bg-primary text-white"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                } ${!isAuthenticated && "opacity-50 cursor-not-allowed"}`}
              >
                Gráfica
              </button>
            </li>
          </ul>
        ) : (
          // Placeholder invisible para que el layout no salte
          <div className="w-full" style={{ visibility: "hidden" }}>
            {/* Contenido invisible para mantener el espacio */}
          </div>
        )}
      </div>

      <div className="relative bg-white rounded-lg overflow-hidden">
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-gray-600">Cargando precios...</span>
          </div>
        ) : datosFormateados.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <p>No hay precios disponibles para el periodo y terminal seleccionados.</p>
            <p className="mt-2 text-md">Selecciona otro rango de fechas o terminal para ver los precios.</p>
          </div>
        ) : (
          <>
            {/* Contenedor principal con posición relativa para los tabs absolutos */}
            <div className="relative min-h-[100px] max-h-[600px]">
              {isAuthenticated ? (
                <>
                  {/* Tab de Precios */}
                  <div
                    className={`transition-opacity duration-300 ease-in-out ${
                      activeTab === "precios" ? "opacity-100 z-10" : "opacity-0 z-0"
                    }`}
                    style={{
                      position: activeTab === "precios" ? "relative" : "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      visibility: activeTab === "precios" ? "visible" : "hidden",
                      height: 'fit-content'
                    }}
                  >
                    <div className="max-w-4xl mx-auto">
                      <div className="border rounded-lg overflow-hidden">
                        {/* Cabecera - Se oculta en móvil */}
                        <div className="hidden md:grid md:grid-cols-[minmax(0,1fr)_120px_120px_120px] md:items-center md:gap-x-4 px-4 py-2 text-sm font-medium sticky top-0 bg-white z-10 shadow-sm">
                          <span>Producto</span>
                          <span className="text-center pr-10">Fecha</span>
                          <span className="text-center pr-4">Terminal</span>
                          <span className="text-right pr-4">Precio</span>
                        </div>

                        {/* Cuerpo scrollable */}
                        <div className="max-h-[450px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                          {datosFormateados.map((item, idx) => (
                            <div
                              key={idx}
                              className="flex flex-col md:grid md:grid-cols-[minmax(0,1fr)_120px_120px_120px] md:items-center md:gap-x-4 px-4 py-4 hover:bg-gray-100 transition-colors border-b md:border-b-0"
                            >
                              {/* Producto */}
                              <div className="flex items-center justify-between md:justify-start gap-2 mb-2 md:mb-0">
                                <div className="flex items-center gap-2">
                                  <GoDotFill className={`${getProductDotColor(item.producto)}`} />
                                  <span className="font-medium text-gray-800">{formatProductName(item.producto)}</span>
                                </div>
                                {/* Precio en móvil */}
                                <span className="text-sm font-medium text-gray-800 md:hidden">{item.precio}</span>
                              </div>

                              {/* Contenedor para fecha y terminal en móvil */}
                              <div className="flex justify-between items-center md:contents">
                                {/* Fecha */}
                                <div className="md:contents">
                                  <span className="text-sm text-gray-600 md:hidden">Fecha: </span>
                                  <span className="px-3 py-1 rounded-full text-xs font-semibold bg-blue-600 text-white md:mx-auto">
                                    {item.fecha.toString()}
                                  </span>
                                </div>

                                {/* Terminal */}
                                <div className="md:contents">
                                  <span className="text-sm text-gray-600 md:hidden">Terminal: </span>
                                  <span className="text-sm text-center text-gray-600">{item.terminal}</span>
                                </div>

                                {/* Precio - Solo visible en desktop */}
                                <span className="hidden md:block text-sm font-medium text-right text-gray-800">
                                  {item.precio}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Pie de tabla */}
                        <div className="p-2 text-center text-xs text-gray-500 border-t">
                          {datosFormateados.length} resultados encontrados
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tab de Gráfica */}
                  <div
                    className={`transition-opacity duration-300 ease-in-out ${
                      activeTab === "grafica" ? "opacity-100 z-10" : "opacity-0 z-0"
                    }`}
                    style={{
                      position: activeTab === "grafica" ? "relative" : "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      visibility: activeTab === "grafica" ? "visible" : "hidden"
                    }}
                  >
                    <div className="p-6">
                      <h3 className="text-xs font-medium text-gray-400 uppercase tracking-wide">Tendencia de Precios</h3>
                      <div className="h-[400px] w-full">
                        <Line
                          data={chartData}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                              y: {
                                beginAtZero: false,
                                ticks: {
                                  callback: (value: number) => "$" + value.toFixed(2),
                                },
                              },
                            },
                            plugins: {
                              tooltip: {
                                callbacks: {
                                  label: (context: { parsed: { y: number } }) => "$" + context.parsed.y.toFixed(2),
                                },
                              },
                            },
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <p>Inicia sesión para ver los precios y gráficas.</p>
                  <p className="mt-2 text-md">Selecciona el botón "Iniciar Sesión para Ver Precios" para acceder.</p>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  )
}
