import { Line as ChartJSLine } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js"

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler)

interface LineProps {
  data: any;
  options?: any;
}

export function Line({ data, options }: LineProps) {
  return <ChartJSLine data={data} options={options} />
}
