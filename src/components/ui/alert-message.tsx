import React from 'react';
import { FaExclamationTriangle, FaCheckCircle, FaInfoCircle, FaTimes } from 'react-icons/fa';

type AlertType = 'error' | 'success' | 'info';

interface AlertMessageProps {
  type: AlertType;
  message: string;
  onClose?: () => void;
}

export function AlertMessage({ type, message, onClose }: AlertMessageProps) {
  const bgColor = {
    error:   'bg-red-50 border-red-100',
    success: 'bg-green-50 border-green-100',
    info:    'bg-blue-50 border-blue-100',
  }[type];

  const textColor = {
    error:   'text-red-600',
    success: 'text-green-600',
    info:    'text-blue-600',
  }[type];

  const Icon = {
    error:   FaExclamationTriangle,
    success: FaCheckCircle,
    info:    FaInfoCircle,
  }[type];

  return (
    <div
      className={`rounded-lg border p-3 sm:p-4 mb-4 shadow-sm ${bgColor} w-full z-10`}
      role="alert"
    >
      <div className="flex items-start">
        <div className="flex-shrink-0 mt-0.5">
          <Icon className={`h-4 w-4 sm:h-5 sm:w-5 ${textColor}`} aria-hidden="true" />
        </div>
        <div className="ml-2 sm:ml-3 flex-1">
          <p className={`text-xs sm:text-sm font-medium leading-tight sm:leading-normal ${textColor}`}>
            {message}
          </p>
        </div>
        {onClose && (
          <div className="ml-auto pl-2 sm:pl-3">
            <div className="-mx-1 -my-1 sm:-mx-1.5 sm:-my-1.5">
              <button
                type="button"
                onClick={onClose}
                className={`inline-flex rounded-md p-1 sm:p-1.5 ${textColor} hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-50`}
                aria-label="Cerrar"
              >
                <span className="sr-only">Cerrar</span>
                <FaTimes className="h-3 w-3 sm:h-4 sm:w-4" aria-hidden="true" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
