"use client";

import { ReactNode, useEffect } from "react";
import { usePathname } from "next/navigation";
import AOS from "aos";
import "aos/dist/aos.css";

interface Props {
  children: ReactNode;
}

export default function AppProviders({ children }: Props) {
  const pathname = usePathname();

  /* ---------- Preline ---------- */
  useEffect(() => {
    let cancel = () => {};

    const loadPreline = async () => {
      try {
        // 👇  nota la ruta
        const { HSStaticMethods } = await import("preline/preline");
        (window as any).HSStaticMethods = HSStaticMethods;
        HSStaticMethods.autoInit();
        cancel = () => {};            // limpia instancias si lo necesitas
      } catch (err) {
        console.error("Error cargando Preline:", err);
      }
    };

    if (typeof window !== "undefined") loadPreline();
    return () => cancel();
  }, []);

  /* ---------- AOS ---------- */
  useEffect(() => {
    const id = setTimeout(() => {
      AOS.init({ duration: 800, easing: "ease-out", once: true, mirror: false });
    }, 500);
    return () => clearTimeout(id);
  }, [pathname]);

  /* ---------- Splash ---------- */
  useEffect(() => {
    const id = setTimeout(() => {
      document.getElementById("splash-screen")?.classList.add("remove");
    }, 300);
    return () => clearTimeout(id);
  }, []);

  return <>{children}</>;
}
