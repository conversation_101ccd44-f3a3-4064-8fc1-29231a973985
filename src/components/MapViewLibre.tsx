"use client";
import React, { useRef, useEffect, useState } from 'react';
import maplibregl, { Map, Marker, Popup, GeoJSONSource } from 'maplibre-gl'; // Importaciones explícitas para claridad
import "@maptiler/geocoding-control/style.css"; // Estilos para el control de geocodificación (si se usara)
import "maplibre-gl/dist/maplibre-gl.css"; // Estilos base para MapLibre
import estadosGeoJson from "@/lib/estadosGeoJson"; // Datos geográficos de los estados

// --- Tipos de Datos (Interfaces) ---
// Definen la estructura esperada para los objetos que usamos.

/** Define la estructura de un objeto Terminal */
interface Terminal {
  nombre: string;
  coordinates: {
    lat: number;
    lon: number;
  };
}

/** Define las propiedades que el componente MapViewLibre espera recibir */
interface MapViewLibreProps {
  userLatitude: number;       // Latitud inicial o actual del usuario/centro del mapa.
  userlongitude: number;      // Longitud inicial o actual del usuario/centro del mapa.
  userzoom: number;           // Nivel de zoom inicial o actual del mapa.
  terminales?: Terminal[];    // Lista opcional de terminales a mostrar en el mapa.
  terminalSeleccionada?: string; // Nombre opcional de la terminal que debe resaltarse.
  showUserMarker?: boolean;   // Booleano opcional para mostrar u ocultar el marcador del usuario y su círculo.
}

// --- Constantes ---
// Centralizar valores fijos mejora la legibilidad y facilita cambios futuros.

/** Estilo CSS para el contenedor del mapa */
const mapContainerStyle: React.CSSProperties = {
  position: 'absolute', // Posicionamiento absoluto para llenar el contenedor padre.
  width: '100%',
  height: '100%',
  zIndex: 1, // Asegura que el mapa esté en una capa base (puede ajustarse si es necesario).
};

/** Radio en kilómetros para el círculo alrededor del marcador de usuario */
const USER_MARKER_RADIUS_KM = 12;

/** URL base del estilo del mapa de MapTiler */ // https://api.maptiler.com/maps/da5bfaf0-cd8f-4017-aad5-383a7f146942/style.json
const MAP_TILER_STYLE_URL = 'https://api.maptiler.com/maps/dataviz/style.json';

// --- Componente Principal ---

/**
 * Componente React para mostrar un mapa interactivo usando MapLibre GL JS.
 * Muestra una ubicación de usuario, terminales opcionales, y capas geográficas (estados).
 * Gestiona la inicialización, actualizaciones y limpieza del mapa.
 */
const MapViewLibre: React.FC<MapViewLibreProps> = ({
  // Desestructuración de props con valores por defecto para los opcionales
  userLatitude,
  userlongitude,
  userzoom,
  terminales = [], // Si no se proveen terminales, usa un array vacío.
  terminalSeleccionada = '', // Si no hay terminal seleccionada, usa un string vacío.
  showUserMarker = false // Por defecto, no muestra el marcador de usuario.
}) => {

  // --- Referencias (Refs) ---
  // Se usan para acceder al DOM o mantener referencias a objetos (como el mapa)
  // sin causar re-renderizados cuando cambian.

  /** Ref para el elemento HTML que contendrá el mapa */
  const mapContainer = useRef<HTMLDivElement>(null);
  /** Ref para la instancia del objeto Mapa de MapLibre una vez inicializado */
  const map = useRef<Map | null>(null);
  /** Ref para saber si el mapa ya ha sido inicializado (evita doble inicialización) */
  const isMapInitialized = useRef(false);

  // Refs para optimizar la animación 'flyTo': almacenan la última posición/zoom
  // a la que se animó el mapa, para evitar animaciones innecesarias si las props
  // se actualizan muy rápido pero al mismo valor.
  const lastAnimatedLatRef = useRef<number | null>(null);
  const lastAnimatedLonRef = useRef<number | null>(null);
  const lastAnimatedZoomRef = useRef<number | null>(null);

  // --- Estado (State) ---
  // Variables cuyo cambio SÍ debe provocar un re-renderizado del componente.

  /** Estado para guardar la clave API de MapTiler (obtenida asíncronamente) */
  const [apiKey, setApiKey] = useState<string | null>(null);
  /** Estado para mantener una referencia al marcador del usuario (si existe) */
  const [currentUserMarker, setCurrentUserMarker] = useState<Marker | null>(null);
  /** Estado para mantener una lista de los marcadores de las terminales */
  const [terminalMarkers, setTerminalMarkers] = useState<Marker[]>([]);

  // --- Funciones de Limpieza del Mapa ---
  // Funciones reutilizables para remover elementos específicos del mapa.

  /** Elimina el marcador del usuario y su círculo de cobertura del mapa */
  const clearUserMarkerAndCircle = () => {
    // Elimina el marcador si existe
    if (currentUserMarker) {
      currentUserMarker.remove();
      setCurrentUserMarker(null); // Limpia el estado
    }
    // Elimina las capas y la fuente del círculo si existen en el mapa
    if (map.current?.getSource('user-radius')) {
      if (map.current.getLayer('user-radius')) map.current.removeLayer('user-radius');
      if (map.current.getLayer('user-radius-outline')) map.current.removeLayer('user-radius-outline');
      map.current.removeSource('user-radius');
    }
  };

  /** Elimina las capas (relleno y contorno) y la fuente GeoJSON de los estados */
  const clearGeoJSONLayers = () => {
    if (map.current?.getSource('estados')) {
      try { // Usamos try-catch por si alguna capa ya fue removida por otro proceso
        if (map.current.getLayer('estados-outline')) map.current.removeLayer('estados-outline');
        if (map.current.getLayer('estados-fill')) map.current.removeLayer('estados-fill');
        map.current.removeSource('estados');
      } catch (error) {
        console.error('Error removing GeoJSON layers:', error);
      }
    }
  };

  /** Elimina todos los marcadores de terminales y sus popups asociados */
  const clearTerminalMarkers = () => {
     // Primero, cierra cualquier popup que esté abierto para evitar errores
    document.querySelectorAll('.maplibregl-popup').forEach(el => {
       try { (el as any).remove(); } catch (e) { console.warn("Could not remove popup:", e); }
    });
    // Luego, elimina cada marcador del mapa
    terminalMarkers.forEach(marker => marker.remove());
    // Finalmente, limpia el estado que los contenía
    setTerminalMarkers([]);
  };


  // --- Efectos de React (useEffect) ---
  // Gestionan la lógica que ocurre en diferentes momentos del ciclo de vida del componente
  // o como reacción a cambios en props o estado.

  /**
   * Efecto 1: Obtener la Clave API de MapTiler.
   * Se ejecuta SÓLO UNA VEZ cuando el componente se monta por primera vez (array de dependencias vacío: []).
   */
  useEffect(() => {
    let isCancelled = false; // Bandera para evitar actualizar el estado si el componente se desmonta antes de que termine el fetch.

    const fetchApiKey = async () => {
      console.log("Fetching Map API Key...");
      try {
        const response = await fetch('/api/mapviewapikey'); // Llama a tu endpoint de API
        if (!response.ok) {
          throw new Error(`API key fetch failed with status ${response.status}`);
        }
        const data = await response.json();
        if (!isCancelled) { // Solo actualiza si el componente sigue montado
          setApiKey(data.mapTilerApiKey); // Usar mapTilerApiKey en lugar de mapViewApiKey
          console.log("API Key fetched successfully.");
        }
      } catch (error) {
        if (!isCancelled) {
          console.error('Error fetching MAPTILER_API_KEY:', error);
          // Aquí podrías establecer un estado de error para mostrar en la UI
        }
      }
    };

    fetchApiKey();

    // Función de limpieza: se ejecuta si el componente se desmonta ANTES de que fetchApiKey termine.
    return () => {
      console.log("Cancelling API Key fetch (component unmounted).");
      isCancelled = true;
    };
  }, []); // <-- Array vacío = Ejecutar solo al montar.

  /**
   * Efecto 2: Inicializar el Mapa.
   * Se ejecuta CUANDO la `apiKey` se obtiene y SÓLO SI el mapa no ha sido inicializado antes.
   * Depende de `apiKey`.
   */
  useEffect(() => {
    // Condiciones para inicializar: necesitamos la key, el contenedor del DOM, y que no esté ya inicializado.
    if (apiKey && mapContainer.current && !isMapInitialized.current) {
      console.log("Initializing map...");
      try {
        // Crea la instancia del mapa
        map.current = new Map({
          container: mapContainer.current, // El div donde se renderizará el mapa
          style: `${MAP_TILER_STYLE_URL}?key=${apiKey}`, // URL del estilo + la key obtenida
          center: [userlongitude, userLatitude], // Centro inicial del mapa
          zoom: userzoom // Zoom inicial
        });

        // Marca el mapa como inicializado para evitar re-inicializaciones
        isMapInitialized.current = true;

        // Añade controles básicos al mapa
        map.current.addControl(new maplibregl.FullscreenControl()); // Botón de pantalla completa
        map.current.addControl(new maplibregl.NavigationControl(), 'top-left'); // Controles de zoom y rotación

        // Listener: Se ejecuta una vez que el estilo base del mapa ha cargado completamente.
        map.current.on('style.load', () => {
          console.log('Map style loaded. Adding initial layers and markers.');
          // Ahora es seguro añadir nuestras capas y marcadores personalizados.
          addGeoJsonLayers();   // Añade la capa de los estados de México.
          addTerminalMarkers(); // Añade los marcadores para las terminales.

          // Si la prop lo indica, muestra el marcador del usuario desde el inicio.
          if (showUserMarker) {
            addUserMarker();
          }

          // Guarda la posición/zoom inicial para la optimización de 'flyTo'.
          lastAnimatedLatRef.current = userLatitude;
          lastAnimatedLonRef.current = userlongitude;
          lastAnimatedZoomRef.current = userzoom;
        });

        // Listener: Captura y loguea errores generales del mapa.
        map.current.on('error', (e) => {
          console.error('MapLibre GL error:', e);
        });

        // Listener: Cierra cualquier popup abierto cuando se hace clic en el mapa.
        map.current.on('click', () => {
           document.querySelectorAll('.maplibregl-popup').forEach(el => {
             try { (el as any).remove(); } catch (e) { /* Ignorar si ya no existe */ }
           });
        });

      } catch (error) {
        console.error('Fatal error initializing map:', error);
        isMapInitialized.current = false; // Resetea por si se quiere intentar de nuevo (aunque probablemente falle igual)
      }
    }

    // Función de limpieza para ESTE efecto: Se ejecuta cuando el componente se desmonta.
    return () => {
      // Es CRUCIAL limpiar el mapa para liberar memoria y evitar problemas.
      if (map.current) {
        console.log("Cleaning up and removing map instance...");
        // Llama a las funciones de limpieza específicas antes de remover el mapa principal
        clearUserMarkerAndCircle();
        clearGeoJSONLayers();
        clearTerminalMarkers();
        // Remueve la instancia del mapa de MapLibre
        map.current.remove();
        map.current = null; // Limpia la referencia
        isMapInitialized.current = false; // Resetea el estado de inicialización
        // Resetea también las refs de animación
        lastAnimatedLatRef.current = null;
        lastAnimatedLonRef.current = null;
        lastAnimatedZoomRef.current = null;
      }
    };
  // Dependencia: sólo `apiKey`. No queremos re-inicializar si cambian lat/lon/zoom.
  }, [apiKey]);


  /**
   * Efecto 3: Actualizar la Vista del Mapa y el Marcador de Usuario.
   * Se ejecuta cuando cambian las coordenadas (`userLatitude`, `userlongitude`),
   * el zoom (`userzoom`), o la visibilidad del marcador (`showUserMarker`),
   * y SÓLO si el mapa ya está inicializado.
   */
  useEffect(() => {
    // No hacer nada si el mapa no está listo.
    if (!map.current || !isMapInitialized.current) {
      return;
    }

    console.log("Effect: Updating map view and/or user marker...");

    try {
        // --- Optimización de 'flyTo' ---
        // Compara las props actuales con la última posición/zoom animados.
        const latChanged = userLatitude !== lastAnimatedLatRef.current;
        const lonChanged = userlongitude !== lastAnimatedLonRef.current;
        const zoomChanged = userzoom !== lastAnimatedZoomRef.current;

        // Solo anima el mapa si hubo un cambio real en la posición o el zoom.
        if (latChanged || lonChanged || zoomChanged) {
          console.log(`MapViewLibre: Animating map (flyTo) to [${userlongitude}, ${userLatitude}], zoom ${userzoom}`);
          map.current.flyTo({
            center: [userlongitude, userLatitude],
            zoom: userzoom,
            essential: true // Marca la animación como importante
          });
          // Actualiza las referencias con los nuevos valores A LOS QUE se está animando.
          lastAnimatedLatRef.current = userLatitude;
          lastAnimatedLonRef.current = userlongitude;
          lastAnimatedZoomRef.current = userzoom;
        } else {
           // console.log('MapViewLibre: flyTo skipped (view parameters unchanged).'); // Descomentar para depuración detallada
        }

      // --- Gestión del Marcador de Usuario ---
      // 1. Siempre limpia el marcador/círculo anterior antes de decidir qué hacer.
      clearUserMarkerAndCircle();

      // 2. Si la prop `showUserMarker` es true, añade (o re-añade) el marcador y círculo.
      if (showUserMarker) {
        console.log("Effect: Adding/Updating user marker.");
        addUserMarker();
      } else {
        console.log("Effect: Ensuring user marker is hidden.");
      }

    } catch (error) {
      console.error('Error updating map view or user marker:', error);
    }

  // Dependencias: Ejecutar este efecto si cambia alguna de estas props o el estado de inicialización.
  }, [userLatitude, userlongitude, userzoom, showUserMarker, isMapInitialized.current]);


  /**
   * Efecto 4: Actualizar los Marcadores de las Terminales.
   * Se ejecuta cuando la lista de `terminales`, la `terminalSeleccionada` cambian,
   * o cuando el mapa se inicializa por primera vez.
   */
  useEffect(() => {
    // No hacer nada si el mapa no está listo.
    if (!map.current || !isMapInitialized.current) {
      return;
    }

    console.log("Effect: Updating terminal markers...");
    // La función `addTerminalMarkers` internamente limpia los marcadores viejos
    // antes de añadir los nuevos/actualizados basados en las props actuales.
    addTerminalMarkers();

  // Dependencias: Reacciona a cambios en la lista de terminales y la selección.
  // Nota: Comparar `terminales` directamente puede ser ineficiente si el padre
  //       pasa una nueva instancia de array en cada render. Para optimizar,
  //       se podría memoizar en el padre o usar JSON.stringify aquí (con cuidado).
  }, [terminales, terminalSeleccionada, isMapInitialized.current]);


  // --- Funciones Auxiliares para Interactuar con el Mapa ---

  /** Añade las capas GeoJSON para los estados de México al mapa */
  const addGeoJsonLayers = () => {
     // Evita añadir si ya existen (getSource devuelve la fuente si existe)
     if (!map.current || map.current.getSource('estados')) return;

     console.log("Adding GeoJSON layers for states...");
     try {
        // 1. Añadir la Fuente de Datos: Contiene la geometría y propiedades de los estados.
        map.current.addSource('estados', {
          type: 'geojson',
          data: estadosGeoJson as any // Le decimos a TS que confíe en la estructura de nuestro JSON.
        });

        // 2. Añadir Capa de Relleno: Dibuja los polígonos de los estados.
        map.current.addLayer({
          id: 'estados-fill', // Identificador único para la capa
          type: 'fill',       // Tipo de capa: relleno
          source: 'estados',  // Fuente de datos a usar
          paint: {            // Propiedades de estilo
            'fill-color': [   // Color de relleno basado en el nombre del estado
              'match',        // Operador 'match' para asignar colores
              ['get', 'name'], // Obtener la propiedad 'name' de cada feature
              'veracruz', '#fb7c3a', // Si name es 'campeche', usa este color
              // ... (resto de estados)
              /* default */ '#fb7c3a' // Color por defecto si no coincide ninguno
            ],
            'fill-opacity': 0.07 // Transparencia del relleno
          }
        });

        // 3. Añadir Capa de Contorno: Dibuja las líneas de borde de los estados.
        map.current.addLayer({
          id: 'estados-outline',
          type: 'line',       // Tipo de capa: línea
          source: 'estados',
          paint: {
            'line-color': '#000', // Color del borde
            'line-width': 0,   // Grosor del borde (más visible que 0)
            'line-opacity': 0  // Transparencia del borde
          }
        });
      } catch (error) {
        console.error('Error adding GeoJSON layers:', error);
      }
  };


  /** Añade o actualiza el marcador de usuario y su círculo de cobertura */
  const addUserMarker = () => {
    // Doble chequeo por si acaso, aunque el efecto que lo llama ya valida.
    if (!map.current || !showUserMarker) return;

    console.log("Adding user marker element and circle...");
    try {
      // 1. Crear el Elemento HTML para el Marcador: Un simple div estilizado.
      const markerElement = document.createElement('div');
      markerElement.className = 'user-marker'; // Útil para aplicar estilos CSS externos
      markerElement.style.backgroundColor = '#fb7c3a';
      markerElement.style.width = '12px';
      markerElement.style.height = '12px';
      markerElement.style.borderRadius = '50%';
      markerElement.style.border = '1px solid white'; // Borde para mejor contraste
      markerElement.style.opacity = '0.6';

      // 2. Crear la Instancia del Marcador de MapLibre: Asocia el elemento HTML a coordenadas.
      const newMarker = new Marker({ element: markerElement })
        .setLngLat([userlongitude, userLatitude]) // Posición
        .addTo(map.current); // Añadir al mapa

      // Guarda la referencia al marcador en el estado (útil para limpiarlo después)
      setCurrentUserMarker(newMarker);

      // 3. Añadir o Actualizar Círculo de Cobertura:
      // Calcula la geometría del círculo como un polígono GeoJSON.
      const circleGeoJSON = createGeoJSONCircle([userlongitude, userLatitude], USER_MARKER_RADIUS_KM);

      // Revisa si la fuente de datos para el círculo ya existe en el mapa.
      const existingSource = map.current.getSource('user-radius') as GeoJSONSource | undefined;

      if (existingSource) {
        // Si ya existe, simplemente actualiza sus datos (más eficiente que remover y añadir).
        existingSource.setData(circleGeoJSON as any);
      } else {
        // Si no existe, añade la fuente y las capas por primera vez.
        map.current.addSource('user-radius', {
          type: 'geojson',
          data: circleGeoJSON as any
        });

        // Capa de relleno para el círculo
        map.current.addLayer({
          id: 'user-radius',
          type: 'fill',
          source: 'user-radius',
          paint: { 'fill-color': '#fb7c3a', 'fill-opacity': 0.1 },
        }, 'estados-outline'); // Intenta dibujar DEBAJO del contorno de los estados

        // Capa de contorno para el círculo
        map.current.addLayer({
          id: 'user-radius-outline',
          type: 'line',
          source: 'user-radius',
          paint: { 'line-color': '#fb7c3a', 'line-width': 1 },
        }, 'estados-outline'); // Intenta dibujar DEBAJO del contorno de los estados
      }
    } catch (error) {
      console.error('Error adding user marker or circle:', error);
    }
  };


  /** Añade (o re-añade) los marcadores para todas las terminales */
  const addTerminalMarkers = () => {
    if (!map.current || !isMapInitialized.current) return; // Guard clause

    console.log(`Adding/Updating ${terminales.length} terminal markers...`);
    try {
      // 1. Limpieza Primero: Elimina los marcadores y popups anteriores.
      clearTerminalMarkers();

      const newMarkerInstances: Marker[] = []; // Almacenará las nuevas instancias de marcadores

      // 2. Iterar sobre la lista de terminales:
      terminales.forEach(terminal => {
         // Validación de datos básica: Asegura que las coordenadas son números.
         if (typeof terminal?.coordinates?.lat !== 'number' || typeof terminal?.coordinates?.lon !== 'number') {
           console.warn(`Skipping terminal "${terminal.nombre}" due to invalid coordinates.`);
           return; // Salta esta terminal y continúa con la siguiente
         }

        try {
          // Determina si esta terminal es la seleccionada actualmente.
          const isSelected = terminal.nombre === terminalSeleccionada;

          // 3. Crear Elemento HTML para el Marcador de Terminal:
          const markerElement = document.createElement('div');
          // Aplicar estilos base y condicionales si está seleccionada
          markerElement.style.width = isSelected ? '14px' : '10px';
          markerElement.style.height = isSelected ? '14px' : '10px';
          markerElement.style.backgroundColor = '#3a7cfb'; // Color azul para terminales
          markerElement.style.borderRadius = '50%';
          markerElement.style.border = isSelected ? '2px solid white' : '1px solid #ccc';
          markerElement.style.cursor = 'pointer';
          markerElement.style.opacity = isSelected ? '1' : '0.8';
          markerElement.style.boxShadow = isSelected ? '0 0 5px 2px rgba(58, 124, 251, 0.7)' : 'none'; // Resaltado visual
          markerElement.title = terminal.nombre; // Texto que aparece al pasar el ratón (tooltip)

          // 4. Crear Instancia del Marcador MapLibre:
          const marker = new Marker({ element: markerElement })
            .setLngLat([terminal.coordinates.lon, terminal.coordinates.lat]);

          // 5. Crear Popup Asociado: La ventana que aparece al hacer clic.
          const popup = new Popup({
            offset: [0, -15],      // Desplazamiento para que aparezca arriba del marcador
            closeButton: false,    // Sin botón de cierre (se cierra al hacer clic fuera)
            closeOnClick: true,    // Se cierra al hacer clic en el mapa
            maxWidth: '200px',     // Ancho máximo
          }).setHTML(`<div style="text-align:center; font-family: sans-serif;">
              <strong style="font-size: 13px; color: #333;">${terminal.nombre}</strong>
              <br>
              <span style="font-size: 12px; color: #666;">Terminal de Almacenamiento y Reparto.</span>
            </div>
          `);

          // Asocia el popup al marcador. No se muestra hasta hacer clic.
          marker.setPopup(popup);

          // 6. Añadir Evento Click al Elemento del Marcador:
          markerElement.addEventListener('click', (e) => {
            e.stopPropagation(); // MUY IMPORTANTE: Evita que el clic se propague al mapa (lo que cerraría el popup inmediatamente)

            // Cierra todos los OTROS popups que puedan estar abiertos
            document.querySelectorAll('.maplibregl-popup').forEach(p => {
              // Comprueba que no sea el popup que estamos a punto de abrir/alternar
              if (p !== popup.getElement()) {
                 try { (p as any).remove(); } catch (err) { /* Ignorar */ }
              }
            });

            // Alterna la visibilidad del popup de ESTE marcador (lo abre si está cerrado, lo cierra si está abierto)
            marker.togglePopup();
          });

          // 7. Añadir el marcador al mapa:
          if (map.current) {
             marker.addTo(map.current);
          }

          // Guarda la instancia del marcador para la limpieza posterior
          newMarkerInstances.push(marker);

        } catch (terminalError) {
          // Captura errores específicos al procesar UNA terminal, para no detener todo el bucle.
          console.error(`Error processing terminal marker for ${terminal.nombre}:`, terminalError);
        }
      }); // Fin del bucle forEach

      // 8. Actualizar el Estado: Guarda la lista de las nuevas instancias de marcadores creadas.
      setTerminalMarkers(newMarkerInstances);

    } catch (error) { // Captura errores generales durante el proceso de añadir marcadores.
      console.error('Error adding terminal markers:', error);
    }
  };

  /**
   * Función matemática para generar un polígono GeoJSON que representa un círculo.
   * @param center Coordenadas [longitud, latitud] del centro.
   * @param radiusInKm Radio del círculo en kilómetros.
   * @param points Número de puntos para aproximar el círculo (más puntos = más suave).
   * @returns Un objeto GeoJSON Feature con una geometría de Polígono.
   */
  const createGeoJSONCircle = (center: [number, number], radiusInKm: number, points = 64) => {
    const coords = { latitude: center[1], longitude: center[0] };
    const km = radiusInKm;
    const coordinates: [number, number][] = []; // Array para guardar los puntos del polígono

    // Factores de conversión aproximados de kilómetros a grados (varían con la latitud)
    const distanceX = km / (111.320 * Math.cos(coords.latitude * Math.PI / 180));
    const distanceY = km / 110.574;

    // Calcula los puntos alrededor del centro
    for (let i = 0; i < points; i++) {
      const angle = (i / points) * (2 * Math.PI); // Ángulo en radianes
      const x = distanceX * Math.cos(angle);     // Desplazamiento en longitud (grados)
      const y = distanceY * Math.sin(angle);     // Desplazamiento en latitud (grados)
      coordinates.push([coords.longitude + x, coords.latitude + y]);
    }
    coordinates.push(coordinates[0]); // Cierra el polígono volviendo al primer punto

    // Devuelve la estructura GeoJSON requerida por MapLibre
    return {
      type: 'Feature',
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates] // GeoJSON requiere un array de anillos (aquí solo uno)
      },
      properties: {} // Propiedades adicionales (vacías en este caso)
    };
  };


  // --- Renderizado del Componente ---
  // Devuelve el elemento JSX que se mostrará en la página.
  // En este caso, es solo un div vacío que actúa como contenedor para el mapa.
  // MapLibre se encargará de rellenar este div.
  console.log("Rendering MapViewLibre component...");
  return (
    <div
      ref={mapContainer} // Asigna la referencia para que MapLibre sepa dónde dibujar
      style={mapContainerStyle} // Aplica los estilos CSS definidos
      data-testid="map-container" // Útil para tests automatizados
    />
  );
};

export default MapViewLibre;
