"use client";

import { useSession, signOut } from "next-auth/react";
import Link from "next/link";

export default function AuthStatus() {
  const { data: session, status } = useSession();
  const isAuthenticated = status === "authenticated";
  const isLoading = status === "loading";

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-8 w-8 rounded-full bg-gray-200 animate-pulse"></div>
        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (isAuthenticated && session?.user) {
    return (
      <div className="flex flex-col md:flex-row md:items-center md:space-x-4">
        <div className="md:max-w-[150px] mb-3 md:mb-0">
          <span className="text-sm md:text-sm text-gray-700 block w-full overflow-hidden">
            <PERSON><PERSON>, <span className="font-medium inline md:block truncate">{(session.user.name?.split(' ')[0] || session.user.email)}</span>
          </span>
          {session.user.role && (
            <div className="mt-1">
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                session.user.role === "ADMIN" ? "bg-purple-100 text-purple-800" :
                session.user.role === "SUPER_ADMIN" ? "bg-red-100 text-red-800" :
                session.user.role === "WORKER" ? "bg-green-100 text-green-800" :
                "bg-blue-100 text-blue-800"
              }`}>
                {session.user.role}
              </span>
            </div>
          )}
        </div>
        <div className="flex flex-col space-y-3 md:space-y-2 w-full md:w-auto">
          <button
            onClick={() => signOut({ callbackUrl: "/" })}
            className="text-base md:text-sm bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 px-4 py-2.5 md:px-3 md:py-1.5 rounded-md transition-colors w-full md:w-auto flex justify-center items-center"
          >
            <span className="hidden lg:inline xl:hidden">Salir</span>
            <span className="inline lg:hidden xl:inline">Cerrar sesión</span>
          </button>
          {session.user.role === "WORKER" && (
            <Link
              href="/tarifas"
              className="text-base md:text-sm bg-green-600 hover:bg-green-700 text-white px-4 py-2.5 md:px-3 md:py-1.5 rounded-md transition-colors text-center w-full md:w-auto flex justify-center items-center"
            >
              Tarifas
            </Link>
          )}
          {(session.user.role === "ADMIN" || session.user.role === "SUPER_ADMIN") && (
            <Link
              href="/admin"
              className="text-base md:text-sm bg-primary hover:bg-primary/90 text-white px-4 py-2.5 md:px-3 md:py-1.5 rounded-md transition-colors text-center w-full md:w-auto flex justify-center items-center"
            >
              Admin
            </Link>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-2 w-full md:w-auto">
      <Link
        href="/auth/signin"
        className="text-base md:text-sm bg-white shadow-md md:shadow-none border border-gray-200 hover:bg-gray-50 text-gray-700 px-4 py-2.5 md:px-3 md:py-1.5 rounded-md transition-colors w-full md:w-auto flex justify-center items-center"
      >
        <span className="hidden lg:inline xl:hidden">Iniciar</span>
        <span className="inline lg:hidden xl:inline">Iniciar sesión</span>
      </Link>
      <Link
        href="/auth/signup"
        className="text-base md:text-sm bg-primary hover:bg-primary/90 text-white px-4 py-2.5 md:px-3 md:py-1.5 rounded-md transition-colors w-full md:w-auto flex justify-center items-center"
      >
        <span className="hidden lg:inline xl:hidden">Registro</span>
        <span className="inline lg:hidden xl:inline">Registrarse</span>
      </Link>
    </div>
  );
}
