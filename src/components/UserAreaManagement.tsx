"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { 
  getAreas, 
  getUsersWithAreas, 
  assignUserToArea, 
  removeUserFromArea,
  type AreaData,
  type UserWithArea 
} from "@/app/actions/areas";
import { Users, MapPin, X, Check, AlertCircle } from "lucide-react";

export default function UserAreaManagement() {
  const { data: session } = useSession();
  const [areas, setAreas] = useState<AreaData[]>([]);
  const [users, setUsers] = useState<UserWithArea[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Verificar permisos
  const canManageAreas = session?.user?.role && ["ADMIN", "SUPER_ADMIN"].includes(session.user.role);

  useEffect(() => {
    if (!canManageAreas) return;

    async function loadData() {
      try {
        setLoading(true);
        
        const [areasResult, usersResult] = await Promise.all([
          getAreas(),
          getUsersWithAreas()
        ]);

        if (areasResult.success && areasResult.data) {
          setAreas(areasResult.data);
        }

        if (usersResult.success && usersResult.data) {
          setUsers(usersResult.data);
        }
      } catch (error) {
        console.error("Error al cargar datos:", error);
        setMessage({ type: 'error', text: 'Error al cargar los datos' });
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [canManageAreas]);

  const handleAssignArea = async (userId: string, areaId: string) => {
    try {
      setSaving(userId);
      const result = await assignUserToArea(userId, areaId);
      
      if (result.success) {
        // Actualizar la lista de usuarios
        const usersResult = await getUsersWithAreas();
        if (usersResult.success && usersResult.data) {
          setUsers(usersResult.data);
        }
        setMessage({ type: 'success', text: 'Área asignada correctamente' });
      } else {
        setMessage({ type: 'error', text: result.error || 'Error al asignar área' });
      }
    } catch (error) {
      console.error("Error al asignar área:", error);
      setMessage({ type: 'error', text: 'Error interno del servidor' });
    } finally {
      setSaving(null);
    }
  };

  const handleRemoveArea = async (userId: string) => {
    try {
      setSaving(userId);
      const result = await removeUserFromArea(userId);
      
      if (result.success) {
        // Actualizar la lista de usuarios
        const usersResult = await getUsersWithAreas();
        if (usersResult.success && usersResult.data) {
          setUsers(usersResult.data);
        }
        setMessage({ type: 'success', text: 'Área removida correctamente' });
      } else {
        setMessage({ type: 'error', text: result.error || 'Error al remover área' });
      }
    } catch (error) {
      console.error("Error al remover área:", error);
      setMessage({ type: 'error', text: 'Error interno del servidor' });
    } finally {
      setSaving(null);
    }
  };

  // Limpiar mensaje después de 5 segundos
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (!canManageAreas) {
    return (
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
        <div className="flex items-center gap-3 text-red-600">
          <AlertCircle className="h-5 w-5" />
          <p>No tienes permisos para gestionar áreas de usuarios.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const workerUsers = users.filter(user => user.role === "WORKER");
  const otherUsers = users.filter(user => user.role !== "WORKER");

  return (
    <div className="space-y-6">
      {/* Mensaje de estado */}
      {message && (
        <div className={`p-4 rounded-lg border ${
          message.type === 'success' 
            ? 'bg-green-50 border-green-200 text-green-800' 
            : 'bg-red-50 border-red-200 text-red-800'
        }`}>
          <div className="flex items-center gap-2">
            {message.type === 'success' ? (
              <Check className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <span className="text-sm font-medium">{message.text}</span>
          </div>
        </div>
      )}

      {/* Gestión de Trabajadores */}
      <div className="bg-white rounded-lg shadow-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
              <Users className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Gestión de Áreas - Trabajadores</h3>
              <p className="text-sm text-gray-500">Asigna áreas específicas a los usuarios con rol WORKER</p>
            </div>
          </div>
        </div>

        <div className="p-6">
          {workerUsers.length === 0 ? (
            <p className="text-gray-500 text-center py-8">No hay usuarios con rol WORKER registrados.</p>
          ) : (
            <div className="space-y-4">
              {workerUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                      <span className="text-sm font-medium text-green-600">
                        {(user.name || user.email).charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{user.name || user.email}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    {/* Área actual */}
                    {user.area ? (
                      <div className="flex items-center gap-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <MapPin className="h-3 w-3 mr-1" />
                          {user.area.displayName}
                        </span>
                        <button
                          onClick={() => handleRemoveArea(user.id)}
                          disabled={saving === user.id}
                          className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50"
                          title="Remover área"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400">Sin área asignada</span>
                    )}

                    {/* Selector de área */}
                    <select
                      value={user.areaId || ""}
                      onChange={(e) => {
                        const areaId = e.target.value;
                        if (areaId && areaId !== user.areaId) {
                          handleAssignArea(user.id, areaId);
                        }
                      }}
                      disabled={saving === user.id}
                      className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                    >
                      <option value="">Seleccionar área...</option>
                      {areas.map((area) => (
                        <option key={area.id} value={area.id}>
                          {area.displayName}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Otros usuarios (solo para referencia) */}
      {otherUsers.length > 0 && (
        <div className="bg-white rounded-lg shadow-lg border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Otros Usuarios</h3>
            <p className="text-sm text-gray-500">Usuarios con otros roles (solo lectura)</p>
          </div>

          <div className="p-6">
            <div className="space-y-3">
              {otherUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-600">
                        {(user.name || user.email).charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{user.name || user.email}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      user.role === "ADMIN" ? "bg-purple-100 text-purple-800" :
                      user.role === "SUPER_ADMIN" ? "bg-red-100 text-red-800" :
                      "bg-blue-100 text-blue-800"
                    }`}>
                      {user.role}
                    </span>
                    {user.area && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <MapPin className="h-3 w-3 mr-1" />
                        {user.area.displayName}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
