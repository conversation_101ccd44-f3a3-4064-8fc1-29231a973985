"use client";
import { useEffect } from "react";

/**
 * Componente que limpia las cookies relacionadas con NextAuth en el cliente
 */
export default function CookieCleaner() {
  useEffect(() => {
    // Función para eliminar una cookie específica
    const deleteCookie = (name: string) => {
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    };

    // Eliminar todas las cookies relacionadas con NextAuth
    const cookieNames = [
      'next-auth.session-token',
      'next-auth.csrf-token',
      'next-auth.callback-url',
      '__Secure-next-auth.session-token',
      '__Secure-next-auth.csrf-token',
      '__Host-next-auth.csrf-token',
    ];

    cookieNames.forEach(deleteCookie);

    // Limpiar localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('next-auth.session-token');
      localStorage.removeItem('next-auth.csrf-token');
      localStorage.removeItem('next-auth.callback-url');
      localStorage.removeItem('next-auth.state');
    }

    console.log('CookieCleaner: Cookies y localStorage limpiados');
  }, []);

  // Este componente no renderiza nada
  return null;
}
