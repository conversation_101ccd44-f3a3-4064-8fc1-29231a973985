'use client'
import React, { Fragment } from 'react'
import { Popover, Transition } from '@headlessui/react'
import { useFloating, offset, shift, flip } from '@floating-ui/react'

const PopoverLayout = ({
  children,
  toggler,
  togglerClass,
  placement = 'bottom',
  menuClass,
}) => {
  const { refs, floatingStyles } = useFloating({
    placement,
    middleware: [offset(5), flip(), shift()],
  })

  return (
    <Popover className="relative">
      {({ open }) => (
        <>
          <Popover.Button
            ref={refs.setReference}
            className={togglerClass ?? ''}
          >
            {toggler}
          </Popover.Button>
          <Transition
            as={Fragment}
            show={open}
            enter="transition ease-out duration-200"
            enterFrom="opacity-0 translate-y-1"
            enterTo="opacity-100 translate-y-0"
            leave="transition ease-out duration-200"
            leaveFrom="opacity-100 translate-y-0"
            leaveTo="opacity-0 translate-y-1"
          >
            <Popover.Panel
              ref={refs.setFloating}
              style={floatingStyles}
              className={menuClass ?? 'mt-2 bg-white border rounded-lg shadow-lg'}
            >
              {children}
            </Popover.Panel>
          </Transition>
        </>
      )}
    </Popover>
  )
}

export default PopoverLayout
