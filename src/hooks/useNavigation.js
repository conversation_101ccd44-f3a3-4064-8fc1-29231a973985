'use client'
import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'

/**
 * Hook personalizado para detectar cuando una navegación está en progreso
 * @returns {Object} { isNavigating, currentPath }
 */
export default function useNavigation() {
  const pathname = usePathname()
  const [isNavigating, setIsNavigating] = useState(false)
  const [previousPath, setPreviousPath] = useState(null)
  
  useEffect(() => {
    // Al iniciar, establecer la ruta inicial
    if (previousPath === null) {
      setPreviousPath(pathname)
      return
    }
    
    // Si la ruta ha cambiado, estamos navegando
    if (pathname !== previousPath) {
      setIsNavigating(true)
      
      // Simular un tiempo de carga para asegurar que la página se ha cargado completamente
      const timer = setTimeout(() => {
        setIsNavigating(false)
        setPreviousPath(pathname)
      }, 500) // Ajustar este tiempo según sea necesario
      
      return () => clearTimeout(timer)
    }
  }, [pathname, previousPath])
  
  return { isNavigating, currentPath: pathname }
}
