// Función para calcular la tarifa más cercana al promedio
export const calcularTarifaCercana = (tarifas) => {
  if (!tarifas || tarifas.length === 0) return 0;
  
  // Calcular el promedio de las tarifas
  const promedio = tarifas.reduce((acc, tarifa) => acc + tarifa, 0) / tarifas.length;
  
  // Encontrar la tarifa más cercana al promedio
  return tarifas.reduce((prev, curr) => {
    return Math.abs(curr - promedio) < Math.abs(prev - promedio) ? curr : prev;
  }, tarifas[0]);
};

// Función para geocodificar una dirección
export const geocoder = async (direccion) => {
  try {
    const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(direccion)}`);
    const data = await response.json();
    
    if (data && data.length > 0) {
      return {
        lat: parseFloat(data[0].lat),
        lon: parseFloat(data[0].lon)
      };
    }
    return null;
  } catch (error) {
    console.error('Error en geocodificación:', error);
    return null;
  }
};

// Estados para el cotizador
export const EstadosQuote = [
  { value: "nuevo leon", label: "Nuevo León" },
  { value: "hidalgo", label: "Hidalgo" },
  { value: "veracruz", label: "Veracruz" },
  { value: "campeche", label: "Campeche" },
  { value: "quintana roo", label: "Quintana Roo" },
  { value: "tamaulipas", label: "Tamaulipas" },
  { value: "edomex", label: "Estado de México" },
  { value: "puebla", label: "Puebla" },
  { value: "queretaro", label: "Querétaro" },
  { value: "san luis potosi", label: "San Luis Potosí" },
  { value: "tabasco", label: "Tabasco" },
  { value: "yucatan", label: "Yucatán" }
];

// Ciudades por estado para el cotizador
export const CiudadesMQuote = {
  "nuevo leon": [
    { value: "monterrey", label: "Monterrey" },
    { value: "guadalupe", label: "Guadalupe" },
    { value: "apodaca", label: "Apodaca" },
    { value: "san nicolas de los garza", label: "San Nicolás de los Garza" },
    { value: "general escobedo", label: "General Escobedo" },
    { value: "santa catarina", label: "Santa Catarina" },
    { value: "juarez", label: "Juárez" },
    { value: "garcia", label: "García" },
    { value: "san pedro garza garcia", label: "San Pedro Garza García" },
    { value: "cadereyta jimenez", label: "Cadereyta Jiménez" }
  ],
  "hidalgo": [
    { value: "pachuca de soto", label: "Pachuca de Soto" },
    { value: "mineral de la reforma", label: "Mineral de la Reforma" },
    { value: "tulancingo", label: "Tulancingo" },
    { value: "tizayuca", label: "Tizayuca" },
    { value: "huejutla de reyes", label: "Huejutla de Reyes" },
    { value: "tula de allende", label: "Tula de Allende" },
    { value: "tepeji del rio", label: "Tepeji del Río" },
    { value: "ixmiquilpan", label: "Ixmiquilpan" },
    { value: "actopan", label: "Actopan" },
    { value: "apan", label: "Apan" }
  ],
  "veracruz": [
    { value: "veracruz", label: "Veracruz" },
    { value: "xalapa", label: "Xalapa" },
    { value: "coatzacoalcos", label: "Coatzacoalcos" },
    { value: "poza rica", label: "Poza Rica" },
    { value: "cordoba", label: "Córdoba" },
    { value: "orizaba", label: "Orizaba" },
    { value: "minatitlan", label: "Minatitlán" },
    { value: "boca del rio", label: "Boca del Río" },
    { value: "tuxpan", label: "Tuxpan" },
    { value: "martinez de la torre", label: "Martínez de la Torre" }
  ],
  "campeche": [
    { value: "campeche", label: "Campeche" },
    { value: "ciudad del carmen", label: "Ciudad del Carmen" },
    { value: "champoton", label: "Champotón" },
    { value: "escarcega", label: "Escárcega" },
    { value: "calkini", label: "Calkiní" },
    { value: "hecelchakan", label: "Hecelchakán" },
    { value: "hopelchen", label: "Hopelchén" },
    { value: "tenabo", label: "Tenabo" },
    { value: "palizada", label: "Palizada" },
    { value: "candelaria", label: "Candelaria" }
  ],
  "quintana roo": [
    { value: "cancun", label: "Cancún" },
    { value: "chetumal", label: "Chetumal" },
    { value: "playa del carmen", label: "Playa del Carmen" },
    { value: "cozumel", label: "Cozumel" },
    { value: "tulum", label: "Tulum" },
    { value: "isla mujeres", label: "Isla Mujeres" },
    { value: "felipe carrillo puerto", label: "Felipe Carrillo Puerto" },
    { value: "bacalar", label: "Bacalar" },
    { value: "jose maria morelos", label: "José María Morelos" },
    { value: "lazaro cardenas", label: "Lázaro Cárdenas" }
  ],
  "tamaulipas": [
    { value: "reynosa", label: "Reynosa" },
    { value: "matamoros", label: "Matamoros" },
    { value: "nuevo laredo", label: "Nuevo Laredo" },
    { value: "ciudad victoria", label: "Ciudad Victoria" },
    { value: "tampico", label: "Tampico" },
    { value: "ciudad madero", label: "Ciudad Madero" },
    { value: "altamira", label: "Altamira" },
    { value: "rio bravo", label: "Río Bravo" },
    { value: "mante", label: "Mante" },
    { value: "valle hermoso", label: "Valle Hermoso" }
  ],
  "edomex": [
    { value: "ecatepec", label: "Ecatepec" },
    { value: "nezahualcoyotl", label: "Nezahualcóyotl" },
    { value: "toluca", label: "Toluca" },
    { value: "naucalpan", label: "Naucalpan" },
    { value: "chimalhuacan", label: "Chimalhuacán" },
    { value: "tlalnepantla", label: "Tlalnepantla" },
    { value: "tultitlan", label: "Tultitlán" },
    { value: "cuautitlan izcalli", label: "Cuautitlán Izcalli" },
    { value: "atizapan de zaragoza", label: "Atizapán de Zaragoza" },
    { value: "ixtapaluca", label: "Ixtapaluca" }
  ],
  "puebla": [
    { value: "puebla", label: "Puebla" },
    { value: "tehuacan", label: "Tehuacán" },
    { value: "san martin texmelucan", label: "San Martín Texmelucan" },
    { value: "atlixco", label: "Atlixco" },
    { value: "san pedro cholula", label: "San Pedro Cholula" },
    { value: "san andres cholula", label: "San Andrés Cholula" },
    { value: "amozoc", label: "Amozoc" },
    { value: "huauchinango", label: "Huauchinango" },
    { value: "teziutlan", label: "Teziutlán" },
    { value: "izucar de matamoros", label: "Izúcar de Matamoros" }
  ],
  "queretaro": [
    { value: "queretaro", label: "Querétaro" },
    { value: "san juan del rio", label: "San Juan del Río" },
    { value: "corregidora", label: "Corregidora" },
    { value: "el marques", label: "El Marqués" },
    { value: "tequisquiapan", label: "Tequisquiapan" },
    { value: "pedro escobedo", label: "Pedro Escobedo" },
    { value: "amealco de bonfil", label: "Amealco de Bonfil" },
    { value: "colon", label: "Colón" },
    { value: "cadereyta de montes", label: "Cadereyta de Montes" },
    { value: "jalpan de serra", label: "Jalpan de Serra" }
  ],
  "san luis potosi": [
    { value: "san luis potosi", label: "San Luis Potosí" },
    { value: "soledad de graciano sanchez", label: "Soledad de Graciano Sánchez" },
    { value: "ciudad valles", label: "Ciudad Valles" },
    { value: "matehuala", label: "Matehuala" },
    { value: "rioverde", label: "Rioverde" },
    { value: "ciudad fernandez", label: "Ciudad Fernández" },
    { value: "tamazunchale", label: "Tamazunchale" },
    { value: "ebano", label: "Ébano" },
    { value: "xilitla", label: "Xilitla" },
    { value: "tamuin", label: "Tamuín" }
  ],
  "tabasco": [
    { value: "villahermosa", label: "Villahermosa" },
    { value: "cardenas", label: "Cárdenas" },
    { value: "comalcalco", label: "Comalcalco" },
    { value: "huimanguillo", label: "Huimanguillo" },
    { value: "macuspana", label: "Macuspana" },
    { value: "teapa", label: "Teapa" },
    { value: "cunduacan", label: "Cunduacán" },
    { value: "paraiso", label: "Paraíso" },
    { value: "centla", label: "Centla" },
    { value: "jalpa de mendez", label: "Jalpa de Méndez" }
  ],
  "yucatan": [
    { value: "merida", label: "Mérida" },
    { value: "valladolid", label: "Valladolid" },
    { value: "tizimin", label: "Tizimín" },
    { value: "progreso", label: "Progreso" },
    { value: "kanasin", label: "Kanasín" },
    { value: "uman", label: "Umán" },
    { value: "tekax", label: "Tekax" },
    { value: "motul", label: "Motul" },
    { value: "izamal", label: "Izamal" },
    { value: "ticul", label: "Ticul" }
  ]
};
