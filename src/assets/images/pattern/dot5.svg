<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="400" height="400" viewBox="0 0 400 400" xml:space="preserve">
<desc>Created with Fabric.js 4.0.0-beta.8</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 200.5 200.5)"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-opacity: 0; fill-rule: nonzero; opacity: 1;"  x="-200" y="-200" rx="0" ry="0" width="400" height="400" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 4 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 14 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 24 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 34 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 44 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 54 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 64 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 74 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 84 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 94 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 104 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 114 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 124 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 134 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 144 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 154 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 164 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 174 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 184 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 194 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 205 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 215 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 225 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 235 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 245 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 255 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 265 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 275 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 285 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 295 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 305 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 315 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 325 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 335 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 345 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 355 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 365 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 375 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 385 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 4)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 14)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 24)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 34)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 44)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 54)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 64)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 74)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 84)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 94)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 104)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 114)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 124)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 134)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 144)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 154)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 164)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 174)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 184)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 194)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 205)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 215)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 225)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 235)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 245)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 255)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 265)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 275)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 285)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 295)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 305)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 315)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 325)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 335)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 345)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.7; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 355)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 365)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 375)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 385)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 395 395)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,165,0); fill-opacity: 0.6; fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
</svg>
