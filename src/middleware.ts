import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Función para validar URLs de redirección
const validateRedirectUrl = (url: string, baseUrl: string): string => {
  try {
    // Si la URL es relativa, está bien
    if (url.startsWith("/")) {
      return url;
    }
    
    // Si es absoluta, verificar que pertenece al mismo dominio
    const urlObj = new URL(url);
    const baseUrlObj = new URL(baseUrl);
    
    if (urlObj.host === baseUrlObj.host) {
      return urlObj.pathname + urlObj.search;
    }
    
    // Si no, redirigir a la página principal
    return "/";
  } catch (error) {
    // Si hay un error, redirigir a la página principal
    return "/";
  }
};

// Rutas que requieren autenticación y roles específicos
const protectedRoutes = [
  {
    path: '/admin',
    requiredRole: 'WORKER' // Permitir acceso a trabajadores, admins y super admins
  },
  {
    path: '/tarifas',
    requiredRole: 'WORKER'
  },
  {
    path: '/cotiza',
    requiredRole: 'ADMIN'
  }
];

// Rutas públicas (no requieren autenticación)
const publicRoutes = [
  '/',
  '/auth/signin',
  '/auth/signup',
  '/auth/error',
  '/api/auth',
  '/recoge-combustible'
];

export async function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;
  const fullUrl = `${pathname}${search}`;

  // Verificar si la ruta es pública
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    // Si es la página de inicio de sesión, validar el parámetro callbackUrl
    if (pathname === '/auth/signin') {
      const url = new URL(request.url);
      const callbackUrl = url.searchParams.get('callbackUrl');
      
      if (callbackUrl) {
        const safeCallbackUrl = validateRedirectUrl(callbackUrl, request.url);
        
        // Si la URL ha cambiado, redirigir a la versión segura
        if (safeCallbackUrl !== callbackUrl) {
          const safeUrl = new URL('/auth/signin', request.url);
          if (safeCallbackUrl !== "/") {
            safeUrl.searchParams.set('callbackUrl', safeCallbackUrl);
          }
          return NextResponse.redirect(safeUrl);
        }
      }
    }
    
    return NextResponse.next();
  }

  // Verificar si la ruta está protegida
  const protectedRoute = protectedRoutes.find(route => pathname.startsWith(route.path));

  // Verificar si el usuario está autenticado comprobando la cookie de sesión
  const sessionCookie = request.cookies.get('next-auth.session-token') ||
                        request.cookies.get('__Secure-next-auth.session-token');

  // Si no hay cookie de sesión y la ruta no es pública, redirigir a login
  if (!sessionCookie) {
    const url = new URL('/auth/signin', request.url);
    // Validar la URL de callback antes de incluirla
    const safeCallbackUrl = validateRedirectUrl(fullUrl, request.url);
    url.searchParams.set('callbackUrl', safeCallbackUrl);
    return NextResponse.redirect(url);
  }

  // Si la ruta está protegida, necesitamos verificar el rol
  if (protectedRoute) {
    // Añadimos la ruta protegida a la que se quiere acceder como parámetro
    const verifyUrl = new URL('/api/auth/verify-role', request.url);
    verifyUrl.searchParams.set('requiredRole', protectedRoute.requiredRole);
    // Validar la URL de redirección
    const safeRedirectUrl = validateRedirectUrl(request.url, request.url);
    verifyUrl.searchParams.set('redirectUrl', safeRedirectUrl);

    // Redirigimos a la API de verificación
    return NextResponse.rewrite(verifyUrl);
  }

  return NextResponse.next();
}

// Configurar el middleware para que se ejecute en todas las rutas excepto las estáticas
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
