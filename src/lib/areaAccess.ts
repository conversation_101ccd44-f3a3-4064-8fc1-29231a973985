"use server";

import { getSession } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { isValidArea, type AreaType } from "@/lib/areaConstants";

/**
 * Verifica si el usuario actual tiene acceso de LECTURA a un área específica
 * WORKERS pueden ver todas las áreas, pero solo editar la suya
 */
export async function hasAreaReadAccess(areaName: string): Promise<boolean> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return false;
    }

    // Validar que el área es válida
    if (!isValidArea(areaName)) {
      return false;
    }

    // Los ADMIN, SUPER_ADMIN y WORKER tienen acceso de lectura a todas las áreas
    if (["ADMIN", "SUPER_ADMIN", "WORKER"].includes(session.user.role)) {
      return true;
    }

    // Otros roles no tienen acceso por defecto
    return false;
  } catch (error) {
    console.error("Error al verificar acceso de lectura al área:", error);
    return false;
  }
}

/**
 * Verifica si el usuario actual tiene acceso de ESCRITURA a un área específica
 * Solo ADMIN, SUPER_ADMIN y WORKERS en su área asignada pueden escribir
 */
export async function hasAreaWriteAccess(areaName: string): Promise<boolean> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return false;
    }

    // Validar que el área es válida
    if (!isValidArea(areaName)) {
      return false;
    }

    // Los ADMIN y SUPER_ADMIN tienen acceso de escritura a todas las áreas
    if (["ADMIN", "SUPER_ADMIN"].includes(session.user.role)) {
      return true;
    }

    // Para usuarios WORKER, solo pueden escribir en su área asignada
    if (session.user.role === "WORKER") {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { area: true }
      });

      if (!user || !user.area) {
        return false;
      }

      return user.area.nombre === areaName;
    }

    // Otros roles no tienen acceso de escritura por defecto
    return false;
  } catch (error) {
    console.error("Error al verificar acceso de escritura al área:", error);
    return false;
  }
}

/**
 * Función de compatibilidad - ahora usa acceso de lectura
 * @deprecated Usar hasAreaReadAccess o hasAreaWriteAccess según el caso
 */
export async function hasAreaAccess(areaName: string): Promise<boolean> {
  return hasAreaReadAccess(areaName);
}

/**
 * Obtiene las áreas a las que el usuario actual tiene acceso de LECTURA
 * WORKERS pueden ver todas las áreas
 */
export async function getUserAccessibleAreas(): Promise<AreaType[]> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return [];
    }

    // Los ADMIN, SUPER_ADMIN y WORKER tienen acceso de lectura a todas las áreas
    if (["ADMIN", "SUPER_ADMIN", "WORKER"].includes(session.user.role)) {
      // Obtener todas las áreas activas
      const areas = await prisma.area.findMany({
        where: { activo: true },
        orderBy: { orden: 'asc' }
      });
      return areas.map(area => area.nombre as AreaType);
    }

    // Otros roles no tienen acceso por defecto
    return [];
  } catch (error) {
    console.error("Error al obtener áreas accesibles:", error);
    return [];
  }
}

/**
 * Obtiene las áreas en las que el usuario actual puede ESCRIBIR/EDITAR
 */
export async function getUserWritableAreas(): Promise<AreaType[]> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return [];
    }

    // Los ADMIN y SUPER_ADMIN pueden escribir en todas las áreas
    if (["ADMIN", "SUPER_ADMIN"].includes(session.user.role)) {
      const areas = await prisma.area.findMany({
        where: { activo: true },
        orderBy: { orden: 'asc' }
      });
      return areas.map(area => area.nombre as AreaType);
    }

    // Para usuarios WORKER, solo su área asignada
    if (session.user.role === "WORKER") {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { area: true }
      });

      if (!user || !user.area) {
        return [];
      }

      return [user.area.nombre as AreaType];
    }

    // Otros roles no pueden escribir por defecto
    return [];
  } catch (error) {
    console.error("Error al obtener áreas escribibles:", error);
    return [];
  }
}

/**
 * Middleware para verificar acceso de LECTURA a un área específica
 * Lanza un error si el usuario no tiene acceso
 */
export async function requireAreaReadAccess(areaName: string): Promise<void> {
  const hasAccess = await hasAreaReadAccess(areaName);

  if (!hasAccess) {
    throw new Error(`Acceso de lectura denegado al área: ${areaName}`);
  }
}

/**
 * Middleware para verificar acceso de ESCRITURA a un área específica
 * Lanza un error si el usuario no tiene acceso
 */
export async function requireAreaWriteAccess(areaName: string): Promise<void> {
  const hasAccess = await hasAreaWriteAccess(areaName);

  if (!hasAccess) {
    throw new Error(`Acceso de escritura denegado al área: ${areaName}`);
  }
}

/**
 * Función de compatibilidad - ahora usa acceso de lectura
 * @deprecated Usar requireAreaReadAccess o requireAreaWriteAccess según el caso
 */
export async function requireAreaAccess(areaName: string): Promise<void> {
  return requireAreaReadAccess(areaName);
}

/**
 * Verifica si el usuario actual es WORKER y tiene un área asignada
 */
export async function isWorkerWithArea(): Promise<{ isWorker: boolean; hasArea: boolean; areaName?: string }> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return { isWorker: false, hasArea: false };
    }

    if (session.user.role !== "WORKER") {
      return { isWorker: false, hasArea: false };
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { area: true }
    });

    if (!user) {
      return { isWorker: true, hasArea: false };
    }

    return {
      isWorker: true,
      hasArea: !!user.area,
      areaName: user.area?.nombre
    };
  } catch (error) {
    console.error("Error al verificar estado de worker:", error);
    return { isWorker: false, hasArea: false };
  }
}

/**
 * Obtiene información del área del usuario actual (solo para WORKERS)
 */
export async function getCurrentWorkerArea(): Promise<{ success: boolean; area?: any; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return { success: false, error: "No autenticado" };
    }

    if (session.user.role !== "WORKER") {
      return { success: false, error: "Solo disponible para usuarios WORKER" };
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { area: true }
    });

    if (!user) {
      return { success: false, error: "Usuario no encontrado" };
    }

    if (!user.area) {
      return { success: false, error: "Usuario sin área asignada" };
    }

    return { success: true, area: user.area };
  } catch (error) {
    console.error("Error al obtener área del worker:", error);
    return { success: false, error: "Error interno del servidor" };
  }
}

/**
 * Filtra las pestañas del dashboard según el acceso del usuario
 */
export async function getAccessibleTabs(): Promise<string[]> {
  try {
    const accessibleAreas = await getUserAccessibleAreas();
    return accessibleAreas;
  } catch (error) {
    console.error("Error al obtener pestañas accesibles:", error);
    return [];
  }
}

/**
 * Verifica si el usuario puede editar datos en un área específica
 * Incluye información adicional sobre el tipo de acceso
 */
export async function canEditAreaData(areaName: string): Promise<{
  canEdit: boolean;
  canView: boolean;
  userArea?: string;
  reason?: string;
}> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return {
        canEdit: false,
        canView: false,
        reason: "Usuario no autenticado"
      };
    }

    const canView = await hasAreaReadAccess(areaName);
    const canEdit = await hasAreaWriteAccess(areaName);

    let userArea: string | undefined;
    if (session.user.role === "WORKER") {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { area: true }
      });
      userArea = user?.area?.nombre;
    }

    let reason: string | undefined;
    if (!canEdit && session.user.role === "WORKER") {
      reason = userArea
        ? `Solo puedes editar datos del área: ${userArea}`
        : "No tienes área asignada";
    }

    return {
      canEdit,
      canView,
      userArea,
      reason
    };
  } catch (error) {
    console.error("Error al verificar permisos de edición:", error);
    return {
      canEdit: false,
      canView: false,
      reason: "Error interno del servidor"
    };
  }
}
