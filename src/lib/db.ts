import { PrismaClient } from '@prisma/client';

// Declaración para el objeto global
declare global {
  var prisma: PrismaClient | undefined;
}

// Exportar una instancia de PrismaClient
export const prisma = global.prisma || new PrismaClient();

// En desarrollo, guardamos la instancia en el objeto global para evitar múltiples instancias
if (process.env.NODE_ENV !== 'production') {
  global.prisma = prisma;
}
