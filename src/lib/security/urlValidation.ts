/**
 * Utilidades para validación segura de URLs de redirección
 * Centraliza la lógica de validación para evitar inconsistencias
 */

/**
 * Valida URLs de redirección en el servidor (NextAuth)
 * @param url - URL a validar
 * @param baseUrl - URL base del servidor
 * @returns URL segura para redirección
 */
export function validateRedirectUrlServer(url: string | undefined, baseUrl: string): string {
  // Si no hay URL, usar la página principal
  if (!url) return "/";
  
  try {
    // Verificar si la URL es relativa (comienza con /)
    if (url.startsWith("/")) {
      // Asegurarse de que no hay caracteres de escape o manipulación
      const sanitizedPath = url.replace(/[^\w\s\-_\/\?\&\=\.]/gi, "");
      
      // Verificar que no intenta escapar del dominio con //
      if (sanitizedPath.includes("//")) return "/";
      
      return sanitizedPath;
    }
    
    // URLs absolutas: verificar que pertenecen al mismo dominio
    const urlObj = new URL(url);
    const baseUrlObj = new URL(baseUrl);
    
    if (urlObj.host === baseUrlObj.host) {
      // URL del mismo dominio: devolver solo el path y query
      return urlObj.pathname + urlObj.search;
    }
    
    // URL de dominio externo: bloquear
    return "/";
  } catch (error) {
    // Si hay un error al analizar la URL, usar la página principal
    return "/";
  }
}

/**
 * Valida URLs de redirección en el cliente
 * @param url - URL a validar
 * @returns URL segura para redirección
 */
export function validateRedirectUrlClient(url: string | null | undefined): string {
  // Si no hay URL, usar la página principal
  if (!url) return "/";
  
  try {
    // Verificar si la URL es relativa (comienza con /)
    if (url.startsWith("/")) {
      // Asegurarse de que no hay caracteres de escape o manipulación
      const sanitizedPath = url.replace(/[^\w\s\-_\/\?\&\=\.]/gi, "");
      
      // Verificar que no intenta escapar del dominio con //
      if (sanitizedPath.includes("//")) return "/";
      
      return sanitizedPath;
    }
    
    // Si la URL es absoluta, verificar que pertenece a nuestro dominio
    const currentHost = window.location.host;
    const urlObj = new URL(url);
    
    if (urlObj.host === currentHost) {
      return urlObj.pathname + urlObj.search;
    }
    
    // URL de dominio externo: bloquear
    return "/";
  } catch (error) {
    // Si hay un error al analizar la URL, usar la página principal
    return "/";
  }
}

/**
 * Información de validación de URL para logging
 * @param url - URL original
 * @param baseUrl - URL base (solo para servidor)
 * @returns Información detallada para logs de seguridad
 */
export function getUrlValidationInfo(url: string, baseUrl?: string) {
  try {
    const urlObj = new URL(url);
    const result: any = {
      attemptedUrl: url,
      attemptedHost: urlObj.host,
      attemptedPath: urlObj.pathname,
      attemptedSearch: urlObj.search
    };
    
    if (baseUrl) {
      const baseUrlObj = new URL(baseUrl);
      result.allowedHost = baseUrlObj.host;
    } else if (typeof window !== 'undefined') {
      result.allowedHost = window.location.host;
    }
    
    return result;
  } catch (error) {
    return {
      attemptedUrl: url,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
