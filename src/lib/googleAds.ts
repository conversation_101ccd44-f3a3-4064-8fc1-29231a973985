'use client';
import { useEffect } from 'react';

// Option 1: Change to named export
export function GoogleAdsInjector() {
  useEffect(() => {
    const adsId = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID!;
    // 1. <PERSON><PERSON><PERSON> y añadir el script asíncrono
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${adsId}`;
    document.head.appendChild(script);

    // 2. Inicializar dataLayer y gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: unknown[]) {
      window.dataLayer?.push(args);
    }
    gtag('js', new Date());
    gtag('config', adsId);

    // 3. Limpieza al desmontar
    return () => {
      document.head.removeChild(script);
    };
  }, []);

  return null; // No renderiza nada en el DOM
}
