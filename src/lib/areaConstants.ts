// Definición de áreas disponibles
export const AREAS = {
  VENTAS: "ventas",
  COMPRAS: "compras", 
  LOGISTICA: "logistica",
  CONTABILIDAD: "contabilidad",
  OPERACIONES: "operaciones",
  LEGAL: "legal",
  PLANEACION: "planeacion",
  FINANZAS: "finanzas",
} as const;

// Tipo para las áreas
export type AreaType = typeof AREAS[keyof typeof AREAS];

// Mapeo de áreas a nombres para mostrar
export const AREA_DISPLAY_NAMES: Record<AreaType, string> = {
  [AREAS.VENTAS]: "Ventas",
  [AREAS.COMPRAS]: "Compras",
  [AREAS.LOGISTICA]: "Logística", 
  [AREAS.CONTABILIDAD]: "Contabilidad",
  [AREAS.OPERACIONES]: "Operaciones",
  [AREAS.LEGAL]: "Legal",
  [AREAS.PLANEACION]: "Planeación",
  [AREAS.FINANZAS]: "Finanzas",
};

// Mapeo de áreas a iconos (nombres de iconos de Lucide React)
export const AREA_ICONS: Record<AreaType, string> = {
  [AREAS.VENTAS]: "ShoppingCart",
  [AREAS.COMPRAS]: "Package",
  [AREAS.LOGISTICA]: "Truck",
  [AREAS.CONTABILIDAD]: "Calculator", 
  [AREAS.OPERACIONES]: "Settings",
  [AREAS.LEGAL]: "Scale",
  [AREAS.PLANEACION]: "Target",
  [AREAS.FINANZAS]: "Banknote",
};

// Mapeo de áreas a colores
export const AREA_COLORS: Record<AreaType, string> = {
  [AREAS.VENTAS]: "#3b82f6", // blue-500
  [AREAS.COMPRAS]: "#10b981", // emerald-500
  [AREAS.LOGISTICA]: "#f59e0b", // amber-500
  [AREAS.CONTABILIDAD]: "#8b5cf6", // violet-500
  [AREAS.OPERACIONES]: "#6366f1", // indigo-500
  [AREAS.LEGAL]: "#ef4444", // red-500
  [AREAS.PLANEACION]: "#06b6d4", // cyan-500
  [AREAS.FINANZAS]: "#059669", // emerald-600
};

// Orden de visualización de las áreas
export const AREA_ORDER: AreaType[] = [
  AREAS.VENTAS,
  AREAS.COMPRAS,
  AREAS.LOGISTICA,
  AREAS.CONTABILIDAD,
  AREAS.OPERACIONES,
  AREAS.LEGAL,
  AREAS.PLANEACION,
  AREAS.FINANZAS,
];

// Función para validar si un string es un área válida
export function isValidArea(area: string): area is AreaType {
  return Object.values(AREAS).includes(area as AreaType);
}

// Función para obtener el nombre para mostrar de un área
export function getAreaDisplayName(area: AreaType): string {
  return AREA_DISPLAY_NAMES[area] || area;
}

// Función para obtener el icono de un área
export function getAreaIcon(area: AreaType): string {
  return AREA_ICONS[area] || "Circle";
}

// Función para obtener el color de un área
export function getAreaColor(area: AreaType): string {
  return AREA_COLORS[area] || "#6b7280"; // gray-500 por defecto
}

// Función para obtener todas las áreas en orden
export function getAllAreasInOrder(): AreaType[] {
  return AREA_ORDER;
}

// Función para obtener información completa de un área
export function getAreaInfo(area: AreaType) {
  return {
    id: area,
    name: area,
    displayName: getAreaDisplayName(area),
    icon: getAreaIcon(area),
    color: getAreaColor(area),
  };
}

// Función para obtener información de todas las áreas
export function getAllAreasInfo() {
  return AREA_ORDER.map(area => getAreaInfo(area));
}
