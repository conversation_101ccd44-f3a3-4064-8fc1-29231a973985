"use server";

import { getSession } from "@/lib/auth";
import { isValidRoleSync } from "./roleUtils";
import { ROLE_HIERARCHY, UserRole } from "./roleConstants";

// Función para validar si un valor es un rol válido (versión server action)
export async function isValidRole(role: string): Promise<boolean> {
  return isValidRoleSync(role);
}

/**
 * Verifica si el usuario tiene un rol específico
 */
export async function hasRole(role: UserRole): Promise<boolean> {
  const session = await getSession();
  if (!session?.user?.role) return false;

  // Validar que el rol del usuario es válido
  const isValid = await isValidRole(session.user.role);
  if (!isValid) return false;

  return session.user.role === role;
}

/**
 * Verifica si el usuario tiene al menos el rol especificado o uno superior
 */
export async function hasRoleOrHigher(minimumRole: UserRole): Promise<boolean> {
  const session = await getSession();
  if (!session?.user?.role) return false;

  // Validar que el rol del usuario es válido
  const isValid = await isValidRole(session.user.role);
  if (!isValid) return false;

  const userRoleIndex = ROLE_HIERARCHY.indexOf(session.user.role as UserRole);
  const requiredRoleIndex = ROLE_HIERARCHY.indexOf(minimumRole);

  return userRoleIndex >= requiredRoleIndex;
}

/**
 * Verifica si el usuario es administrador (ADMIN o SUPER_ADMIN)
 */
export async function isAdmin(): Promise<boolean> {
  return hasRoleOrHigher("ADMIN");
}

/**
 * Verifica si el usuario es trabajador o tiene un rol superior
 */
export async function isWorkerOrHigher(): Promise<boolean> {
  return hasRoleOrHigher("WORKER");
}

/**
 * Verifica si el usuario es exactamente un trabajador
 */
export async function isWorker(): Promise<boolean> {
  return hasRole("WORKER");
}

/**
 * Verifica si el usuario es super administrador
 */
export async function isSuperAdmin(): Promise<boolean> {
  return hasRole("SUPER_ADMIN");
}

/**
 * Middleware para proteger rutas que requieren un rol específico
 * Redirige a la página de inicio de sesión si el usuario no está autenticado
 * o a la página de acceso denegado si no tiene el rol requerido
 */
export async function requireRole(role: UserRole) {
  const session = await getSession();

  if (!session?.user) {
    throw new Error("No autenticado");
  }

  const hasRequiredRole = await hasRoleOrHigher(role);

  if (!hasRequiredRole) {
    throw new Error("Acceso denegado");
  }

  return session.user;
}
