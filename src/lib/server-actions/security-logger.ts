'use server';

import { securityLogger } from '@/lib/loggers/logger';

// Server Action para registrar eventos de seguridad
export async function logSecurityEvent(level: 'info' | 'warn' | 'error', message: string, metadata: any) {
  try {
    // Validar que el nivel sea válido
    if (!['info', 'warn', 'error'].includes(level)) {
      throw new Error('Nivel de log inválido');
    }
    
    // Registrar el evento usando el logger del servidor
    securityLogger[level](message, metadata);
    
    return { success: true };
  } catch (error) {
    console.error('Error al procesar el registro de seguridad:', error);
    return { error: 'Error interno del servidor' };
  }
}