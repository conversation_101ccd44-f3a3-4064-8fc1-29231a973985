import { PrismaClient } from '@prisma/client';

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
// Learn more: https://pris.ly/d/help/next-js-best-practices

// Declaración más robusta para evitar errores de tipado
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Función para crear una nueva instancia de PrismaClient
function createPrismaClient() {
  console.log('Creando nueva instancia de PrismaClient');
  return new PrismaClient({
    log: process.env.NODE_ENV === 'production' ? ['error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });
}

// Asegurarnos de que siempre tenemos una instancia de PrismaClient
export const prisma = globalForPrisma.prisma || createPrismaClient();

// Guardar en el objeto global en desarrollo para reutilización
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// Función auxiliar para verificar si prisma está disponible
export function isPrismaAvailable() {
  return true; // Siempre devolvemos true ya que ahora garantizamos que prisma está disponible
}
