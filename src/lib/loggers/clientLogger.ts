'use client';

import { logSecurityEvent } from '@/lib/server-actions/security-logger';

class ClientLogger {
  private component: string;

  constructor(component: string) {
    this.component = component;
  }

  // Método para enviar logs al servidor
  private async sendLog(level: 'info' | 'warn' | 'error', message: string, meta?: any) {
    try {
      // Añadir información del navegador
      const enrichedMeta = {
        ...meta,
        component: this.component,
        userAgent: navigator.userAgent,
        origin: window.location.origin,
        path: window.location.pathname
      };

      // Usar Server Action en lugar de fetch
      await logSecurityEvent(level, message, enrichedMeta);
    } catch (error) {
      // En caso de error, al menos lo mostramos en consola
      console.error('Error sending log to server:', error);
    }
  }

  info(message: string, meta?: any) {
    this.sendLog('info', message, meta);
  }

  warn(message: string, meta?: any) {
    this.sendLog('warn', message, meta);
  }

  error(message: string, meta?: any) {
    this.sendLog('error', message, meta);
  }
}

// Exportamos instancias preconfiguradas para diferentes componentes
export const securityLogger = new ClientLogger('security');
export const errorLogger = new ClientLogger('error');
export const logger = new ClientLogger('app');
