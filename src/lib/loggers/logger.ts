import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Primero, asegurémonos de tener un lugar donde guardar nuestros registros
// Si no existe la carpeta 'logs', la creamos automáticamente
const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
  console.log('Carpeta de logs creada');
}

// Esta es nuestra receta base para todos los loggers
// Así mantenemos un formato consistente en todos nuestros registros
const commonConfig = {
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss' // Fecha y hora en formato humano
    }),
    winston.format.json() // Formato JSON para fácil procesamiento
  ),
  defaultMeta: { service: 'cassiopeia-petroliferos' } // Identificamos nuestro servicio
};

// Nuestro logger principal - el todoterreno para información general
export const logger = winston.createLogger({
  ...commonConfig,
  level: 'info', // Captura info, warnings y errores
  transports: [
    // Guardamos todo en un archivo para referencia futura
    new winston.transports.File({ filename: path.join(logDir, 'combined.log') }),
    // También mostramos en consola durante desarrollo para facilitar debugging
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(), // Colores para distinguir niveles
        winston.format.simple()    // Formato legible en consola
      ),
      silent: process.env.NODE_ENV === 'production' // En producción, mantenemos la consola limpia
    })
  ]
});

// Este logger es nuestro detector de problemas - solo registra errores
export const errorLogger = winston.createLogger({
  ...commonConfig,
  level: 'error', // Solo captura errores graves
  transports: [
    // Archivo separado para poder revisar solo los errores cuando sea necesario
    new winston.transports.File({ filename: path.join(logDir, 'error.log') }),
    // Consola para alertas inmediatas durante desarrollo
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(), // Errores en rojo para que destaquen
        winston.format.simple()
      ),
      silent: process.env.NODE_ENV === 'production'
    })
  ]
});

// Nuestro vigilante de seguridad - registra intentos de acceso y eventos de seguridad
export const securityLogger = winston.createLogger({
  ...commonConfig,
  level: 'info',
  // Añadimos etiqueta de seguridad para filtrar fácilmente estos eventos
  defaultMeta: { ...commonConfig.defaultMeta, component: 'security' },
  transports: [
    // Archivo dedicado para auditorías de seguridad
    new winston.transports.File({ filename: path.join(logDir, 'security.log') }),
    // También en consola durante desarrollo
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
      silent: process.env.NODE_ENV === 'production'
    })
  ]
});

// Nuestra red de seguridad - captura errores que de otro modo se perderían
export function setupGlobalErrorLogging() {
  // Para esos errores inesperados que rompen todo
  process.on('uncaughtException', (error) => {
    errorLogger.error('¡Ups! Error no capturado en la aplicación', { 
      error: error.stack || error.toString() 
    });
    console.error('🔥 Error no capturado:', error);
  });

  // Para promesas que fallan silenciosamente
  process.on('unhandledRejection', (reason) => {
    errorLogger.error('¡Atención! Promesa rechazada no manejada', { 
      reason: reason instanceof Error ? reason.stack : String(reason) 
    });
    console.error('⚠️ Promesa rechazada no manejada:', reason);
  });
}
