import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/authOptions";
import { redirect } from "next/navigation";

// Función para obtener la sesión del servidor
export async function getSession() {
  return await getServerSession(authOptions);
}

// Función para verificar si el usuario está autenticado en el servidor
export async function getCurrentUser() {
  const session = await getSession();
  return session?.user;
}

// Middleware para proteger rutas en el servidor
export async function requireAuth() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/signin");
  }
  
  return user;
}
