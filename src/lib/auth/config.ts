/**
 * Configuración de autenticación centralizada
 */

// Verificar que las variables de entorno requeridas estén presentes
const requiredEnvVars = {
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
} as const;

// Validar variables de entorno
for (const [key, value] of Object.entries(requiredEnvVars)) {
  if (!value) {
    throw new Error(`Variable de entorno requerida no encontrada: ${key}`);
  }
}

export const authConfig = {
  // URL base de la aplicación
  baseUrl: process.env.NEXTAUTH_URL!,
  
  // Secret para firmar tokens JWT
  secret: process.env.NEXTAUTH_SECRET!,
  
  // Configuración de dominio para cookies
  cookieDomain: process.env.NODE_ENV === "production" 
    ? ".cassiopeiamx.com" 
    : undefined,
    
  // Configuración de seguridad
  isProduction: process.env.NODE_ENV === "production",
  
  // Duración de sesión (30 días)
  sessionMaxAge: 30 * 24 * 60 * 60,
  
  // Frecuencia de actualización de sesión (24 horas)
  sessionUpdateAge: 24 * 60 * 60,
} as const;

export default authConfig;
