import CredentialsProvider from "next-auth/providers/credentials";
import { prisma } from "@/lib/db";
import bcrypt from "bcryptjs";
import { NextAuthOptions } from "next-auth";
import { isValidRoleSync, getDefaultRole } from "@/lib/roleUtils";
import { UserRole } from "@/lib/roleConstants";
import { securityLogger } from "@/lib/loggers/logger";
import { validateRedirectUrlServer, getUrlValidationInfo } from "@/lib/security/urlValidation";

// Función para validar URLs de redirección en el servidor
const validateRedirectUrl = (url: string | undefined, baseUrl: string): string => {
  const safeUrl = validateRedirectUrlServer(url, baseUrl);
  
  // Si la URL fue bloqueada (no es la original), registrar el evento
  if (url && safeUrl === "/" && url !== "/") {
    try {
      const urlInfo = getUrlValidationInfo(url, baseUrl);
      
      // Solo loggear URLs de dominios externos (verdaderos intentos maliciosos)
      if (url.startsWith("http") && !url.startsWith(baseUrl)) {
        securityLogger.warn("Intento de redirección a dominio externo bloqueado", urlInfo);
      }
    } catch (error) {
      securityLogger.error("Error al validar URL de redirección en el servidor", {
        url,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
  
  return safeUrl;
};

export const authOptions: NextAuthOptions = {
  // Nota: Cuando se usa CredentialsProvider, es mejor no usar el adaptador
  // ya que puede causar problemas con la estrategia JWT
  // adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "Credenciales",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Contraseña", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          securityLogger.warn("Intento de login sin credenciales completas", {
            email: credentials?.email || "no proporcionado"
          });
          return null;
        }

        try {
          // Acceder al modelo User a través de prisma
          const user = await prisma.user.findUnique({
            where: { email: credentials.email }
          });

          if (!user || !user.password) {
            securityLogger.warn("Intento de login con usuario inexistente o sin contraseña", {
              email: credentials.email
            });
            console.log("Usuario no encontrado o sin contraseña");
            return null;
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            securityLogger.warn("Intento de login con contraseña inválida", {
              userId: user.id,
              email: user.email
            });
            console.log("Contraseña inválida");
            return null;
          }

          securityLogger.info("Login exitoso", {
            userId: user.id,
            email: user.email
          });
          console.log("Usuario autenticado correctamente:", { id: user.id, email: user.email });

          return {
            id: user.id,
            email: user.email,
            name: user.name,
          };
        } catch (error) {
          securityLogger.error("Error en proceso de autenticación", {
            email: credentials.email,
            error: error instanceof Error ? error.message : String(error)
          });
          console.error("Error en authorize:", error);
          return null;
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 días
    updateAge: 24 * 60 * 60, // Actualizar cada 24 horas
  },
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production"
        ? "__Secure-next-auth.session-token"
        : "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        domain: process.env.NODE_ENV === "production"
          ? ".cassiopeiamx.com"
          : undefined
      }
    },
    callbackUrl: {
      name: process.env.NODE_ENV === "production"
        ? "__Secure-next-auth.callback-url"
        : "next-auth.callback-url",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        domain: process.env.NODE_ENV === "production"
          ? ".cassiopeiamx.com"
          : undefined
      }
    },
    csrfToken: {
      name: process.env.NODE_ENV === "production"
        ? "__Host-next-auth.csrf-token"
        : "next-auth.csrf-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production"
      }
    }
  },
  pages: {
    signIn: "/auth/signin",
    // signUp: "/auth/signup", // Esta propiedad no está en el tipo PagesOptions
    error: "/auth/error",
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      // Si es un nuevo login
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;

        // Obtener el rol del usuario desde la base de datos
        try {
          // Usar una consulta SQL directa para obtener el rol
          const dbUser = await prisma.$queryRaw`SELECT role FROM User WHERE id = ${user.id}`;

          if (dbUser && Array.isArray(dbUser) && dbUser.length > 0) {
            const userRole = dbUser[0].role as string;

            // Validar que el rol es válido (usando la función síncrona)
            if (isValidRoleSync(userRole)) {
              token.role = userRole as UserRole;
            } else {
              // Si el rol no es válido, asignar el rol por defecto
              console.warn(`Rol inválido encontrado: ${userRole}. Asignando rol por defecto.`);
              token.role = getDefaultRole();
            }
          } else {
            // Si no se encuentra el rol, asignar el rol por defecto
            token.role = getDefaultRole();
          }
        } catch (error) {
          console.error("Error al obtener el rol del usuario:", error);
          // En caso de error, asignar el rol por defecto
          token.role = getDefaultRole();
        }
      }

      // Si es una actualización de sesión
      if (trigger === "update" && session) {
        // Actualizar el token con los nuevos datos de la sesión
        token.name = session.name || token.name;
        token.email = session.email || token.email;
      }

      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;

        // Validar que el rol es válido antes de asignarlo (usando la función síncrona)
        const role = token.role as string;
        if (isValidRoleSync(role)) {
          session.user.role = role as UserRole;
        } else {
          // Si el rol no es válido, asignar el rol por defecto
          console.warn(`Rol inválido encontrado en token: ${role}. Asignando rol por defecto.`);
          session.user.role = getDefaultRole();
        }
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Validar y sanitizar la URL de redirección
      const safeUrl = validateRedirectUrl(url, baseUrl);

      // Asegurarse de que la URL es relativa o pertenece al mismo dominio
      if (safeUrl.startsWith("/")) {
        return `${baseUrl}${safeUrl}`;
      }

      // Si llegamos aquí, redirigir a la página principal
      return baseUrl;
    },
  },
  debug: process.env.NODE_ENV === 'production',
};
