// Utilidades para roles que no necesitan ser server actions

import { isValidRole as zodIsValidRole, getDefaultRole as zodGetDefaultRole } from "./schemas/roleSchema";
import { UserRole } from "./roleConstants";

/**
 * Verifica si un valor es un rol válido (función síncrona)
 * Usa Zod para validación
 */
export function isValidRoleSync(role: string): role is UserRole {
  return zodIsValidRole(role);
}

/**
 * Obtiene el rol por defecto
 */
export function getDefaultRole(): UserRole {
  return zodGetDefaultRole();
}

/**
 * Verifica si un rol es igual o superior a otro en la jerarquía
 */
export function isRoleEqualOrHigher(userRole: UserRole, minimumRole: UserRole): boolean {
  const { ROLE_HIERARCHY } = require('./roleConstants');
  const userRoleIndex = ROLE_HIERARCHY.indexOf(userRole);
  const requiredRoleIndex = ROLE_HIERARCHY.indexOf(minimumRole);

  return userRoleIndex >= requiredRoleIndex;
}
