import { z } from 'zod';
import { USER_ROLES, UserRole } from '../roleConstants';

// Crear un array con los valores de USER_ROLES
const roleValues = Object.values(USER_ROLES);

// Esquema para validar roles
export const roleSchema = z.enum(roleValues as [string, ...string[]]);

// Usar el tipo UserRole de roleConstants
export type RoleType = UserRole;

/**
 * Valida un rol usando Zod
 * @param role El rol a validar
 * @returns Un objeto con el resultado de la validación
 */
export function validateRole(role: unknown) {
  const result = roleSchema.safeParse(role);
  return {
    success: result.success,
    value: result.success ? result.data : undefined,
    error: !result.success ? result.error : undefined
  };
}

/**
 * Verifica si un valor es un rol válido
 * @param role El rol a verificar
 * @returns true si el rol es válido, false en caso contrario
 */
export function isValidRole(role: unknown): boolean {
  return validate<PERSON>ole(role).success;
}

/**
 * Obtiene el rol por defecto
 * @returns El rol por defecto (USER)
 */
export function getDefaultRole(): RoleType {
  return 'USER';
}
