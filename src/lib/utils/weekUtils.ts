/**
 * Utilidades para manejo de semanas del año
 * Las semanas van de viernes a jueves para alinearse con el ciclo de negocio
 */

export interface WeekInfo {
  year: number;
  weekNumber: number;
  startDate: Date;
  endDate: Date;
  label: string; // ej: "Semana 27/2025 (30/06/2025 - 06/07/2025)"
}

/**
 * Obtiene el número de semana para una fecha dada (viernes a jueves)
 * @param date Fecha
 * @returns Número de semana (1-53)
 */
export function getISOWeekNumber(date: Date): number {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay(); // 0=domingo, 1=lunes, ..., 6=sábado

  // Encontrar el viernes de la semana que contiene esta fecha
  let fridayOfWeek = new Date(d);

  if (dayNum >= 0 && dayNum <= 4) {
    // Si es domingo, lunes, martes, miércoles o jueves, ir al viernes anterior
    const daysToSubtract = dayNum === 0 ? 2 : dayNum + 2;
    fridayOfWeek.setUTCDate(d.getUTCDate() - daysToSubtract);
  } else {
    // Si es viernes o sábado, ir al viernes de esa semana
    const daysToSubtract = dayNum - 5;
    fridayOfWeek.setUTCDate(d.getUTCDate() - daysToSubtract);
  }

  // Determinar el año de la semana
  const weekYear = fridayOfWeek.getUTCFullYear();

  // Encontrar el primer viernes del año de la semana
  let firstFriday: Date;

  if (weekYear === 2025) {
    // Para 2025, usar la fecha específica
    firstFriday = new Date(Date.UTC(2024, 11, 27)); // 27 de diciembre de 2024
  } else {
    // Para otros años, usar lógica estándar
    const jan1 = new Date(Date.UTC(weekYear, 0, 1));
    firstFriday = new Date(jan1);
    const firstDayOfWeek = jan1.getUTCDay();
    const daysToFirstFriday = firstDayOfWeek <= 5 ? 5 - firstDayOfWeek : 12 - firstDayOfWeek;
    firstFriday.setUTCDate(jan1.getUTCDate() + daysToFirstFriday);
  }

  // Calcular la diferencia en días y convertir a semanas
  const diffTime = fridayOfWeek.getTime() - firstFriday.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  return Math.floor(diffDays / 7) + 1;
}

/**
 * Obtiene el año para una fecha dada (basado en semanas viernes-jueves)
 * @param date Fecha
 * @returns Año
 */
export function getISOYear(date: Date): number {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay();

  // Encontrar el viernes de la semana que contiene esta fecha
  let fridayOfWeek = new Date(d);

  if (dayNum >= 0 && dayNum <= 4) {
    // Si es domingo, lunes, martes, miércoles o jueves, ir al viernes anterior
    const daysToSubtract = dayNum === 0 ? 2 : dayNum + 2;
    fridayOfWeek.setUTCDate(d.getUTCDate() - daysToSubtract);
  } else {
    // Si es viernes o sábado, ir al viernes de esa semana
    const daysToSubtract = dayNum - 5;
    fridayOfWeek.setUTCDate(d.getUTCDate() - daysToSubtract);
  }

  // Para fechas en diciembre de 2024 que pertenecen a semanas de 2025
  if (fridayOfWeek.getUTCFullYear() === 2024 && fridayOfWeek.getUTCMonth() === 11 && fridayOfWeek.getUTCDate() >= 27) {
    return 2025;
  }

  // El año de la semana es el año del viernes
  return fridayOfWeek.getUTCFullYear();
}

/**
 * Obtiene las fechas de inicio y fin de una semana específica (viernes a jueves)
 * @param year Año
 * @param weekNumber Número de semana
 * @returns Objeto con fechas de inicio y fin
 */
export function getWeekDates(year: number, weekNumber: number): { startDate: Date; endDate: Date } {
  // Validar parámetros de entrada
  if (!year || year < 1900 || year > 2100) {
    throw new Error(`Año inválido: ${year}`);
  }

  if (!weekNumber || weekNumber < 1 || weekNumber > 53) {
    throw new Error(`Número de semana inválido: ${weekNumber}`);
  }

  // Para el año 2025: S1 debe comenzar el viernes 27 de diciembre de 2024
  // para que S27 sea 27 JUN - 03 JUL de 2025
  // Para otros años, usar lógica similar ajustada

  let firstFridayOfYear: Date;

  if (year === 2025) {
    // Fecha específica para 2025
    firstFridayOfYear = new Date(2024, 11, 27); // 27 de diciembre de 2024
  } else {
    // Para otros años, usar lógica estándar
    const jan1 = new Date(year, 0, 1);
    firstFridayOfYear = new Date(jan1);
    const dayOfWeek = jan1.getDay(); // 0=domingo, 1=lunes, ..., 6=sábado

    // Calcular días hasta el primer viernes
    let daysToFirstFriday;
    if (dayOfWeek <= 5) {
      // Si el 1 de enero es domingo a viernes
      daysToFirstFriday = 5 - dayOfWeek;
    } else {
      // Si el 1 de enero es sábado
      daysToFirstFriday = 12 - dayOfWeek;
    }

    firstFridayOfYear.setDate(jan1.getDate() + daysToFirstFriday);
  }

  // Calcular la fecha de inicio de la semana solicitada (viernes)
  const startDate = new Date(firstFridayOfYear);
  startDate.setDate(firstFridayOfYear.getDate() + (weekNumber - 1) * 7);

  // La fecha de fin es 6 días después del inicio (jueves)
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);

  // Validar que las fechas generadas sean válidas
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    throw new Error(`Fechas inválidas generadas para año ${year}, semana ${weekNumber}`);
  }

  return { startDate, endDate };
}

/**
 * Obtiene información completa de la semana actual
 * @returns Información de la semana actual
 */
export function getCurrentWeekInfo(): WeekInfo {
  const now = new Date();
  const year = getISOYear(now);
  const weekNumber = getISOWeekNumber(now);
  const { startDate, endDate } = getWeekDates(year, weekNumber);
  
  return {
    year,
    weekNumber,
    startDate,
    endDate,
    label: formatWeekLabel(year, weekNumber, startDate, endDate)
  };
}

/**
 * Obtiene información de una semana específica
 * @param year Año
 * @param weekNumber Número de semana
 * @returns Información de la semana
 */
export function getWeekInfo(year: number, weekNumber: number): WeekInfo {
  try {
    const { startDate, endDate } = getWeekDates(year, weekNumber);

    return {
      year,
      weekNumber,
      startDate,
      endDate,
      label: formatWeekLabel(year, weekNumber, startDate, endDate)
    };
  } catch (error) {
    // Si hay un error, devolver información de la semana 1 como fallback
    console.warn(`Error al obtener semana ${weekNumber}/${year}, usando semana 1 como fallback:`, error);
    const { startDate, endDate } = getWeekDates(year, 1);

    return {
      year,
      weekNumber: 1,
      startDate,
      endDate,
      label: formatWeekLabel(year, 1, startDate, endDate)
    };
  }
}

/**
 * Formatea el label de una semana
 * @param year Año
 * @param weekNumber Número de semana
 * @param startDate Fecha de inicio
 * @param endDate Fecha de fin
 * @returns Label formateado
 */
export function formatWeekLabel(year: number, weekNumber: number, startDate: Date, endDate: Date): string {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };
  
  return `Semana ${weekNumber}/${year} (${formatDate(startDate)} - ${formatDate(endDate)})`;
}

/**
 * Obtiene las últimas N semanas incluyendo la actual
 * @param count Número de semanas a obtener
 * @returns Array de información de semanas
 */
export function getLastWeeks(count: number): WeekInfo[] {
  const weeks: WeekInfo[] = [];
  const currentWeek = getCurrentWeekInfo();

  for (let i = count - 1; i >= 0; i--) {
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() - (i * 7));

    const year = getISOYear(targetDate);
    const weekNumber = getISOWeekNumber(targetDate);
    const weekInfo = getWeekInfo(year, weekNumber);

    weeks.push(weekInfo);
  }

  return weeks;
}

/**
 * Verifica si se puede agregar datos para una semana específica
 * Solo se pueden agregar datos después de que la semana haya terminado completamente
 * @param year Año de la semana
 * @param weekNumber Número de semana
 * @returns true si se puede agregar datos, false si no
 */
export function canAddDataForWeek(year: number, weekNumber: number): boolean {
  try {
    const { endDate } = getWeekDates(year, weekNumber);
    const now = new Date();

    // Normalizar las fechas para comparar solo día/mes/año (sin horas)
    const endDateNormalized = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());
    const nowNormalized = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Solo se puede agregar datos si la fecha actual es DESPUÉS del último día de la semana
    return nowNormalized > endDateNormalized;
  } catch (error) {
    console.error(`Error al verificar si se puede agregar datos para semana ${weekNumber}/${year}:`, error);
    return false;
  }
}

/**
 * Obtiene el mensaje de error cuando no se puede agregar datos para una semana
 * @param year Año de la semana
 * @param weekNumber Número de semana
 * @returns Mensaje de error explicativo
 */
export function getWeekDataRestrictionMessage(year: number, weekNumber: number): string {
  try {
    const { startDate, endDate } = getWeekDates(year, weekNumber);
    const formatDate = (date: Date) => {
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    };

    return `No se pueden agregar datos para la semana ${weekNumber}/${year} (${formatDate(startDate)} - ${formatDate(endDate)}) hasta que haya terminado completamente. Podrás agregar datos a partir del ${formatDate(new Date(endDate.getTime() + 24 * 60 * 60 * 1000))}.`;
  } catch (error) {
    return `No se pueden agregar datos para la semana ${weekNumber}/${year} hasta que haya terminado completamente.`;
  }
}

/**
 * Obtiene la información de la última semana completada (para la cual se pueden agregar datos)
 * @returns Información de la última semana completada
 */
export function getLastCompletedWeekInfo(): WeekInfo {
  const currentWeek = getCurrentWeekInfo();

  // Si se pueden agregar datos para la semana actual, devolverla
  if (canAddDataForWeek(currentWeek.year, currentWeek.weekNumber)) {
    return currentWeek;
  }

  // Si no, buscar la semana anterior que esté completada
  let weekToCheck = currentWeek.weekNumber - 1;
  let yearToCheck = currentWeek.year;

  // Si estamos en la semana 1, ir al año anterior
  if (weekToCheck < 1) {
    weekToCheck = 52; // Asumir 52 semanas por año
    yearToCheck = currentWeek.year - 1;
  }

  // Verificar si se pueden agregar datos para esta semana
  while (!canAddDataForWeek(yearToCheck, weekToCheck) && weekToCheck > 0) {
    weekToCheck--;
    if (weekToCheck < 1) {
      weekToCheck = 52;
      yearToCheck--;
    }
  }

  try {
    return getWeekInfo(yearToCheck, weekToCheck);
  } catch (error) {
    // Si hay error, devolver la semana actual como fallback
    console.warn('Error al obtener última semana completada, usando semana actual:', error);
    return currentWeek;
  }
}

/**
 * Formatea las fechas de una semana para mostrar en gráficas
 * @param year Año de la semana
 * @param weekNumber Número de semana
 * @returns String formateado como "27 JUN - 03 JUL"
 */
export function formatWeekDatesForChart(year: number, weekNumber: number): string {
  try {
    const { startDate, endDate } = getWeekDates(year, weekNumber);

    const formatDate = (date: Date) => {
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: 'short'
      }).toUpperCase();
    };

    const startFormatted = formatDate(startDate);
    const endFormatted = formatDate(endDate);

    return `${startFormatted} - ${endFormatted}`;
  } catch (error) {
    console.warn(`Error al formatear fechas para semana ${weekNumber}/${year}:`, error);
    return `S${weekNumber}`;
  }
}

/**
 * Valida si una fecha está dentro de una semana específica
 * @param date Fecha a validar
 * @param year Año de la semana
 * @param weekNumber Número de semana
 * @returns true si la fecha está en la semana
 */
export function isDateInWeek(date: Date, year: number, weekNumber: number): boolean {
  const { startDate, endDate } = getWeekDates(year, weekNumber);
  return date >= startDate && date <= endDate;
}
