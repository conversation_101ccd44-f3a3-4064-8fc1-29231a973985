interface Coordinates {
  lat: number;
  lon: number;
}

interface Distribuidora {
  nombre: string;
  coordinates: Coordinates;
}

export const DISTRIBUIDORAS: Record<string, Distribuidora[]> = {
  'Diesel': [
    { nombre: 'AXFALTEC', coordinates: { lat: 18.4354445, lon: -93.2003204 } },
    { nombre: 'TUXPAN', coordinates: { lat: 20.951914792440803, lon: -97.3368904753723 } },
    { nombre: 'VOPAK', coordinates: { lat: 19.2154227, lon: -96.1377359 } },
    { nombre: 'VALERO', coordinates: { lat: 19.2452536, lon: -96.1734283 } }
  ],
  'Regular': [
    { nombre: 'TUXPAN', coordinates: { lat: 20.951914792440803, lon: -97.3368904753723 } },
    { nombre: 'VALERO', coordinates: { lat: 19.2452536, lon: -96.1734283 } },
    { nombre: 'TIZAYUCA', coordinates: { lat: 19.8221027, lon: -98.9606588 } }
  ],
  'Premium': [
    { nombre: 'TUXPAN', coordinates: { lat: 20.951914792440803, lon: -97.3368904753723 } },
    { nombre: 'VALERO', coordinates: { lat: 19.2452536, lon: -96.1734283 } },
    { nombre: 'TIZAYUCA', coordinates: { lat: 19.8221027, lon: -98.9606588 } }
  ]
};