import NextAuth from "next-auth";

declare module "next-auth" {
  /**
   * Extender la interfaz Session para incluir el ID del usuario y su rol
   */
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: string;
    };
  }

  /**
   * Extender la interfaz User para incluir campos adicionales
   */
  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string;
  }
}

declare module "next-auth/jwt" {
  /**
   * Extender la interfaz JWT para incluir el ID del usuario y su rol
   */
  interface JWT {
    id: string;
    role?: string;
  }
}
