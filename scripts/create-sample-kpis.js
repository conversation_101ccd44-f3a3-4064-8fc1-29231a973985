const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSampleKpis() {
  try {
    console.log('🔄 Creando KPIs de muestra para demostración...\n');

    // Obtener usuario worker
    const workerUser = await prisma.user.findFirst({
      where: { role: 'WORKER' }
    });

    if (!workerUser) {
      console.log('❌ No se encontró usuario WORKER');
      return;
    }

    // Datos de muestra para las últimas 6 semanas
    const sampleKpis = [
      {
        year: 2025,
        weekNumber: 23,
        weekStartDate: new Date('2025-06-02'),
        weekEndDate: new Date('2025-06-08'),
        volumenTotalLitros: 2200000,
        crecimientoMensual: 5.2,
        margenBrutoPorLitro: 2.15,
        tasaRetencionClientes: 85.5,
        cumplimientoObjetivo: 92.3,
        desviacionVentas: -3.1,
        cicloPromedioCierre: 14,
        clientesActivosMensuales: 1650
      },
      {
        year: 2025,
        weekNumber: 24,
        weekStartDate: new Date('2025-06-09'),
        weekEndDate: new Date('2025-06-15'),
        volumenTotalLitros: 2350000,
        crecimientoMensual: 6.8,
        margenBrutoPorLitro: 2.25,
        tasaRetencionClientes: 86.2,
        cumplimientoObjetivo: 93.1,
        desviacionVentas: -2.8,
        cicloPromedioCierre: 13,
        clientesActivosMensuales: 1720
      },
      {
        year: 2025,
        weekNumber: 25,
        weekStartDate: new Date('2025-06-16'),
        weekEndDate: new Date('2025-06-22'),
        volumenTotalLitros: 2280000,
        crecimientoMensual: -3.0,
        margenBrutoPorLitro: 2.18,
        tasaRetencionClientes: 84.8,
        cumplimientoObjetivo: 91.7,
        desviacionVentas: -4.2,
        cicloPromedioCierre: 15,
        clientesActivosMensuales: 1680
      },
      {
        year: 2025,
        weekNumber: 26,
        weekStartDate: new Date('2025-06-23'),
        weekEndDate: new Date('2025-06-29'),
        volumenTotalLitros: 2420000,
        crecimientoMensual: 6.1,
        margenBrutoPorLitro: 2.32,
        tasaRetencionClientes: 87.1,
        cumplimientoObjetivo: 94.2,
        desviacionVentas: -1.9,
        cicloPromedioCierre: 11,
        clientesActivosMensuales: 1780
      },
      {
        year: 2025,
        weekNumber: 27,
        weekStartDate: new Date('2025-06-30'),
        weekEndDate: new Date('2025-07-06'),
        volumenTotalLitros: 2380000,
        crecimientoMensual: -1.7,
        margenBrutoPorLitro: 2.28,
        tasaRetencionClientes: 86.8,
        cumplimientoObjetivo: 93.8,
        desviacionVentas: -3.5,
        cicloPromedioCierre: 13,
        clientesActivosMensuales: 1820
      }
    ];

    // Eliminar KPIs existentes para estas semanas
    await prisma.kpiSemanal.deleteMany({
      where: {
        OR: sampleKpis.map(kpi => ({
          year: kpi.year,
          weekNumber: kpi.weekNumber
        }))
      }
    });

    console.log('🗑️  KPIs existentes eliminados para evitar duplicados');

    // Crear los nuevos KPIs
    for (const kpiData of sampleKpis) {
      const kpi = await prisma.kpiSemanal.create({
        data: {
          ...kpiData,
          userId: workerUser.id
        }
      });

      console.log(`✅ KPI creado para semana ${kpi.weekNumber}/${kpi.year}:`);
      console.log(`   - Volumen: ${kpi.volumenTotalLitros.toLocaleString()} L`);
      console.log(`   - Crecimiento: ${kpi.crecimientoMensual}%`);
      console.log(`   - Retención: ${kpi.tasaRetencionClientes}%`);
    }

    // Mantener el KPI de la semana 28 si existe
    const week28Kpi = await prisma.kpiSemanal.findUnique({
      where: {
        year_weekNumber: {
          year: 2025,
          weekNumber: 28
        }
      }
    });

    if (!week28Kpi) {
      const currentWeekKpi = await prisma.kpiSemanal.create({
        data: {
          year: 2025,
          weekNumber: 28,
          weekStartDate: new Date('2025-07-07'),
          weekEndDate: new Date('2025-07-13'),
          volumenTotalLitros: 2600000, // Datos actualizados del script anterior
          crecimientoMensual: 9.2,
          margenBrutoPorLitro: 2.45,
          tasaRetencionClientes: 88.5,
          cumplimientoObjetivo: 95.2,
          desviacionVentas: -2.1,
          cicloPromedioCierre: 12,
          clientesActivosMensuales: 1920,
          userId: workerUser.id
        }
      });

      console.log(`✅ KPI creado para semana actual ${currentWeekKpi.weekNumber}/${currentWeekKpi.year}:`);
      console.log(`   - Volumen: ${currentWeekKpi.volumenTotalLitros.toLocaleString()} L`);
      console.log(`   - Crecimiento: ${currentWeekKpi.crecimientoMensual}%`);
    } else {
      console.log(`ℹ️  KPI de semana 28/2025 ya existe, manteniéndolo`);
    }

    // Mostrar resumen final
    const totalKpis = await prisma.kpiSemanal.count();
    console.log(`\n🎉 Proceso completado. Total de KPIs en la base de datos: ${totalKpis}`);
    
    const latestKpis = await prisma.kpiSemanal.findMany({
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ],
      take: 3,
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    console.log('\n📊 Últimos 3 KPIs capturados:');
    latestKpis.forEach(kpi => {
      console.log(`   - Semana ${kpi.weekNumber}/${kpi.year}: ${kpi.volumenTotalLitros.toLocaleString()} L, ${kpi.crecimientoMensual}% crecimiento`);
    });

    console.log('\n🚀 Los datos ahora se mostrarán en el Panel de Rendimiento Comercial');

  } catch (error) {
    console.error('❌ Error al crear KPIs de muestra:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSampleKpis();
