const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createWorkerUser() {
  try {
    // Verificar si ya existe un usuario worker
    const existingWorker = await prisma.user.findFirst({
      where: { role: 'WORKER' }
    });

    if (existingWorker) {
      console.log('Ya existe un usuario con rol WORKER:', existingWorker.email);
      return;
    }

    // Crear usuario worker
    const hashedPassword = await bcrypt.hash('worker123', 10);
    
    const worker = await prisma.user.create({
      data: {
        name: '<PERSON>rab<PERSON><PERSON> Prueba',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'WORKER'
      }
    });

    console.log('Usuario WORKER creado exitosamente:');
    console.log('Email:', worker.email);
    console.log('Password: worker123');
    console.log('Rol:', worker.role);

  } catch (error) {
    console.error('Error al crear usuario worker:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createWorkerUser();
