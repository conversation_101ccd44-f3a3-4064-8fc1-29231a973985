const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedAreas() {
  try {
    console.log('🌱 Iniciando seed de áreas...');

    // Definir las áreas basadas en el dashboard actual
    const areas = [
      {
        nombre: 'ventas',
        displayName: 'Ventas',
        descripcion: 'Área encargada de la gestión de ventas y relaciones con clientes',
        orden: 1,
        icono: 'ShoppingCart',
        color: '#3b82f6', // blue-500
        activo: true
      },
      {
        nombre: 'compras',
        displayName: 'Compras',
        descripcion: 'Área encargada de la gestión de compras y proveedores',
        orden: 2,
        icono: 'Package',
        color: '#10b981', // emerald-500
        activo: true
      },
      {
        nombre: 'logistica',
        displayName: 'Logística',
        descripcion: 'Área encargada de la gestión logística y distribución',
        orden: 3,
        icono: 'Truck',
        color: '#f59e0b', // amber-500
        activo: true
      },
      {
        nombre: 'contabilidad',
        displayName: 'Contabilidad',
        descripcion: 'Área encargada de la gestión contable y financiera',
        orden: 4,
        icono: 'Calculator',
        color: '#8b5cf6', // violet-500
        activo: true
      },
      {
        nombre: 'operaciones',
        displayName: 'Operaciones',
        descripcion: 'Área encargada de las operaciones diarias y procesos',
        orden: 5,
        icono: 'Settings',
        color: '#6366f1', // indigo-500
        activo: true
      },
      {
        nombre: 'legal',
        displayName: 'Legal',
        descripcion: 'Área encargada de asuntos legales y cumplimiento normativo',
        orden: 6,
        icono: 'Scale',
        color: '#ef4444', // red-500
        activo: true
      },
      {
        nombre: 'planeacion',
        displayName: 'Planeación',
        descripcion: 'Área encargada de la planeación estratégica y proyectos',
        orden: 7,
        icono: 'Target',
        color: '#06b6d4', // cyan-500
        activo: true
      },
      {
        nombre: 'finanzas',
        displayName: 'Finanzas',
        descripcion: 'Área encargada de la gestión financiera y presupuestos',
        orden: 8,
        icono: 'Banknote',
        color: '#059669', // emerald-600
        activo: true
      }
    ];

    // Verificar si ya existen áreas
    const existingAreas = await prisma.area.count();
    
    if (existingAreas > 0) {
      console.log(`⚠️  Ya existen ${existingAreas} áreas en la base de datos.`);
      console.log('🔄 Actualizando áreas existentes...');
      
      // Actualizar áreas existentes o crear nuevas
      for (const areaData of areas) {
        await prisma.area.upsert({
          where: { nombre: areaData.nombre },
          update: {
            displayName: areaData.displayName,
            descripcion: areaData.descripcion,
            orden: areaData.orden,
            icono: areaData.icono,
            color: areaData.color,
            activo: areaData.activo
          },
          create: areaData
        });
        console.log(`✅ Área "${areaData.displayName}" procesada`);
      }
    } else {
      console.log('📝 Creando áreas iniciales...');
      
      // Crear todas las áreas
      for (const areaData of areas) {
        const area = await prisma.area.create({
          data: areaData
        });
        console.log(`✅ Área "${area.displayName}" creada con ID: ${area.id}`);
      }
    }

    // Mostrar resumen de áreas creadas
    const totalAreas = await prisma.area.count();
    const areasActivas = await prisma.area.count({ where: { activo: true } });
    
    console.log('\n📊 Resumen de áreas:');
    console.log(`   Total de áreas: ${totalAreas}`);
    console.log(`   Áreas activas: ${areasActivas}`);
    
    // Mostrar todas las áreas ordenadas
    const todasLasAreas = await prisma.area.findMany({
      orderBy: { orden: 'asc' }
    });
    
    console.log('\n📋 Áreas disponibles:');
    todasLasAreas.forEach((area, index) => {
      console.log(`   ${index + 1}. ${area.displayName} (${area.nombre}) - ${area.activo ? '✅ Activa' : '❌ Inactiva'}`);
    });

    console.log('\n🎉 Seed de áreas completado exitosamente!');
    
  } catch (error) {
    console.error('❌ Error durante el seed de áreas:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar el seed si el script se ejecuta directamente
if (require.main === module) {
  seedAreas()
    .then(() => {
      console.log('✅ Script de seed ejecutado correctamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error en el script de seed:', error);
      process.exit(1);
    });
}

module.exports = { seedAreas };
