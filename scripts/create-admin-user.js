const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // Verificar si ya existe un usuario admin
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (existingAdmin) {
      console.log('Ya existe un usuario con rol ADMIN:', existingAdmin.email);
      return;
    }

    // Crear usuario admin
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    const admin = await prisma.user.create({
      data: {
        name: '<PERSON><PERSON>stra<PERSON> Prueba',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN'
      }
    });

    console.log('Usuario ADMIN creado exitosamente:');
    console.log('Email:', admin.email);
    console.log('Password: admin123');
    console.log('Rol:', admin.role);

  } catch (error) {
    console.error('Error al crear usuario admin:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
