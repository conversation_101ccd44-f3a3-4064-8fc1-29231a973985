const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSampleProveedores() {
  try {
    console.log('🏭 Creando proveedores de ejemplo...');

    // Datos de proveedores de ejemplo
    const sampleProveedores = [
      {
        nombre: "PEMEX Refinación",
        descripcion: "Petróleos Mexicanos - Refinación",
        activo: true
      },
      {
        nombre: "Shell México",
        descripcion: "Shell de México S.A. de C.V.",
        activo: true
      },
      {
        nombre: "BP México",
        descripcion: "British Petroleum México",
        activo: true
      },
      {
        nombre: "ExxonMobil México",
        descripcion: "ExxonMobil de México S.A. de C.V.",
        activo: true
      },
      {
        nombre: "Chevron México",
        descripcion: "Chevron de México S.A. de C.V.",
        activo: true
      },
      {
        nombre: "Total Energies México",
        descripcion: "Total Energies Marketing México",
        activo: true
      },
      {
        nombre: "Valero Energy",
        descripcion: "Valero Energy Corporation México",
        activo: true
      },
      {
        nombre: "Marathon Petroleum",
        descripcion: "Marathon Petroleum Corporation México",
        activo: true
      },
      {
        nombre: "Phillips 66",
        descripcion: "Phillips 66 México",
        activo: true
      },
      {
        nombre: "Repsol México",
        descripcion: "Repsol Mexicana S.A. de C.V.",
        activo: true
      },
      {
        nombre: "Petrobras México",
        descripcion: "Petróleo Brasileiro S.A. México",
        activo: true
      },
      {
        nombre: "Lukoil México",
        descripcion: "Lukoil México S.A. de C.V.",
        activo: true
      },
      {
        nombre: "Trafigura México",
        descripcion: "Trafigura México S.A. de C.V.",
        activo: true
      },
      {
        nombre: "Vitol México",
        descripcion: "Vitol México S.A. de C.V.",
        activo: true
      },
      {
        nombre: "Glencore México",
        descripcion: "Glencore México S.A. de C.V.",
        activo: true
      }
    ];

    // Crear proveedores
    for (const proveedorData of sampleProveedores) {
      // Verificar si ya existe un proveedor con ese nombre
      const existingProveedor = await prisma.proveedor.findUnique({
        where: { nombre: proveedorData.nombre }
      });

      if (!existingProveedor) {
        const proveedor = await prisma.proveedor.create({
          data: proveedorData
        });
        console.log(`✅ Proveedor creado: ${proveedor.nombre}`);
      } else {
        console.log(`⚠️  Proveedor ya existe: ${proveedorData.nombre}`);
      }
    }

    console.log('\n🎉 ¡Proveedores de ejemplo creados exitosamente!');
    console.log(`📊 Total de proveedores: ${sampleProveedores.length}`);
    console.log('🏭 Los proveedores incluyen las principales empresas petroleras y comercializadoras');

  } catch (error) {
    console.error('❌ Error al crear proveedores de ejemplo:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar el script
createSampleProveedores();
