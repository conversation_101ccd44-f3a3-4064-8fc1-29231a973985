const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSampleKpisCompras() {
  try {
    console.log('📊 Creando KPIs de compras de ejemplo con proveedores reales...');

    // Buscar usuario WORKER para asignar los KPIs
    const workerUser = await prisma.user.findFirst({
      where: { role: 'WORKER' }
    });

    if (!workerUser) {
      console.error('❌ No se encontró un usuario con rol WORKER');
      return;
    }

    // Obtener proveedores de la base de datos
    const proveedores = await prisma.proveedor.findMany({
      where: { activo: true },
      orderBy: { nombre: 'asc' }
    });

    if (proveedores.length < 3) {
      console.error('❌ Se necesitan al menos 3 proveedores en la base de datos');
      return;
    }

    console.log(`📋 Proveedores encontrados: ${proveedores.length}`);

    // Función para generar distribución realista de proveedores
    const generateProveedorDistribution = (weekIndex) => {
      const selectedProveedores = [];
      const numProveedores = Math.min(3 + (weekIndex % 3), Math.min(5, proveedores.length)); // 3-5 proveedores por semana
      
      // Seleccionar proveedores principales (siempre incluir PEMEX si existe)
      const pemex = proveedores.find(p => p.nombre.includes('PEMEX'));
      if (pemex) {
        selectedProveedores.push({
          nombre: pemex.nombre,
          porcentaje: 35 + (weekIndex % 10) // 35-44%
        });
      }

      // Agregar otros proveedores principales
      const otherProveedores = proveedores.filter(p => p.id !== pemex?.id);
      const shuffled = otherProveedores.sort(() => 0.5 - Math.random()); // Mezclar aleatoriamente
      
      for (let i = 0; i < numProveedores - (pemex ? 1 : 0) && i < shuffled.length; i++) {
        selectedProveedores.push({
          nombre: shuffled[i].nombre,
          porcentaje: 0 // Se calculará después
        });
      }

      // Calcular porcentajes restantes
      const totalAsignado = selectedProveedores.reduce((sum, p) => sum + p.porcentaje, 0);
      const restante = 100 - totalAsignado;
      const proveedoresRestantes = selectedProveedores.filter(p => p.porcentaje === 0);

      if (proveedoresRestantes.length > 0) {
        const porcentajePorProveedor = restante / proveedoresRestantes.length;
        proveedoresRestantes.forEach((p, index) => {
          if (index === proveedoresRestantes.length - 1) {
            // El último proveedor toma el restante para que sume exactamente 100%
            p.porcentaje = Number((restante - (porcentajePorProveedor * (proveedoresRestantes.length - 1))).toFixed(1));
          } else {
            p.porcentaje = Number(porcentajePorProveedor.toFixed(1));
          }
        });
      }

      // Verificar que sume 100%
      const total = selectedProveedores.reduce((sum, p) => sum + p.porcentaje, 0);
      if (Math.abs(total - 100) > 0.1) {
        // Ajustar el primer proveedor para que sume exactamente 100%
        selectedProveedores[0].porcentaje += (100 - total);
        selectedProveedores[0].porcentaje = Number(selectedProveedores[0].porcentaje.toFixed(1));
      }

      return selectedProveedores;
    };

    // Datos de ejemplo para las últimas 6 semanas (semanas 23-28 de 2025)
    const sampleData = [
      // Semana 23 (2025-06-02 a 2025-06-08)
      {
        year: 2025,
        weekNumber: 23,
        weekStartDate: new Date('2025-06-02'),
        weekEndDate: new Date('2025-06-08'),
        numeroProveedoresActivos: 22,
        porcentajeReporteGanancia: 89.2,
        preciosPromedioCompra: 21.45,
        diferencialPrecioPemex: -1.15
      },
      // Semana 24 (2025-06-09 a 2025-06-15)
      {
        year: 2025,
        weekNumber: 24,
        weekStartDate: new Date('2025-06-09'),
        weekEndDate: new Date('2025-06-15'),
        numeroProveedoresActivos: 23,
        porcentajeReporteGanancia: 91.1,
        preciosPromedioCompra: 21.62,
        diferencialPrecioPemex: -1.18
      },
      // Semana 25 (2025-06-16 a 2025-06-22)
      {
        year: 2025,
        weekNumber: 25,
        weekStartDate: new Date('2025-06-16'),
        weekEndDate: new Date('2025-06-22'),
        numeroProveedoresActivos: 21,
        porcentajeReporteGanancia: 88.7,
        preciosPromedioCompra: 21.38,
        diferencialPrecioPemex: -1.32
      },
      // Semana 26 (2025-06-23 a 2025-06-29)
      {
        year: 2025,
        weekNumber: 26,
        weekStartDate: new Date('2025-06-23'),
        weekEndDate: new Date('2025-06-29'),
        numeroProveedoresActivos: 24,
        porcentajeReporteGanancia: 93.2,
        preciosPromedioCompra: 21.78,
        diferencialPrecioPemex: -1.22
      },
      // Semana 27 (2025-06-30 a 2025-07-06)
      {
        year: 2025,
        weekNumber: 27,
        weekStartDate: new Date('2025-06-30'),
        weekEndDate: new Date('2025-07-06'),
        numeroProveedoresActivos: 23,
        porcentajeReporteGanancia: 90.8,
        preciosPromedioCompra: 21.71,
        diferencialPrecioPemex: -1.28
      },
      // Semana 28 (2025-07-07 a 2025-07-13)
      {
        year: 2025,
        weekNumber: 28,
        weekStartDate: new Date('2025-07-07'),
        weekEndDate: new Date('2025-07-13'),
        numeroProveedoresActivos: 24,
        porcentajeReporteGanancia: 92.5,
        preciosPromedioCompra: 21.85,
        diferencialPrecioPemex: -1.25
      }
    ];

    // Crear KPIs de compras
    for (let i = 0; i < sampleData.length; i++) {
      const kpiData = sampleData[i];
      
      // Verificar si ya existe un KPI para esa semana
      const existingKpi = await prisma.kpiCompras.findUnique({
        where: {
          year_weekNumber: {
            year: kpiData.year,
            weekNumber: kpiData.weekNumber
          }
        }
      });

      if (!existingKpi) {
        const distribucionProveedores = generateProveedorDistribution(i);
        
        const kpi = await prisma.kpiCompras.create({
          data: {
            ...kpiData,
            distribucionProveedores: JSON.stringify(distribucionProveedores),
            userId: workerUser.id
          }
        });
        
        console.log(`✅ KPI Compras creado para semana ${kpi.weekNumber}/${kpi.year}:`);
        distribucionProveedores.forEach(p => {
          console.log(`   ${p.nombre}: ${p.porcentaje}%`);
        });
        console.log(`   Total: ${distribucionProveedores.reduce((sum, p) => sum + p.porcentaje, 0)}%\n`);
      } else {
        console.log(`⚠️  KPI Compras ya existe para semana ${kpiData.weekNumber}/${kpiData.year}`);
      }
    }

    console.log('\n🎉 ¡KPIs de compras de ejemplo creados exitosamente!');
    console.log(`📊 Total de semanas con datos: ${sampleData.length}`);
    console.log('🏭 Los datos incluyen proveedores reales de la base de datos');

  } catch (error) {
    console.error('❌ Error al crear KPIs de compras de ejemplo:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar el script
createSampleKpisCompras();
