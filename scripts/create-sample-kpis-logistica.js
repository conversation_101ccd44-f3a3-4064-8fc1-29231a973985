const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSampleKpisLogistica() {
  try {
    console.log('🚛 Creando KPIs de ejemplo para Logística...\n');

    // Buscar un usuario WORKER para asignar los KPIs
    let workerUser = await prisma.user.findFirst({
      where: {
        role: 'WORKER'
      }
    });

    if (!workerUser) {
      console.log('❌ No se encontró un usuario con rol WORKER. Creando uno...');

      workerUser = await prisma.user.create({
        data: {
          name: '<PERSON><PERSON>rio Logística',
          email: '<EMAIL>',
          password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
          role: 'WORKER'
        }
      });

      console.log('✅ Usuario WORKER creado:', workerUser.email);
    }

    // Datos de ejemplo para las últimas 16 semanas (semanas 13-28 de 2025)
    const sampleData = [
      // Semana 13 (2025-03-24 a 2025-03-30)
      {
        year: 2025,
        weekNumber: 13,
        weekStartDate: new Date('2025-03-24'),
        weekEndDate: new Date('2025-03-30'),
        unidadesConfirmadas: 145,
        unidadesSolicitadas: 160,
        porcentajeEntregasTiempo: 87.5,
        porcentajeRetardos: 8.2,
        porcentajeReprogramaciones: 4.3,
        promedioKmOperacion: 285.4,
        promedioCostoFleteLitro: 1.85,
        promedioCostoFleteOperacion: 12500.00,
        pagoSemanalFlete: 1875000.00,
        pagoSemanalPenalizaciones: 45000.00,
        porcentajeRutasCotizadas: 92.3,
        porcentajeTransportistas: 78.5
      },
      // Semana 14 (2025-03-31 a 2025-04-06)
      {
        year: 2025,
        weekNumber: 14,
        weekStartDate: new Date('2025-03-31'),
        weekEndDate: new Date('2025-04-06'),
        unidadesConfirmadas: 152,
        unidadesSolicitadas: 165,
        porcentajeEntregasTiempo: 89.1,
        porcentajeRetardos: 7.1,
        porcentajeReprogramaciones: 3.8,
        promedioKmOperacion: 292.1,
        promedioCostoFleteLitro: 1.82,
        promedioCostoFleteOperacion: 12750.00,
        pagoSemanalFlete: 1940000.00,
        pagoSemanalPenalizaciones: 38000.00,
        porcentajeRutasCotizadas: 94.1,
        porcentajeTransportistas: 81.2
      },
      // Semana 15 (2025-04-07 a 2025-04-13)
      {
        year: 2025,
        weekNumber: 15,
        weekStartDate: new Date('2025-04-07'),
        weekEndDate: new Date('2025-04-13'),
        unidadesConfirmadas: 148,
        unidadesSolicitadas: 158,
        porcentajeEntregasTiempo: 91.2,
        porcentajeRetardos: 6.3,
        porcentajeReprogramaciones: 2.5,
        promedioKmOperacion: 278.9,
        promedioCostoFleteLitro: 1.88,
        promedioCostoFleteOperacion: 12200.00,
        pagoSemanalFlete: 1806000.00,
        pagoSemanalPenalizaciones: 32000.00,
        porcentajeRutasCotizadas: 95.7,
        porcentajeTransportistas: 83.8
      },
      // Semana 16 (2025-04-14 a 2025-04-20)
      {
        year: 2025,
        weekNumber: 16,
        weekStartDate: new Date('2025-04-14'),
        weekEndDate: new Date('2025-04-20'),
        unidadesConfirmadas: 156,
        unidadesSolicitadas: 170,
        porcentajeEntregasTiempo: 88.7,
        porcentajeRetardos: 7.8,
        porcentajeReprogramaciones: 3.5,
        promedioKmOperacion: 301.5,
        promedioCostoFleteLitro: 1.79,
        promedioCostoFleteOperacion: 13100.00,
        pagoSemanalFlete: 2043600.00,
        pagoSemanalPenalizaciones: 42000.00,
        porcentajeRutasCotizadas: 93.4,
        porcentajeTransportistas: 85.1
      },
      // Semana 17 (2025-04-21 a 2025-04-27)
      {
        year: 2025,
        weekNumber: 17,
        weekStartDate: new Date('2025-04-21'),
        weekEndDate: new Date('2025-04-27'),
        unidadesConfirmadas: 162,
        unidadesSolicitadas: 175,
        porcentajeEntregasTiempo: 90.5,
        porcentajeRetardos: 6.9,
        porcentajeReprogramaciones: 2.6,
        promedioKmOperacion: 295.8,
        promedioCostoFleteLitro: 1.83,
        promedioCostoFleteOperacion: 12850.00,
        pagoSemanalFlete: 2081700.00,
        pagoSemanalPenalizaciones: 35000.00,
        porcentajeRutasCotizadas: 96.2,
        porcentajeTransportistas: 87.4
      },
      // Semana 18 (2025-04-28 a 2025-05-04)
      {
        year: 2025,
        weekNumber: 18,
        weekStartDate: new Date('2025-04-28'),
        weekEndDate: new Date('2025-05-04'),
        unidadesConfirmadas: 159,
        unidadesSolicitadas: 168,
        porcentajeEntregasTiempo: 92.8,
        porcentajeRetardos: 5.1,
        porcentajeReprogramaciones: 2.1,
        promedioKmOperacion: 287.3,
        promedioCostoFleteLitro: 1.86,
        promedioCostoFleteOperacion: 12650.00,
        pagoSemanalFlete: 2011350.00,
        pagoSemanalPenalizaciones: 28000.00,
        porcentajeRutasCotizadas: 97.8,
        porcentajeTransportistas: 89.2
      },
      // Semana 19 (2025-05-05 a 2025-05-11)
      {
        year: 2025,
        weekNumber: 19,
        weekStartDate: new Date('2025-05-05'),
        weekEndDate: new Date('2025-05-11'),
        unidadesConfirmadas: 164,
        unidadesSolicitadas: 172,
        porcentajeEntregasTiempo: 94.1,
        porcentajeRetardos: 4.2,
        porcentajeReprogramaciones: 1.7,
        promedioKmOperacion: 279.6,
        promedioCostoFleteLitro: 1.81,
        promedioCostoFleteOperacion: 12400.00,
        pagoSemanalFlete: 2033600.00,
        pagoSemanalPenalizaciones: 22000.00,
        porcentajeRutasCotizadas: 98.5,
        porcentajeTransportistas: 91.7
      },
      // Semana 20 (2025-05-12 a 2025-05-18)
      {
        year: 2025,
        weekNumber: 20,
        weekStartDate: new Date('2025-05-12'),
        weekEndDate: new Date('2025-05-18'),
        unidadesConfirmadas: 167,
        unidadesSolicitadas: 178,
        porcentajeEntregasTiempo: 91.6,
        porcentajeRetardos: 6.1,
        porcentajeReprogramaciones: 2.3,
        promedioKmOperacion: 298.2,
        promedioCostoFleteLitro: 1.84,
        promedioCostoFleteOperacion: 12900.00,
        pagoSemanalFlete: 2154300.00,
        pagoSemanalPenalizaciones: 31000.00,
        porcentajeRutasCotizadas: 96.8,
        porcentajeTransportistas: 93.1
      },
      // Semana 21 (2025-05-19 a 2025-05-25)
      {
        year: 2025,
        weekNumber: 21,
        weekStartDate: new Date('2025-05-19'),
        weekEndDate: new Date('2025-05-25'),
        unidadesConfirmadas: 171,
        unidadesSolicitadas: 182,
        porcentajeEntregasTiempo: 93.4,
        porcentajeRetardos: 4.8,
        porcentajeReprogramaciones: 1.8,
        promedioKmOperacion: 284.7,
        promedioCostoFleteLitro: 1.87,
        promedioCostoFleteOperacion: 12550.00,
        pagoSemanalFlete: 2146050.00,
        pagoSemanalPenalizaciones: 25000.00,
        porcentajeRutasCotizadas: 97.9,
        porcentajeTransportistas: 94.8
      },
      // Semana 22 (2025-05-26 a 2025-06-01)
      {
        year: 2025,
        weekNumber: 22,
        weekStartDate: new Date('2025-05-26'),
        weekEndDate: new Date('2025-06-01'),
        unidadesConfirmadas: 168,
        unidadesSolicitadas: 175,
        porcentajeEntregasTiempo: 95.2,
        porcentajeRetardos: 3.4,
        porcentajeReprogramaciones: 1.4,
        promedioKmOperacion: 276.1,
        promedioCostoFleteLitro: 1.89,
        promedioCostoFleteOperacion: 12300.00,
        pagoSemanalFlete: 2066400.00,
        pagoSemanalPenalizaciones: 18000.00,
        porcentajeRutasCotizadas: 98.7,
        porcentajeTransportistas: 96.2
      },
      // Semana 23 (2025-06-02 a 2025-06-08)
      {
        year: 2025,
        weekNumber: 23,
        weekStartDate: new Date('2025-06-02'),
        weekEndDate: new Date('2025-06-08'),
        unidadesConfirmadas: 174,
        unidadesSolicitadas: 185,
        porcentajeEntregasTiempo: 92.7,
        porcentajeRetardos: 5.5,
        porcentajeReprogramaciones: 1.8,
        promedioKmOperacion: 291.4,
        promedioCostoFleteLitro: 1.85,
        promedioCostoFleteOperacion: 12700.00,
        pagoSemanalFlete: 2209800.00,
        pagoSemanalPenalizaciones: 29000.00,
        porcentajeRutasCotizadas: 97.1,
        porcentajeTransportistas: 97.5
      },
      // Semana 24 (2025-06-09 a 2025-06-15)
      {
        year: 2025,
        weekNumber: 24,
        weekStartDate: new Date('2025-06-09'),
        weekEndDate: new Date('2025-06-15'),
        unidadesConfirmadas: 176,
        unidadesSolicitadas: 188,
        porcentajeEntregasTiempo: 94.8,
        porcentajeRetardos: 3.7,
        porcentajeReprogramaciones: 1.5,
        promedioKmOperacion: 283.9,
        promedioCostoFleteLitro: 1.82,
        promedioCostoFleteOperacion: 12450.00,
        pagoSemanalFlete: 2191200.00,
        pagoSemanalPenalizaciones: 21000.00,
        porcentajeRutasCotizadas: 98.4,
        porcentajeTransportistas: 98.1
      },
      // Semana 25 (2025-06-16 a 2025-06-22)
      {
        year: 2025,
        weekNumber: 25,
        weekStartDate: new Date('2025-06-16'),
        weekEndDate: new Date('2025-06-22'),
        unidadesConfirmadas: 178,
        unidadesSolicitadas: 190,
        porcentajeEntregasTiempo: 96.2,
        porcentajeRetardos: 2.8,
        porcentajeReprogramaciones: 1.0,
        promedioKmOperacion: 275.8,
        promedioCostoFleteLitro: 1.80,
        promedioCostoFleteOperacion: 12200.00,
        pagoSemanalFlete: 2172400.00,
        pagoSemanalPenalizaciones: 15000.00,
        porcentajeRutasCotizadas: 99.1,
        porcentajeTransportistas: 98.8
      },
      // Semana 26 (2025-06-23 a 2025-06-29)
      {
        year: 2025,
        weekNumber: 26,
        weekStartDate: new Date('2025-06-23'),
        weekEndDate: new Date('2025-06-29'),
        unidadesConfirmadas: 180,
        unidadesSolicitadas: 192,
        porcentajeEntregasTiempo: 97.8,
        porcentajeRetardos: 1.5,
        porcentajeReprogramaciones: 0.7,
        promedioKmOperacion: 268.2,
        promedioCostoFleteLitro: 1.78,
        promedioCostoFleteOperacion: 11950.00,
        pagoSemanalFlete: 2151000.00,
        pagoSemanalPenalizaciones: 12000.00,
        porcentajeRutasCotizadas: 99.5,
        porcentajeTransportistas: 99.2
      },
      // Semana 27 (2025-06-30 a 2025-07-06)
      {
        year: 2025,
        weekNumber: 27,
        weekStartDate: new Date('2025-06-30'),
        weekEndDate: new Date('2025-07-06'),
        unidadesConfirmadas: 182,
        unidadesSolicitadas: 195,
        porcentajeEntregasTiempo: 98.5,
        porcentajeRetardos: 1.2,
        porcentajeReprogramaciones: 0.3,
        promedioKmOperacion: 262.7,
        promedioCostoFleteLitro: 1.76,
        promedioCostoFleteOperacion: 11800.00,
        pagoSemanalFlete: 2147600.00,
        pagoSemanalPenalizaciones: 8000.00,
        porcentajeRutasCotizadas: 99.8,
        porcentajeTransportistas: 99.6
      },
      // Semana 28 (2025-07-07 a 2025-07-13)
      {
        year: 2025,
        weekNumber: 28,
        weekStartDate: new Date('2025-07-07'),
        weekEndDate: new Date('2025-07-13'),
        unidadesConfirmadas: 185,
        unidadesSolicitadas: 198,
        porcentajeEntregasTiempo: 99.1,
        porcentajeRetardos: 0.8,
        porcentajeReprogramaciones: 0.1,
        promedioKmOperacion: 258.3,
        promedioCostoFleteLitro: 1.74,
        promedioCostoFleteOperacion: 11650.00,
        pagoSemanalFlete: 2155750.00,
        pagoSemanalPenalizaciones: 5000.00,
        porcentajeRutasCotizadas: 99.9,
        porcentajeTransportistas: 99.8
      }
    ];

    // Crear KPIs de logística
    for (const kpiData of sampleData) {
      // Verificar si ya existe un KPI para esa semana
      const existingKpi = await prisma.kpiLogistica.findUnique({
        where: {
          year_weekNumber: {
            year: kpiData.year,
            weekNumber: kpiData.weekNumber
          }
        }
      });

      if (!existingKpi) {
        const kpi = await prisma.kpiLogistica.create({
          data: {
            ...kpiData,
            userId: workerUser.id
          }
        });
        console.log(`✅ KPI Logística creado para semana ${kpi.weekNumber}/${kpi.year}`);
      } else {
        console.log(`⚠️  KPI Logística ya existe para semana ${kpiData.weekNumber}/${kpiData.year}`);
      }
    }

    console.log('\n🎉 ¡KPIs de logística de ejemplo creados exitosamente!');
    console.log(`📊 Total de semanas con datos: ${sampleData.length}`);
    console.log('🚛 Los datos incluyen métricas de entregas, costos, rutas y transportistas');

  } catch (error) {
    console.error('❌ Error al crear KPIs de logística de ejemplo:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar el script
createSampleKpisLogistica();
