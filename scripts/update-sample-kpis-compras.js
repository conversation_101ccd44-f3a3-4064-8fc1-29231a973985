const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateSampleKpisCompras() {
  try {
    console.log('🔄 Actualizando KPIs de compras de ejemplo con proveedores reales...');

    // Obtener proveedores de la base de datos
    const proveedores = await prisma.proveedor.findMany({
      where: { activo: true },
      orderBy: { nombre: 'asc' }
    });

    if (proveedores.length < 3) {
      console.error('❌ Se necesitan al menos 3 proveedores en la base de datos');
      return;
    }

    console.log(`📋 Proveedores encontrados: ${proveedores.length}`);
    proveedores.forEach(p => console.log(`  - ${p.nombre}`));

    // Obtener KPIs de compras existentes
    const kpisCompras = await prisma.kpiCompras.findMany({
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ]
    });

    if (kpisCompras.length === 0) {
      console.log('⚠️  No hay KPIs de compras para actualizar');
      return;
    }

    console.log(`📊 KPIs de compras encontrados: ${kpisCompras.length}`);

    // Función para generar distribución realista de proveedores
    const generateProveedorDistribution = (weekIndex) => {
      const selectedProveedores = [];
      const numProveedores = Math.min(3 + (weekIndex % 3), proveedores.length); // 3-5 proveedores por semana
      
      // Seleccionar proveedores principales (siempre incluir PEMEX si existe)
      const pemex = proveedores.find(p => p.nombre.includes('PEMEX'));
      if (pemex) {
        selectedProveedores.push({
          nombre: pemex.nombre,
          porcentaje: 35 + (weekIndex % 10) // 35-44%
        });
      }

      // Agregar otros proveedores principales
      const otherProveedores = proveedores.filter(p => p.id !== pemex?.id);
      for (let i = 0; i < numProveedores - 1 && i < otherProveedores.length; i++) {
        selectedProveedores.push({
          nombre: otherProveedores[i].nombre,
          porcentaje: 0 // Se calculará después
        });
      }

      // Calcular porcentajes restantes
      const totalAsignado = selectedProveedores.reduce((sum, p) => sum + p.porcentaje, 0);
      const restante = 100 - totalAsignado;
      const proveedoresRestantes = selectedProveedores.filter(p => p.porcentaje === 0);

      if (proveedoresRestantes.length > 0) {
        const porcentajePorProveedor = restante / proveedoresRestantes.length;
        proveedoresRestantes.forEach((p, index) => {
          if (index === proveedoresRestantes.length - 1) {
            // El último proveedor toma el restante para que sume exactamente 100%
            p.porcentaje = Number((restante - (porcentajePorProveedor * (proveedoresRestantes.length - 1))).toFixed(1));
          } else {
            p.porcentaje = Number(porcentajePorProveedor.toFixed(1));
          }
        });
      }

      // Verificar que sume 100%
      const total = selectedProveedores.reduce((sum, p) => sum + p.porcentaje, 0);
      if (Math.abs(total - 100) > 0.1) {
        // Ajustar el primer proveedor para que sume exactamente 100%
        selectedProveedores[0].porcentaje += (100 - total);
        selectedProveedores[0].porcentaje = Number(selectedProveedores[0].porcentaje.toFixed(1));
      }

      return selectedProveedores;
    };

    // Actualizar cada KPI con proveedores reales
    for (let i = 0; i < kpisCompras.length; i++) {
      const kpi = kpisCompras[i];
      const nuevaDistribucion = generateProveedorDistribution(i);

      await prisma.kpiCompras.update({
        where: { id: kpi.id },
        data: {
          distribucionProveedores: JSON.stringify(nuevaDistribucion)
        }
      });

      console.log(`✅ KPI actualizado - Semana ${kpi.weekNumber}/${kpi.year}:`);
      nuevaDistribucion.forEach(p => {
        console.log(`   ${p.nombre}: ${p.porcentaje}%`);
      });
      console.log(`   Total: ${nuevaDistribucion.reduce((sum, p) => sum + p.porcentaje, 0)}%\n`);
    }

    console.log('🎉 ¡KPIs de compras actualizados exitosamente!');
    console.log(`📊 Total de KPIs actualizados: ${kpisCompras.length}`);
    console.log('🏭 Ahora todos los KPIs usan proveedores reales de la base de datos');

  } catch (error) {
    console.error('❌ Error al actualizar KPIs de compras:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar el script
updateSampleKpisCompras();
