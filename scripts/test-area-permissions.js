const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAreaPermissions() {
  try {
    console.log('🧪 Probando el sistema de permisos de áreas...\n');

    // 1. Verificar que las áreas existen
    console.log('1️⃣ Verificando áreas disponibles:');
    const areas = await prisma.area.findMany({
      where: { activo: true },
      orderBy: { orden: 'asc' }
    });

    if (areas.length === 0) {
      console.log('❌ No hay áreas disponibles. Ejecuta el script seed-areas.js primero.');
      return;
    }

    areas.forEach((area, index) => {
      console.log(`   ${index + 1}. ${area.displayName} (${area.nombre})`);
    });

    // 2. Verificar usuarios WORKER con áreas asignadas
    console.log('\n2️⃣ Verificando usuarios WORKER:');
    const workers = await prisma.user.findMany({
      where: { role: 'WORKER' },
      include: { area: true },
      orderBy: { name: 'asc' }
    });

    if (workers.length === 0) {
      console.log('❌ No hay usuarios WORKER. Ejecuta el script create-demo-workers.js primero.');
      return;
    }

    workers.forEach((worker, index) => {
      console.log(`   ${index + 1}. ${worker.name || worker.email}`);
      console.log(`      Email: ${worker.email}`);
      console.log(`      Área: ${worker.area ? worker.area.displayName : '❌ Sin área asignada'}`);
      console.log('');
    });

    // 3. Verificar usuarios ADMIN
    console.log('3️⃣ Verificando usuarios ADMIN:');
    const admins = await prisma.user.findMany({
      where: { 
        role: { in: ['ADMIN', 'SUPER_ADMIN'] }
      },
      include: { area: true },
      orderBy: { role: 'asc' }
    });

    if (admins.length === 0) {
      console.log('⚠️  No hay usuarios ADMIN o SUPER_ADMIN.');
    } else {
      admins.forEach((admin, index) => {
        console.log(`   ${index + 1}. ${admin.name || admin.email} (${admin.role})`);
        console.log(`      Email: ${admin.email}`);
        console.log(`      Área: ${admin.area ? admin.area.displayName : 'Sin área (acceso a todas)'}`);
        console.log('');
      });
    }

    // 4. Simular escenarios de permisos
    console.log('4️⃣ Simulando escenarios de permisos:\n');

    // Escenario 1: Worker en su área asignada
    const workerConArea = workers.find(w => w.area);
    if (workerConArea) {
      console.log(`📝 Escenario 1: Worker "${workerConArea.name}" en área "${workerConArea.area.displayName}"`);
      console.log(`   ✅ Puede VER datos de: ${workerConArea.area.displayName}`);
      console.log(`   ✅ Puede EDITAR datos de: ${workerConArea.area.displayName}`);
      console.log(`   ✅ Puede VER datos de: Todas las otras áreas`);
      console.log(`   ❌ NO puede EDITAR datos de: Otras áreas`);
      console.log(`   ✅ Puede DESCARGAR PDFs/CSVs de: Todas las áreas\n`);
    }

    // Escenario 2: Worker sin área asignada
    const workerSinArea = workers.find(w => !w.area);
    if (workerSinArea) {
      console.log(`📝 Escenario 2: Worker "${workerSinArea.name}" sin área asignada`);
      console.log(`   ✅ Puede VER datos de: Todas las áreas`);
      console.log(`   ❌ NO puede EDITAR datos de: Ninguna área`);
      console.log(`   ✅ Puede DESCARGAR PDFs/CSVs de: Todas las áreas\n`);
    }

    // Escenario 3: Admin
    if (admins.length > 0) {
      const admin = admins[0];
      console.log(`📝 Escenario 3: Admin "${admin.name}" (${admin.role})`);
      console.log(`   ✅ Puede VER datos de: Todas las áreas`);
      console.log(`   ✅ Puede EDITAR datos de: Todas las áreas`);
      console.log(`   ✅ Puede GESTIONAR áreas de usuarios`);
      console.log(`   ✅ Puede DESCARGAR PDFs/CSVs de: Todas las áreas\n`);
    }

    // 5. Verificar distribución de workers por área
    console.log('5️⃣ Distribución de trabajadores por área:');
    const areaStats = {};
    
    // Inicializar todas las áreas con 0
    areas.forEach(area => {
      areaStats[area.displayName] = 0;
    });

    // Contar workers por área
    workers.forEach(worker => {
      if (worker.area) {
        areaStats[worker.area.displayName]++;
      } else {
        if (!areaStats['Sin área asignada']) {
          areaStats['Sin área asignada'] = 0;
        }
        areaStats['Sin área asignada']++;
      }
    });

    Object.entries(areaStats).forEach(([areaName, count]) => {
      const icon = count > 0 ? '👥' : '📭';
      console.log(`   ${icon} ${areaName}: ${count} trabajador(es)`);
    });

    // 6. Recomendaciones
    console.log('\n6️⃣ Recomendaciones:');
    
    const workersWithoutArea = workers.filter(w => !w.area);
    if (workersWithoutArea.length > 0) {
      console.log(`   ⚠️  Hay ${workersWithoutArea.length} trabajador(es) sin área asignada:`);
      workersWithoutArea.forEach(worker => {
        console.log(`      - ${worker.name || worker.email} (${worker.email})`);
      });
      console.log('   💡 Asigna áreas usando el panel de administración o el script assign-worker-areas.js');
    }

    if (admins.length === 0) {
      console.log('   ⚠️  No hay usuarios administradores.');
      console.log('   💡 Crea un usuario ADMIN para gestionar las áreas.');
    }

    // 7. Comandos útiles
    console.log('\n7️⃣ Comandos útiles:');
    console.log('   📝 Asignar área específica:');
    console.log('      node scripts/assign-worker-areas.js <EMAIL> nombre_area');
    console.log('   📝 Asignar áreas automáticamente:');
    console.log('      node scripts/assign-worker-areas.js');
    console.log('   📝 Crear más usuarios de prueba:');
    console.log('      node scripts/create-demo-workers.js');

    console.log('\n✅ Prueba del sistema de permisos completada!');
    console.log('\n🎯 Resumen del funcionamiento:');
    console.log('   • WORKERS pueden VER todas las áreas');
    console.log('   • WORKERS solo pueden EDITAR su área asignada');
    console.log('   • WORKERS pueden DESCARGAR de todas las áreas');
    console.log('   • ADMINS tienen acceso completo a todo');
    console.log('   • Los botones de edición se deshabilitan según permisos');
    console.log('   • Se muestran banners informativos sobre restricciones');

  } catch (error) {
    console.error('❌ Error durante la prueba:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar el script si se ejecuta directamente
if (require.main === module) {
  testAreaPermissions()
    .then(() => {
      console.log('\n✅ Script de prueba ejecutado correctamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Error en el script de prueba:', error);
      process.exit(1);
    });
}

module.exports = { testAreaPermissions };
