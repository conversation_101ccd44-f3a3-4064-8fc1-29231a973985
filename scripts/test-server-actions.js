const { PrismaClient } = require('@prisma/client');

async function testServerActions() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Probando conexión a la base de datos...');
    
    // Verificar que el modelo KpiSemanal existe
    const kpis = await prisma.kpiSemanal.findMany({
      take: 1
    });
    
    console.log('✅ Modelo KpiSemanal encontrado');
    console.log(`📊 KPIs en la base de datos: ${kpis.length > 0 ? 'Sí' : 'No'}`);
    
    if (kpis.length > 0) {
      console.log('📈 Último KPI:', {
        semana: `${kpis[0].weekNumber}/${kpis[0].year}`,
        volumen: kpis[0].volumenTotalLitros.toLocaleString()
      });
    }
    
    console.log('✅ Los server actions deberían funcionar correctamente');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testServerActions();
