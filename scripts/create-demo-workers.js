const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createDemoWorkers() {
  try {
    console.log('👥 Creando usuarios WORKER de demostración...');

    // Definir usuarios workers para cada área
    const demoWorkers = [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'ventas123',
        area: 'ventas'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'compras123',
        area: 'compras'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'contabilidad123',
        area: 'contabilidad'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'operaciones123',
        area: 'operaciones'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'legal123',
        area: 'legal'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'planeacion123',
        area: 'planeacion'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'finanzas123',
        area: 'finanzas'
      }
    ];

    // Obtener todas las áreas
    const areas = await prisma.area.findMany({
      where: { activo: true }
    });

    const areaMap = {};
    areas.forEach(area => {
      areaMap[area.nombre] = area.id;
    });

    console.log('📋 Áreas disponibles:');
    areas.forEach(area => {
      console.log(`   - ${area.displayName} (${area.nombre})`);
    });

    let createdCount = 0;
    let updatedCount = 0;

    for (const workerData of demoWorkers) {
      // Verificar si el usuario ya existe
      const existingUser = await prisma.user.findUnique({
        where: { email: workerData.email }
      });

      const areaId = areaMap[workerData.area];
      if (!areaId) {
        console.log(`⚠️  Área "${workerData.area}" no encontrada para ${workerData.email}`);
        continue;
      }

      if (existingUser) {
        // Actualizar usuario existente
        await prisma.user.update({
          where: { id: existingUser.id },
          data: {
            name: workerData.name,
            role: 'WORKER',
            areaId: areaId
          }
        });
        console.log(`🔄 Usuario actualizado: ${workerData.name} (${workerData.email}) - Área: ${workerData.area}`);
        updatedCount++;
      } else {
        // Crear nuevo usuario
        const hashedPassword = await bcrypt.hash(workerData.password, 10);
        
        await prisma.user.create({
          data: {
            name: workerData.name,
            email: workerData.email,
            password: hashedPassword,
            role: 'WORKER',
            areaId: areaId
          }
        });
        console.log(`✅ Usuario creado: ${workerData.name} (${workerData.email}) - Área: ${workerData.area}`);
        createdCount++;
      }
    }

    // Mostrar resumen
    console.log('\n📊 Resumen de la operación:');
    console.log(`   Usuarios creados: ${createdCount}`);
    console.log(`   Usuarios actualizados: ${updatedCount}`);

    // Mostrar todos los workers con sus áreas
    console.log('\n👥 Usuarios WORKER con áreas asignadas:');
    const allWorkers = await prisma.user.findMany({
      where: { role: 'WORKER' },
      include: { area: true },
      orderBy: { name: 'asc' }
    });

    allWorkers.forEach((worker, index) => {
      console.log(`   ${index + 1}. ${worker.name || worker.email}`);
      console.log(`      Email: ${worker.email}`);
      console.log(`      Área: ${worker.area ? worker.area.displayName : 'Sin área asignada'}`);
      console.log('');
    });

    // Mostrar credenciales para pruebas
    console.log('🔑 Credenciales para pruebas:');
    demoWorkers.forEach(worker => {
      console.log(`   ${worker.name}: ${worker.email} / ${worker.password}`);
    });

    console.log('\n🎉 Usuarios WORKER de demostración creados/actualizados exitosamente!');

  } catch (error) {
    console.error('❌ Error durante la creación de usuarios WORKER:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar el script si se ejecuta directamente
if (require.main === module) {
  createDemoWorkers()
    .then(() => {
      console.log('✅ Script ejecutado correctamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error en el script:', error);
      process.exit(1);
    });
}

module.exports = { createDemoWorkers };
