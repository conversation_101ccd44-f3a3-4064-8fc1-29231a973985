const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testKpiActions() {
  try {
    console.log('🧪 Probando funcionalidades de KPIs semanales...\n');

    // 1. Verificar que la tabla KpiSemanal existe
    console.log('1. Verificando estructura de la base de datos...');
    const tableInfo = await prisma.$queryRaw`PRAGMA table_info(KpiSemanal)`;
    console.log('✅ Tabla KpiSemanal encontrada con', tableInfo.length, 'columnas');

    // 2. Verificar usuarios
    console.log('\n2. Verificando usuarios...');
    const users = await prisma.user.findMany({
      where: {
        role: {
          in: ['WORKER', 'ADMIN', 'SUPER_ADMIN']
        }
      },
      select: {
        id: true,
        email: true,
        role: true,
        name: true
      }
    });
    console.log('✅ Usuarios encontrados:', users.length);
    users.forEach(user => {
      console.log(`   - ${user.email} (${user.role})`);
    });

    // 3. Crear un KPI de prueba
    console.log('\n3. Creando KPI de prueba...');
    const workerUser = users.find(u => u.role === 'WORKER');
    
    if (!workerUser) {
      console.log('❌ No se encontró usuario WORKER');
      return;
    }

    const testKpi = {
      year: 2025,
      weekNumber: 28,
      weekStartDate: new Date('2025-07-07'),
      weekEndDate: new Date('2025-07-13'),
      volumenTotalLitros: 2500000,
      crecimientoMensual: 8.5,
      margenBrutoPorLitro: 2.45,
      tasaRetencionClientes: 88.5,
      cumplimientoObjetivo: 95.2,
      desviacionVentas: -3.8,
      cicloPromedioCierre: 11,
      clientesActivosMensuales: 1920,
      userId: workerUser.id
    };

    // Verificar si ya existe
    const existingKpi = await prisma.kpiSemanal.findUnique({
      where: {
        year_weekNumber: {
          year: testKpi.year,
          weekNumber: testKpi.weekNumber
        }
      }
    });

    if (existingKpi) {
      console.log('⚠️  Ya existe un KPI para la semana 28/2025, eliminando...');
      await prisma.kpiSemanal.delete({
        where: { id: existingKpi.id }
      });
    }

    const createdKpi = await prisma.kpiSemanal.create({
      data: testKpi,
      include: {
        user: {
          select: {
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    console.log('✅ KPI creado exitosamente:');
    console.log(`   - Semana: ${createdKpi.weekNumber}/${createdKpi.year}`);
    console.log(`   - Volumen: ${createdKpi.volumenTotalLitros.toLocaleString()} L`);
    console.log(`   - Creado por: ${createdKpi.user.name} (${createdKpi.user.email})`);

    // 4. Obtener KPIs
    console.log('\n4. Obteniendo KPIs...');
    const kpis = await prisma.kpiSemanal.findMany({
      include: {
        user: {
          select: {
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ]
    });

    console.log(`✅ Se encontraron ${kpis.length} KPIs en total`);
    kpis.forEach(kpi => {
      console.log(`   - Semana ${kpi.weekNumber}/${kpi.year}: ${kpi.volumenTotalLitros.toLocaleString()} L`);
    });

    // 5. Actualizar KPI
    console.log('\n5. Actualizando KPI...');
    const updatedKpi = await prisma.kpiSemanal.update({
      where: { id: createdKpi.id },
      data: {
        volumenTotalLitros: 2600000,
        crecimientoMensual: 9.2
      }
    });

    console.log('✅ KPI actualizado:');
    console.log(`   - Nuevo volumen: ${updatedKpi.volumenTotalLitros.toLocaleString()} L`);
    console.log(`   - Nuevo crecimiento: ${updatedKpi.crecimientoMensual}%`);

    console.log('\n🎉 Todas las pruebas pasaron exitosamente!');
    console.log('\n📋 Resumen de credenciales de prueba:');
    console.log('   - Worker: <EMAIL> / worker123');
    console.log('   - Admin: <EMAIL> / admin123 (si existe)');

  } catch (error) {
    console.error('❌ Error en las pruebas:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testKpiActions();
