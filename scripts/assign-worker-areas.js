const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function assignWorkerAreas() {
  try {
    console.log('🔍 Verificando usuarios WORKER y áreas disponibles...');

    // Obtener todos los usuarios WORKER
    const workers = await prisma.user.findMany({
      where: { role: 'WORKER' },
      include: { area: true }
    });

    console.log(`📊 Encontrados ${workers.length} usuarios WORKER`);

    if (workers.length === 0) {
      console.log('⚠️  No hay usuarios WORKER en la base de datos.');
      console.log('💡 Ejecuta el script create-worker-user.js primero para crear un usuario WORKER.');
      return;
    }

    // Obtener todas las áreas disponibles
    const areas = await prisma.area.findMany({
      where: { activo: true },
      orderBy: { orden: 'asc' }
    });

    console.log(`📋 Áreas disponibles: ${areas.length}`);
    areas.forEach((area, index) => {
      console.log(`   ${index + 1}. ${area.displayName} (${area.nombre})`);
    });

    console.log('\n👥 Estado actual de usuarios WORKER:');
    workers.forEach((worker, index) => {
      console.log(`   ${index + 1}. ${worker.name || worker.email}`);
      console.log(`      Email: ${worker.email}`);
      console.log(`      Área actual: ${worker.area ? worker.area.displayName : 'Sin área asignada'}`);
      console.log('');
    });

    // Si hay workers sin área asignada, asignar automáticamente
    const workersWithoutArea = workers.filter(worker => !worker.areaId);
    
    if (workersWithoutArea.length > 0) {
      console.log(`🔧 Asignando áreas automáticamente a ${workersWithoutArea.length} trabajadores sin área...`);
      
      for (let i = 0; i < workersWithoutArea.length; i++) {
        const worker = workersWithoutArea[i];
        // Asignar áreas de forma rotativa
        const areaIndex = i % areas.length;
        const selectedArea = areas[areaIndex];
        
        await prisma.user.update({
          where: { id: worker.id },
          data: { areaId: selectedArea.id }
        });
        
        console.log(`✅ Asignado ${worker.name || worker.email} al área "${selectedArea.displayName}"`);
      }
    }

    // Mostrar resumen final
    console.log('\n📊 Resumen final:');
    const updatedWorkers = await prisma.user.findMany({
      where: { role: 'WORKER' },
      include: { area: true }
    });

    const workersByArea = {};
    updatedWorkers.forEach(worker => {
      const areaName = worker.area ? worker.area.displayName : 'Sin área';
      if (!workersByArea[areaName]) {
        workersByArea[areaName] = [];
      }
      workersByArea[areaName].push(worker.name || worker.email);
    });

    Object.entries(workersByArea).forEach(([areaName, workers]) => {
      console.log(`   ${areaName}: ${workers.length} trabajador(es)`);
      workers.forEach(workerName => {
        console.log(`     - ${workerName}`);
      });
    });

    console.log('\n🎉 Asignación de áreas completada!');

  } catch (error) {
    console.error('❌ Error durante la asignación de áreas:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Función para asignar un área específica a un usuario específico
async function assignSpecificArea(userEmail, areaName) {
  try {
    console.log(`🎯 Asignando área "${areaName}" al usuario "${userEmail}"...`);

    // Buscar el usuario
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      include: { area: true }
    });

    if (!user) {
      console.log(`❌ Usuario con email "${userEmail}" no encontrado.`);
      return;
    }

    if (user.role !== 'WORKER') {
      console.log(`⚠️  El usuario "${userEmail}" no tiene rol WORKER (rol actual: ${user.role}).`);
      return;
    }

    // Buscar el área
    const area = await prisma.area.findUnique({
      where: { nombre: areaName }
    });

    if (!area) {
      console.log(`❌ Área "${areaName}" no encontrada.`);
      return;
    }

    if (!area.activo) {
      console.log(`⚠️  El área "${areaName}" está inactiva.`);
      return;
    }

    // Asignar el área
    await prisma.user.update({
      where: { id: user.id },
      data: { areaId: area.id }
    });

    console.log(`✅ Área "${area.displayName}" asignada correctamente a "${user.name || user.email}"`);

  } catch (error) {
    console.error('❌ Error durante la asignación específica:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar el script
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 2) {
    // Asignación específica: node assign-worker-areas.js <EMAIL> area_name
    const [userEmail, areaName] = args;
    assignSpecificArea(userEmail, areaName)
      .then(() => {
        console.log('✅ Script de asignación específica ejecutado correctamente');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Error en el script de asignación específica:', error);
        process.exit(1);
      });
  } else {
    // Asignación automática
    assignWorkerAreas()
      .then(() => {
        console.log('✅ Script de asignación automática ejecutado correctamente');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Error en el script de asignación automática:', error);
        process.exit(1);
      });
  }
}

module.exports = { assignWorkerAreas, assignSpecificArea };
