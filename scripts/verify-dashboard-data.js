const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyDashboardData() {
  try {
    console.log('📊 Verificando datos para el Panel de Rendimiento Comercial...\n');

    // Obtener todos los KPIs ordenados por fecha
    const kpis = await prisma.kpiSemanal.findMany({
      include: {
        user: {
          select: {
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ]
    });

    if (kpis.length === 0) {
      console.log('❌ No hay KPIs en la base de datos');
      return;
    }

    console.log(`✅ Total de KPIs encontrados: ${kpis.length}\n`);

    // Mostrar el KPI más reciente (datos actuales)
    const latestKpi = kpis[0];
    console.log('🔥 DATOS ACTUALES (Semana más reciente):');
    console.log(`📅 Semana: ${latestKpi.weekNumber}/${latestKpi.year}`);
    console.log(`📅 Período: ${latestKpi.weekStartDate.toLocaleDateString('es-ES')} - ${latestKpi.weekEndDate.toLocaleDateString('es-ES')}`);
    console.log(`👤 Capturado por: ${latestKpi.user.name} (${latestKpi.user.email})`);
    console.log('');
    console.log('📈 INDICADORES PRINCIPALES:');
    console.log(`   🛢️  Volumen Total: ${latestKpi.volumenTotalLitros.toLocaleString()} litros`);
    console.log(`   📊 Crecimiento Mensual: ${latestKpi.crecimientoMensual}%`);
    console.log(`   💰 Margen Bruto por Litro: $${latestKpi.margenBrutoPorLitro}`);
    console.log(`   👥 Tasa de Retención: ${latestKpi.tasaRetencionClientes}%`);
    console.log(`   🎯 Cumplimiento de Objetivo: ${latestKpi.cumplimientoObjetivo}%`);
    console.log(`   📉 Desviación de Ventas: ${latestKpi.desviacionVentas}%`);
    console.log(`   ⏱️  Ciclo de Cierre: ${latestKpi.cicloPromedioCierre} días`);
    console.log(`   👤 Clientes Activos: ${latestKpi.clientesActivosMensuales.toLocaleString()}`);

    // Mostrar evolución (últimas 6 semanas)
    console.log('\n📈 EVOLUCIÓN DE INDICADORES (Últimas 6 semanas):');
    console.log('Semana\t\tVolumen (M L)\tCrecimiento\tMargen\t\tRetención');
    console.log('─'.repeat(80));
    
    const evolutionData = kpis.slice(0, 6).reverse();
    evolutionData.forEach(kpi => {
      const volumenM = (kpi.volumenTotalLitros / 1000000).toFixed(1);
      console.log(`S${kpi.weekNumber}/${kpi.year}\t\t${volumenM}\t\t${kpi.crecimientoMensual}%\t\t$${kpi.margenBrutoPorLitro}\t\t${kpi.tasaRetencionClientes}%`);
    });

    // Calcular estadísticas
    console.log('\n📊 ESTADÍSTICAS CALCULADAS:');
    const volumenPromedio = kpis.reduce((sum, kpi) => sum + kpi.volumenTotalLitros, 0) / kpis.length;
    const crecimientoPromedio = kpis.reduce((sum, kpi) => sum + kpi.crecimientoMensual, 0) / kpis.length;
    const retencionPromedio = kpis.reduce((sum, kpi) => sum + kpi.tasaRetencionClientes, 0) / kpis.length;
    
    console.log(`   📊 Volumen Promedio: ${volumenPromedio.toLocaleString()} litros`);
    console.log(`   📈 Crecimiento Promedio: ${crecimientoPromedio.toFixed(1)}%`);
    console.log(`   👥 Retención Promedio: ${retencionPromedio.toFixed(1)}%`);

    // Mostrar distribuciones para gráficos circulares
    console.log('\n🥧 DISTRIBUCIONES PARA GRÁFICOS:');
    console.log('Cumplimiento de Objetivos:');
    console.log(`   ✅ Cumplido: ${latestKpi.cumplimientoObjetivo}%`);
    console.log(`   ⏳ Pendiente: ${(100 - latestKpi.cumplimientoObjetivo).toFixed(1)}%`);
    
    console.log('Retención de Clientes:');
    console.log(`   👥 Retenidos: ${latestKpi.tasaRetencionClientes}%`);
    console.log(`   👋 Perdidos: ${(100 - latestKpi.tasaRetencionClientes).toFixed(1)}%`);

    // Información para desarrolladores
    console.log('\n🔧 INFORMACIÓN TÉCNICA:');
    console.log('Los datos mostrados arriba son exactamente los que se visualizan en:');
    console.log('   • Panel de Rendimiento Comercial');
    console.log('   • Gráficos de evolución de indicadores');
    console.log('   • Gráficos circulares de distribución');
    console.log('   • Tarjetas de KPIs individuales');

    console.log('\n✅ Estado del Dashboard:');
    console.log('   🟢 Datos Reales: SÍ (se mostrará indicador verde)');
    console.log(`   📅 Última Actualización: Semana ${latestKpi.weekNumber}/${latestKpi.year}`);
    console.log('   📊 Gráficos: Actualizados con datos reales');
    console.log('   🔄 Revalidación: Automática al agregar nuevos KPIs');

    console.log('\n🚀 Para ver estos datos en acción:');
    console.log('   1. Inicia sesió<NAME_EMAIL> / worker123');
    console.log('   2. Ve a /admin');
    console.log('   3. Selecciona la pestaña "Ventas"');
    console.log('   4. Observa el indicador "Datos Reales Capturados" en verde');
    console.log('   5. Los gráficos mostrarán estos datos exactos');

  } catch (error) {
    console.error('❌ Error al verificar datos del dashboard:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyDashboardData();
