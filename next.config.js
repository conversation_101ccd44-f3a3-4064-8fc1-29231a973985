/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [], // Añade aquí los dominios externos si los usas
    unoptimized: process.env.NODE_ENV === 'production',
  },
  env: {
    OSRM_API_URL: process.env.OSRM_API_URL,
  },
  eslint: {
    // Ignora los errores de ESLint durante el build
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Ignora los errores de TypeScript durante el build
    ignoreBuildErrors: true,
  },
}

module.exports = nextConfig
