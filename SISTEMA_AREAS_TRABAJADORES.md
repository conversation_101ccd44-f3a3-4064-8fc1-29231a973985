# 🏢 Sistema de Áreas para Trabajadores

## 📋 Resumen

Se ha implementado un sistema completo de áreas para usuarios WORKER que permite:

✅ **Ver todas las áreas** (acceso de lectura completo)
✅ **Descargar PDFs y CSVs** de todas las áreas  
✅ **Solo editar/agregar datos** en su área asignada
❌ **No modificar datos** de otras áreas

## 🏗️ Arquitectura del Sistema

### 1. **Base de Datos**

#### Tabla `Area`
```sql
CREATE TABLE Area (
  id          String   PRIMARY KEY
  nombre      String   UNIQUE     -- "ventas", "compras", etc.
  displayName String              -- "Ventas", "Compras", etc.
  descripcion String?             -- Descripción opcional
  activo      Boolean             -- Si está activa
  orden       Int                 -- Orden de visualización
  icono       String?             -- Nombre del icono
  color       String?             -- Color asociado
)
```

#### Tabla `User` (modificada)
```sql
ALTER TABLE User ADD COLUMN areaId String? -- Referencia al área asignada
```

### 2. **Áreas Disponibles**

| Área | Nombre | Color | Icono |
|------|--------|-------|-------|
| Ventas | ventas | #3b82f6 | ShoppingCart |
| Compras | compras | #10b981 | Package |
| Logística | logistica | #f59e0b | Truck |
| Contabilidad | contabilidad | #8b5cf6 | Calculator |
| Operaciones | operaciones | #6366f1 | Settings |
| Legal | legal | #ef4444 | Scale |
| Planeación | planeacion | #06b6d4 | Target |
| Finanzas | finanzas | #059669 | Banknote |

## 🔐 Sistema de Permisos

### **Roles y Accesos**

#### 👤 **WORKER (Trabajador)**
- ✅ **Lectura**: Puede ver TODAS las áreas
- ✅ **Descarga**: Puede descargar PDFs/CSVs de TODAS las áreas
- ✅ **Escritura**: Solo puede editar/agregar datos en SU área asignada
- ❌ **Restricción**: No puede modificar datos de otras áreas

#### 👨‍💼 **ADMIN / SUPER_ADMIN**
- ✅ **Acceso completo**: Puede ver y editar TODAS las áreas
- ✅ **Gestión**: Puede asignar/remover áreas a usuarios
- ✅ **Panel especial**: Acceso a "Gestión de Áreas"

### **Funciones de Control de Acceso**

```typescript
// Verificar acceso de lectura (WORKERS ven todo)
hasAreaReadAccess(areaName: string): Promise<boolean>

// Verificar acceso de escritura (WORKERS solo su área)
hasAreaWriteAccess(areaName: string): Promise<boolean>

// Obtener áreas que puede ver el usuario
getUserAccessibleAreas(): Promise<AreaType[]>

// Obtener áreas que puede editar el usuario
getUserWritableAreas(): Promise<AreaType[]>

// Verificar permisos completos con contexto
canEditAreaData(areaName: string): Promise<{
  canEdit: boolean;
  canView: boolean;
  userArea?: string;
  reason?: string;
}>
```

## 🎨 Componentes de UI

### 1. **UserAreaBadge**
- Muestra el área asignada del usuario
- Solo visible para usuarios WORKER
- Colores dinámicos según el área

### 2. **AreaRestrictionBanner**
- Banner informativo sobre restricciones
- Solo se muestra cuando hay limitaciones
- Explica qué puede y no puede hacer el usuario

### 3. **UserAreaManagement**
- Panel de administración para gestionar áreas
- Solo visible para ADMIN/SUPER_ADMIN
- Permite asignar/remover áreas a usuarios WORKER

## 🔧 Implementación en el Dashboard

### **Modificaciones Realizadas**

#### AdminDashboard.tsx
- ✅ Verificación de permisos por área activa
- ✅ Botones de edición deshabilitados según permisos
- ✅ Banner de restricción en cada área
- ✅ Badge de área en el sidebar

#### AdminDashboardCompras.tsx
- ✅ Banner de restricción
- ✅ Botones de edición con permisos
- ✅ Verificación de acceso de escritura

### **Comportamiento de Botones**

```typescript
// Botón habilitado solo si puede editar
<button
  onClick={handleNewKpi}
  disabled={!canEditCurrentArea}
  className={canEditCurrentArea 
    ? "bg-green-600 hover:bg-green-700" 
    : "bg-gray-400 cursor-not-allowed"
  }
  title={canEditCurrentArea 
    ? "Agregar Datos" 
    : "No puedes agregar datos en esta área"
  }
>
```

## 👥 Usuarios de Prueba

### **Trabajadores por Área**
```
Ana García: <EMAIL> / ventas123 (Ventas)
Carlos Rodríguez: <EMAIL> / compras123 (Compras)
Usuario Logística: <EMAIL> / logistica123 (Logística)
María López: <EMAIL> / contabilidad123 (Contabilidad)
José Martínez: <EMAIL> / operaciones123 (Operaciones)
Laura Sánchez: <EMAIL> / legal123 (Legal)
Roberto Fernández: <EMAIL> / planeacion123 (Planeación)
Patricia Morales: <EMAIL> / finanzas123 (Finanzas)
```

### **Administrador**
```
Rodrigo: <EMAIL> (ADMIN)
```

## 🛠️ Scripts de Gestión

### **Poblar Áreas**
```bash
node scripts/seed-areas.js
```

### **Crear Usuarios de Prueba**
```bash
node scripts/create-demo-workers.js
```

### **Asignar Áreas**
```bash
# Asignación automática
node scripts/assign-worker-areas.js

# Asignación específica
node scripts/assign-worker-areas.js <EMAIL> nombre_area
```

### **Probar Sistema**
```bash
node scripts/test-area-permissions.js
```

## 🎯 Casos de Uso

### **Escenario 1: Worker de Ventas**
1. Se loguea con `<EMAIL>`
2. Ve su badge "Ventas" en el sidebar
3. Puede navegar a TODAS las áreas
4. Solo puede agregar/editar datos en "Ventas"
5. Los botones de edición están deshabilitados en otras áreas
6. Ve banner informativo sobre restricciones
7. Puede descargar PDFs/CSVs de todas las áreas

### **Escenario 2: Administrador**
1. Se loguea como ADMIN
2. Ve todas las áreas sin restricciones
3. Puede editar datos en cualquier área
4. Tiene acceso a "Gestión de Áreas"
5. Puede asignar/remover áreas a trabajadores

## 🔄 Flujo de Permisos

```mermaid
graph TD
    A[Usuario se loguea] --> B{¿Qué rol tiene?}
    B -->|WORKER| C[Verificar área asignada]
    B -->|ADMIN/SUPER_ADMIN| D[Acceso completo]
    
    C --> E{¿Tiene área?}
    E -->|Sí| F[Puede editar solo su área]
    E -->|No| G[Solo lectura en todas]
    
    F --> H[Ver todas las áreas]
    G --> H
    D --> H
    
    H --> I[Dashboard con permisos aplicados]
```

## ✅ Estado Actual

- ✅ Base de datos configurada
- ✅ 8 áreas creadas y pobladas
- ✅ 8 usuarios WORKER con áreas asignadas
- ✅ Sistema de permisos implementado
- ✅ UI actualizada con restricciones
- ✅ Banners informativos
- ✅ Botones con permisos
- ✅ Scripts de gestión
- ✅ Documentación completa

## 🚀 Próximos Pasos Opcionales

1. **Filtros por área**: Filtrar datos automáticamente por área del usuario
2. **Notificaciones**: Sistema de notificaciones cuando se cambia de área
3. **Reportes por área**: Reportes específicos por área
4. **Auditoría**: Log de cambios por área y usuario
5. **Jerarquías**: Sub-áreas o departamentos dentro de áreas

El sistema está **completamente funcional** y listo para producción! 🎉
