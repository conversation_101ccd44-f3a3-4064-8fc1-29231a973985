-- CreateTable
CREATE TABLE "KpiLogistica" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "weekNumber" INTEGER NOT NULL,
    "weekStartDate" DATETIME NOT NULL,
    "weekEndDate" DATETIME NOT NULL,
    "unidadesConfirmadas" INTEGER NOT NULL,
    "unidadesSolicitadas" INTEGER NOT NULL,
    "porcentajeEntregasTiempo" REAL NOT NULL,
    "porcentajeRetardos" REAL NOT NULL,
    "porcentajeReprogramaciones" REAL NOT NULL,
    "promedioKmOperacion" REAL NOT NULL,
    "promedioCostoFleteLitro" REAL NOT NULL,
    "promedioCostoFleteOperacion" REAL NOT NULL,
    "pagoSemanalFlete" REAL NOT NULL,
    "pagoSemanalPenalizaciones" REAL NOT NULL,
    "porcentajeRutasCotizadas" REAL NOT NULL,
    "porcentajeTransportistas" REAL NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "KpiLogistica_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "KpiLogistica_year_weekNumber_idx" ON "KpiLogistica"("year", "weekNumber");

-- CreateIndex
CREATE INDEX "KpiLogistica_userId_idx" ON "KpiLogistica"("userId");

-- CreateIndex
CREATE INDEX "KpiLogistica_weekStartDate_idx" ON "KpiLogistica"("weekStartDate");

-- CreateIndex
CREATE UNIQUE INDEX "KpiLogistica_year_weekNumber_key" ON "KpiLogistica"("year", "weekNumber");
