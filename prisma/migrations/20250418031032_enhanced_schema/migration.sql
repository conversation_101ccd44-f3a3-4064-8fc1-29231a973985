/*
  Warnings:

  - You are about to drop the column `terminal` on the `Precio` table. All the data in the column will be lost.
  - Added the required column `terminalId` to the `Precio` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `Precio` table without a default value. This is not possible if the table is not empty.

*/
-- CreateTable
CREATE TABLE "Terminal" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "nombre" TEXT NOT NULL,
    "activo" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Precio" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "producto" TEXT NOT NULL,
    "terminalId" TEXT NOT NULL,
    "fecha" DATETIME NOT NULL,
    "valor" REAL NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Precio_terminalId_fkey" FOREIGN KEY ("terminalId") REFERENCES "Terminal" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_Precio" ("createdAt", "fecha", "id", "producto", "valor") SELECT "createdAt", "fecha", "id", "producto", "valor" FROM "Precio";
DROP TABLE "Precio";
ALTER TABLE "new_Precio" RENAME TO "Precio";
CREATE INDEX "Precio_producto_terminalId_fecha_idx" ON "Precio"("producto", "terminalId", "fecha");
CREATE INDEX "Precio_fecha_idx" ON "Precio"("fecha");
CREATE UNIQUE INDEX "Precio_producto_terminalId_fecha_key" ON "Precio"("producto", "terminalId", "fecha");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE UNIQUE INDEX "Terminal_nombre_key" ON "Terminal"("nombre");

-- CreateIndex
CREATE INDEX "Terminal_nombre_idx" ON "Terminal"("nombre");
