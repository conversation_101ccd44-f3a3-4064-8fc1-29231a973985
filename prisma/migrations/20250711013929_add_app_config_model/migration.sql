/*
  Warnings:

  - You are about to drop the `K<PERSON>ComprasSemanal` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "KpiComprasSemanal";
PRAGMA foreign_keys=on;

-- CreateTable
CREATE TABLE "KpiCompras" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "weekNumber" INTEGER NOT NULL,
    "weekStartDate" DATETIME NOT NULL,
    "weekEndDate" DATETIME NOT NULL,
    "numeroProveedoresActivos" INTEGER NOT NULL,
    "porcentajeReporteGanancia" REAL NOT NULL,
    "preciosPromedioCompra" REAL NOT NULL,
    "diferencialPrecioPemex" REAL NOT NULL,
    "distribucionProveedores" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "KpiCompras_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "AppConfig" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "description" TEXT,
    "userId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "AppConfig_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "KpiCompras_year_weekNumber_idx" ON "KpiCompras"("year", "weekNumber");

-- CreateIndex
CREATE INDEX "KpiCompras_userId_idx" ON "KpiCompras"("userId");

-- CreateIndex
CREATE INDEX "KpiCompras_weekStartDate_idx" ON "KpiCompras"("weekStartDate");

-- CreateIndex
CREATE UNIQUE INDEX "KpiCompras_year_weekNumber_key" ON "KpiCompras"("year", "weekNumber");

-- CreateIndex
CREATE UNIQUE INDEX "AppConfig_key_key" ON "AppConfig"("key");

-- CreateIndex
CREATE INDEX "AppConfig_key_idx" ON "AppConfig"("key");

-- CreateIndex
CREATE INDEX "AppConfig_userId_idx" ON "AppConfig"("userId");
