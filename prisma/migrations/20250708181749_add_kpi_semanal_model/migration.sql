-- CreateTable
CREATE TABLE "KpiSemanal" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "weekNumber" INTEGER NOT NULL,
    "weekStartDate" DATETIME NOT NULL,
    "weekEndDate" DATETIME NOT NULL,
    "volumenTotalLitros" REAL NOT NULL,
    "crecimientoMensual" REAL NOT NULL,
    "margenBrutoPorLitro" REAL NOT NULL,
    "tasaRetencionClientes" REAL NOT NULL,
    "cumplimientoObjetivo" REAL NOT NULL,
    "desviacionVentas" REAL NOT NULL,
    "cicloPromedioCierre" INTEGER NOT NULL,
    "clientesActivosMensuales" INTEGER NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "KpiSemanal_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "KpiSemanal_year_weekNumber_idx" ON "KpiSemanal"("year", "weekNumber");

-- CreateIndex
CREATE INDEX "KpiSemanal_userId_idx" ON "KpiSemanal"("userId");

-- CreateIndex
CREATE INDEX "KpiSemanal_weekStartDate_idx" ON "KpiSemanal"("weekStartDate");

-- CreateIndex
CREATE UNIQUE INDEX "KpiSemanal_year_weekNumber_key" ON "KpiSemanal"("year", "weekNumber");
