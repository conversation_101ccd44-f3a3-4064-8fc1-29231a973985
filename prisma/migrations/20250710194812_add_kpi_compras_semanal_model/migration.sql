-- CreateTable
CREATE TABLE "KpiComprasSemanal" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "weekNumber" INTEGER NOT NULL,
    "weekStartDate" DATETIME NOT NULL,
    "weekEndDate" DATETIME NOT NULL,
    "numeroProveedoresActivos" INTEGER NOT NULL,
    "porcentajeReporteGanancia" REAL NOT NULL,
    "preciosPromediosCompra" REAL NOT NULL,
    "diferencialPrecioCompraTerminal" REAL NOT NULL,
    "porcentajeCompraPorProveedor" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "KpiComprasSemanal_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "KpiComprasSemanal_year_weekNumber_idx" ON "KpiComprasSemanal"("year", "weekNumber");

-- CreateIndex
CREATE INDEX "KpiComprasSemanal_userId_idx" ON "KpiComprasSemanal"("userId");

-- CreateIndex
CREATE INDEX "KpiComprasSemanal_weekStartDate_idx" ON "KpiComprasSemanal"("weekStartDate");

-- CreateIndex
CREATE UNIQUE INDEX "KpiComprasSemanal_year_weekNumber_key" ON "KpiComprasSemanal"("year", "weekNumber");
